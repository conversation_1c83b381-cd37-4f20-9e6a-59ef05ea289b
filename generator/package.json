{"private": true, "name": "@webiny/docs-generator", "version": "0.0.0", "sideEffects": false, "main": "./src/index.js", "exports": {"default": "./src/index.ts"}, "scripts": {"test": "jest --config=jest.config.js src/**/*.test.ts"}, "dependencies": {"@babel/parser": "^7.23.3", "chalk": "^4.1.0", "debounce": "^1.2.0", "escape-html": "^1.0.3", "execa": "^5.1.1", "fs-extra": "^9.1.0", "globby": "^11.1.0", "jsdom": "^20.0.0", "load-json-file": "^6.2.0", "md5": "^2.3.0", "react": "^17.0.2", "react-dom": "^17.0.2", "unist-util-visit": "^2.0.3", "write-json-file": "^4.3.0", "yargs": "^17.3.1"}, "devDependencies": {"@types/debounce": "^1.2.3", "@types/escape-html": "^1.0.4", "@types/fs-extra": "^9.0.13", "@types/jest": "^29.5.4", "@types/md5": "^2.3.4", "@types/yargs": "^17.0.8", "jest": "^29.6.4", "ts-jest": "^29.1.1", "ts-node": "^10.5.0", "typescript": "^4.7.4"}}