## Short Description
<!--- Shortly describe what this PR introduces. -->
<!--- For help on writing docs, visit https://docs.webiny.com/docs/contributing/documentation -->

## Relevant Links
<!--- If possible, please include the URLs of the newly added or edited pages (wait for the Netlify preview to be deployed and then paste the links). -->
- [A change on X page](#)
- [Update list of libraries](#)
- [A new diagram with updated resources](#)

## Checklist
- [ ] I added page metadata (description, keywords)
- [ ] I've added "Can I Use This?" section (if needed, e.g. if documenting a new feature)
- [ ] I added `What You'll Learn` at the top of the page and every item in the list starts with a lower case letter
- [ ] I used title case for titles and subtitles (in the main menu and in the page content)
- [ ] I checked for typos and grammar mistakes
- [ ] I added `alt` / `title` attributes for inserted images (if any)
- [ ] When linking code from GitHub, I did it via a specific tag (and not `next` / `v5` branch) 

<!--- Resources:
- new document template: https://docs.webiny.com/docs/contributing/documentation#template-for-new-docs
- "What You'll Learn" example: https://docs.webiny.com/docs/how-to-guides/upgrade-webiny
- example of using title-case correctly: https://docs.webiny.com/docs/key-topics/deployment/iac-with-pulumi
- for title case checks - https://titlecaseconverter.com
- for typos and grammar checks - https://www.grammarly.com
-->

## Screenshots (if relevant):
N/A
