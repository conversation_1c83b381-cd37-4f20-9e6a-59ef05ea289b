{
  "compilerOptions": {
    "target": "esnext",
    "allowJs": true,
    "forceConsistentCasingInFileNames": true,
    "allowSyntheticDefaultImports": true,
    "moduleResolution": "node",
    "module": "esnext",
    "lib": ["esnext"],
    "esModuleInterop": true,
    "declaration": true,
    "composite": true,
    "noEmit": false,
    "jsx": "react-jsx",
    "emitDeclarationOnly": true,
    "resolveJsonModule": true,
    "baseUrl": ".",
    "plugins": [
      {
        "transform": "typescript-transform-paths",
        "afterDeclarations": true
      }
    ],
    "noUnusedParameters": false,
    "noUnusedLocals": false,
    "noImplicitUseStrict": false,
    "noImplicitThis": true,
    "noImplicitReturns": true,
    "noImplicitAny": true,
    "noUncheckedIndexedAccess": false,
    "noStrictGenericChecks": false,
    "noFallthroughCasesInSwitch": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitOverride": true,
    "strict": true,
    "noPropertyAccessFromIndexSignature": false,
    "suppressImplicitAnyIndexErrors": false,
    "suppressExcessPropertyErrors": false,
    "keyofStringsOnly": false,
    "experimentalDecorators": true,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,
    /**
     * Maybe switch to true at some point? There would need to be A LOT of error handling changes.
     * https://www.typescriptlang.org/tsconfig#useUnknownInCatchVariables
     */
    "useUnknownInCatchVariables": false,
    /**
     * Setting to true will start producing TS errors that are inside libraries we use.
     * https://www.typescriptlang.org/tsconfig#exactOptionalPropertyTypes
     */
    "exactOptionalPropertyTypes": false
  },
  "exclude": ["node_modules"]
}
