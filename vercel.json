{"redirects": [{"source": "/", "destination": "/docs/get-started/welcome", "permanent": true}, {"source": "/docs", "destination": "/docs/get-started/welcome", "permanent": true}, {"source": "/docs/user-guides", "destination": "/docs/user-guides/overview", "permanent": true}, {"source": "/docs/webiny/introduction", "destination": "/docs/get-started/welcome", "permanent": true}, {"source": "/docs/webiny-overview/serverless-cms/apps/page-builder", "destination": "/docs/overview/applications/page-builder", "permanent": true}, {"source": "/docs/webiny-overview/serverless-cms/apps/headless-cms", "destination": "/docs/overview/applications/headless-cms", "permanent": true}, {"source": "/docs/webiny-overview/serverless-cms/apps/form-builder", "destination": "/docs/overview/applications/form-builder", "permanent": true}, {"source": "/docs/webiny-overview/serverless-cms/apps/file-manager", "destination": "/docs/overview/applications/file-manager", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/themes/page-builder/introduction", "destination": "/docs/page-builder/theming/introduction", "permanent": true}, {"source": "/docs/docs/webiny-overview/security", "destination": "/docs/overview/security", "permanent": true}, {"source": "/docs/webiny-overview/serverless-cms/apps/intro", "destination": "/docs/overview/introduction", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/themes/page-builder/introduction", "destination": "/docs/page-builder/theming/introduction", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/themes/page-builder/colors", "destination": "/docs/page-builder/theming/colors", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/themes/page-builder/elements", "destination": "/docs/page-builder/theming/elements", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/themes/page-builder/layouts", "destination": "/docs/page-builder/theming/layouts", "permanent": true}, {"source": "/docs/tutorials/page-builder/create-a-new-page-element", "destination": "/docs/page-builder/extending/create-a-page-element", "permanent": true}, {"source": "/docs/tutorials/page-builder/customize-an-existing-page-builder-element", "destination": "/docs/page-builder/extending/customize-an-existing-element", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/page-builder/extend-graphql-api", "destination": "/docs/page-builder/extending/extend-graphql-api", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/page-builder/extend-page-settings", "destination": "/docs/page-builder/extending/extend-page-settings", "permanent": true}, {"source": "/docs/key-topics/page-builder/lifecycle-events", "destination": "/docs/page-builder/references/lifecycle-events", "permanent": true}, {"source": "/docs/enterprise/multi-tenancy/setup", "destination": "/docs/enterprise/multi-tenancy", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.13.0-to-5.14.0", "destination": "/docs/release-notes/5.14.0/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.12.0-to-5.13.0", "destination": "/docs/release-notes/5.13.0/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.11.1-to-5.12.0", "destination": "/docs/release-notes/5.12.0/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.11.0-to-5.11.1", "destination": "/docs/release-notes/5.11.1/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.10.0-to-5.11.0", "destination": "/docs/release-notes/5.11.0/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.9.0-to-5.10.0", "destination": "/docs/release-notes/5.10.0/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.8.0-to-5.9.0", "destination": "/docs/release-notes/5.9.0/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.7.0-to-5.8.0", "destination": "/docs/release-notes/5.8.0/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.6.0-to-5.7.0", "destination": "/docs/release-notes/5.7.0/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.5.0-to-5.6.0", "destination": "/docs/release-notes/5.6.0/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.4.0-to-5.5.0", "destination": "/docs/release-notes/5.5.0/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.3.0-to-5.4.0", "destination": "/docs/release-notes/5.4.0/upgrade-guide", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny/5.0.0-to-5.1.0", "destination": "/docs/release-notes/5.1.0/upgrade-guide", "permanent": true}, {"source": "/docs/changelog/5.14.0", "destination": "/docs/release-notes/5.14.0/changelog", "permanent": true}, {"source": "/docs/changelog/5.13.0", "destination": "/docs/release-notes/5.13.0/changelog", "permanent": true}, {"source": "/docs/changelog/v5.12.0", "destination": "/docs/release-notes/5.12.0/changelog", "permanent": true}, {"source": "/docs/changelog/5.11.1", "destination": "/docs/release-notes/5.11.1/changelog", "permanent": true}, {"source": "/docs/changelog/5.11.0", "destination": "/docs/release-notes/5.11.0/changelog", "permanent": true}, {"source": "/docs/changelog/5.10.0", "destination": "/docs/release-notes/5.10.0/changelog", "permanent": true}, {"source": "/docs/changelog/5.9.0", "destination": "/docs/release-notes/5.9.0/changelog", "permanent": true}, {"source": "/docs/changelog/5.8.0", "destination": "/docs/release-notes/5.8.0/changelog", "permanent": true}, {"source": "/docs/changelog/5.7.0", "destination": "/docs/release-notes/5.7.0/changelog", "permanent": true}, {"source": "/docs/changelog/5.6.0", "destination": "/docs/release-notes/5.6.0/changelog", "permanent": true}, {"source": "/docs/changelog/5.5.0", "destination": "/docs/release-notes/5.5.0/changelog", "permanent": true}, {"source": "/docs/changelog/5.4.0", "destination": "/docs/release-notes/5.4.0/changelog", "permanent": true}, {"source": "/docs/changelog/5.3.0", "destination": "/docs/release-notes/5.3.0/changelog", "permanent": true}, {"source": "/docs/tutorials/page-builder/create-a-new-page-builder-element", "destination": "/docs/tutorials/page-builder/create-a-new-page-element", "permanent": true}, {"source": "/docs/how-to-guides/development/environment-variables", "destination": "/docs/how-to-guides/environment-variables", "permanent": true}, {"source": "/docs/how-to-guides/development/integrations/integrate-tailwindcss", "destination": "/docs/how-to-guides/integrations/integrate-tailwindcss", "permanent": true}, {"source": "/docs/how-to-guides/webiny-cli/use-watch-command", "destination": "/docs/how-to-guides/use-watch-command", "permanent": true}, {"source": "/docs/how-to-guides/webiny-cli/scaffolding/introduction", "destination": "/docs/how-to-guides/scaffolding/introduction", "permanent": true}, {"source": "/docs/how-to-guides/webiny-cli/scaffolding/extend-admin-area", "destination": "/docs/how-to-guides/scaffolding/extend-admin-area", "permanent": true}, {"source": "/docs/how-to-guides/webiny-cli/scaffolding/extend-graphql-api", "destination": "/docs/how-to-guides/scaffolding/extend-graphql-api", "permanent": true}, {"source": "/docs/how-to-guides/webiny-cli/scaffolding/ci-cd", "destination": "/docs/how-to-guides/scaffolding/ci-cd", "permanent": true}, {"source": "/docs/how-to-guides/webiny-cli/scaffolding/react-application/webiny-config", "destination": "/docs/how-to-guides/webiny-cli/scaffolding/react-application#webiny-config", "permanent": true}, {"source": "/docs/tutorials/create-an-application/introduction", "destination": "/docs/tutorials/extend-admin-area/introduction", "permanent": true}, {"source": "/docs/tutorials/create-an-application/api-package", "destination": "/docs/tutorials/extend-admin-area/introduction", "permanent": true}, {"source": "/docs/tutorials/create-an-application/admin-area-package", "destination": "/docs/tutorials/extend-admin-area/introduction", "permanent": true}, {"source": "/docs/key-topics/ci-cd/environments/staging-prod-deployments", "destination": "/docs/key-topics/ci-cd/environments/#except-my-application-code-should-the-staging-and-prod-infrastructure-also-be-the-same", "permanent": true}, {"source": "/docs/key-topics/ci-cd/testing/slow-ephemeral-environments", "destination": "/docs/key-topics/ci-cd/testing/#deploying-ephemeral-environments-takes-a-long-time-finish-what-can-i-do-about-it", "permanent": true}, {"source": "/docs/how-to-guides/development/use-watch-command", "destination": "/docs/how-to-guides/webiny-cli/use-watch-command", "permanent": true}, {"source": "/docs/how-to-guides/headless-cms/create-a-webiny-headless-cms-field-plugin", "destination": "/docs/how-to-guides/webiny-applications/headless-cms/create-a-webiny-headless-cms-field-plugin", "permanent": true}, {"source": "/docs/how-to-guides/headless-cms/create-a-content-model-form-layout", "destination": "/docs/how-to-guides/webiny-applications/headless-cms/create-a-content-model-form-layout", "permanent": true}, {"source": "/docs/how-to-guides/headless-cms/using-graphql-api", "destination": "/docs/how-to-guides/webiny-applications/headless-cms/using-graphql-api", "permanent": true}, {"source": "/docs/how-to-guides/headless-cms/rendering-rich-text", "destination": "/docs/how-to-guides/webiny-applications/headless-cms/rendering-rich-text", "permanent": true}, {"source": "/docs/key-topics/webiny-applications/themes/introduction", "destination": "/docs/how-to-guides/webiny-applications/themes/introduction", "permanent": true}, {"source": "/docs/key-topics/webiny-applications/themes/page-builder/introduction", "destination": "/docs/how-to-guides/webiny-applications/themes/page-builder/introduction", "permanent": true}, {"source": "/docs/key-topics/webiny-applications/themes/page-builder/colors", "destination": "/docs/how-to-guides/webiny-applications/themes/page-builder/colors", "permanent": true}, {"source": "/docs/key-topics/webiny-applications/themes/page-builder/elements", "destination": "/docs/how-to-guides/webiny-applications/themes/page-builder/elements", "permanent": true}, {"source": "/docs/key-topics/webiny-applications/themes/page-builder/layouts", "destination": "/docs/how-to-guides/webiny-applications/themes/page-builder/layouts", "permanent": true}, {"source": "/docs/key-topics/webiny-applications/themes/form-builder/introduction", "destination": "/docs/how-to-guides/webiny-applications/themes/form-builder/introduction", "permanent": true}, {"source": "/docs/key-topics/webiny-applications/themes/form-builder/layouts", "destination": "/docs/how-to-guides/webiny-applications/themes/form-builder/layouts", "permanent": true}, {"source": "/docs/key-topics/webiny-applications/headless-cms/graphql-api", "destination": "/docs/headless-cms/basics/graphql-api", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/headless-cms/using-graphql-api", "destination": "/docs/headless-cms/basics/using-graphql-api", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/headless-cms/extend-graphql-api", "destination": "/docs/headless-cms/extending/extend-graphql-api", "permanent": true}, {"source": "/docs/tutorials/headless-cms/create-a-webiny-headless-cms-custom-field-plugin", "destination": "/docs/headless-cms/extending/custom-field-type", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/headless-cms/content-model-plugins", "destination": "/docs/headless-cms/extending/define-content-models-via-code", "permanent": true}, {"source": "/docs/key-topics/headless-cms/lifecycle-events", "destination": "/docs/headless-cms/references/lifecycle-events", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/headless-cms/create-a-content-model-form-layout", "destination": "/docs/headless-cms/extending/customize-entry-form-layout", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/headless-cms/rendering-rich-text", "destination": "/docs/headless-cms/extending/render-rich-text-content", "permanent": true}, {"source": "/docs/serverless-cms/headless-cms/plugins/storage-transform-plugin", "destination": "/docs/headless-cms/references/storage-transform-plugin", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/themes/form-builder/introduction", "destination": "/docs/form-builder/theming/introduction", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/themes/form-builder/layouts", "destination": "/docs/form-builder/theming/layouts", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/file-manager/create-a-file-type-plugin", "destination": "/docs/file-manager/extending/create-a-file-type-plugin", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/admin-area/framework", "destination": "/docs/admin-area/basics/framework", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/admin-area/api-playground", "destination": "/docs/admin-area/basics/api-playground", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/admin-area/change-logo", "destination": "/docs/admin-area/extending/change-logo", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/admin-area/change-logo", "destination": "/docs/admin-area/extending/change-logo", "permanent": true}, {"source": "/docs/tutorials/extend-admin-area/introduction", "destination": "/docs/admin-area/new-app-tutorial/introduction", "permanent": true}, {"source": "/docs/how-to-guides/scaffolding/extend-admin-area", "destination": "/docs/admin-area/new-app-tutorial/scaffolding", "permanent": true}, {"source": "/docs/tutorials/extend-admin-area/getting-started", "destination": "/docs/admin-area/new-app-tutorial/get-started", "permanent": true}, {"source": "/docs/tutorials/extend-admin-area/extending-entities", "destination": "/docs/admin-area/new-app-tutorial/extending-entities", "permanent": true}, {"source": "/docs/tutorials/extend-admin-area/security/introduction", "destination": "/docs/admin-area/new-app-tutorial/security", "permanent": true}, {"source": "/docs/tutorials/extend-admin-area/security/graphql-api", "destination": "/docs/admin-area/new-app-tutorial/securing-graphql-api", "permanent": true}, {"source": "/docs/tutorials/extend-admin-area/security/admin-area", "destination": "/docs/admin-area/new-app-tutorial/securing-client-app", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/introduction", "destination": "/docs/custom-app-tutorial/overview/introduction", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/getting-started", "destination": "/docs/custom-app-tutorial/overview/get-started", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/graphql-api", "destination": "/docs/custom-app-tutorial/graphql-api/create-a-graphql-api", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/react-application/introduction", "destination": "/docs/custom-app-tutorial/react-application/introduction", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/react-application/layout", "destination": "/docs/custom-app-tutorial/react-application/layout", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/react-application/new-pin-modal-dialog", "destination": "/docs/custom-app-tutorial/react-application/new-pin-modal-dialog", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/react-application/homepage", "destination": "/docs/custom-app-tutorial/react-application/homepage", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/react-application/pin-details-page", "destination": "/docs/custom-app-tutorial/react-application/pin-details-page", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/security/introduction", "destination": "/docs/custom-app-tutorial/security-overview/introduction", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/security/getting-started", "destination": "/docs/custom-app-tutorial/security-overview/get-started", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/security/cloud-infrastructure/adding-user-pool-and-user-pool-domain", "destination": "/docs/custom-app-tutorial/adding-user-pools/adding-user-pool-and-user-pool-domain", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/security/cloud-infrastructure/adding-user-pool-client", "destination": "/docs/custom-app-tutorial/adding-user-pools/adding-user-pool-client", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/security/cloud-infrastructure/adjusting-webiny-config-ts-configuration-file", "destination": "/docs/custom-app-tutorial/adding-user-pools/adjusting-webiny-config-ts-configuration-file", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/security/react-application/initial-setup", "destination": "/docs/custom-app-tutorial/securing-react-application/initial-setup", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/security/react-application/integrating-hosted-ui-authentication-flow", "destination": "/docs/custom-app-tutorial/securing-react-application/integrating-hosted-ui-authentication-flow", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/security/graphql-api/initial-setup", "destination": "/docs/custom-app-tutorial/securing-graphql-api/initial-setup", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/security/graphql-api/implementing-authentication-and-authorization-checks", "destination": "/docs/custom-app-tutorial/securing-graphql-api/implementing-authentication-and-authorization-checks", "permanent": true}, {"source": "/docs/tutorials/create-custom-application/security/wrapping-it-up", "destination": "/docs/custom-app-tutorial/wrapping-it-up/wrapping-it-up", "permanent": true}, {"source": "/docs/key-topics/plugins", "destination": "/docs/core-development-concepts/basics/plugins", "permanent": true}, {"source": "/docs/core-development-concepts/basics/plugins", "destination": "/docs/core-development-concepts/basics/extensions", "permanent": true}, {"source": "/docs/key-topics/tools-libraries", "destination": "/docs/core-development-concepts/basics/tools-and-libraries", "permanent": true}, {"source": "/docs/how-to-guides/importing-plugins", "destination": "/docs/core-development-concepts/basics/importing-plugins", "permanent": true}, {"source": "/docs/core-development-concepts/basics/importing-plugins", "destination": "/docs/core-development-concepts/basics/extensions", "permanent": true}, {"source": "/docs/how-to-guides/extend-graphql-api", "destination": "/docs/core-development-concepts/extending-and-customizing/extend-graphql-api", "permanent": true}, {"source": "/docs/key-topics/webiny-cli", "destination": "/docs/core-development-concepts/basics/webiny-cli", "permanent": true}, {"source": "/docs/how-to-guides/deployment/deploy-your-project", "destination": "/docs/core-development-concepts/basics/project-deployment", "permanent": true}, {"source": "/docs/how-to-guides/use-watch-command", "destination": "/docs/core-development-concepts/basics/watch-command", "permanent": true}, {"source": "/docs/key-topics/project-organization/project-applications", "destination": "/docs/core-development-concepts/project-organization/project-applications", "permanent": true}, {"source": "/docs/key-topics/project-organization/project-applications-and-packages", "destination": "/docs/core-development-concepts/project-organization/project-applications-and-packages", "permanent": true}, {"source": "/docs/key-topics/project-organization/monorepo-organization", "destination": "/docs/core-development-concepts/project-organization/monorepo-organization", "permanent": true}, {"source": "/docs/how-to-guides/scaffolding/introduction", "destination": "/docs/core-development-concepts/scaffolding/introduction", "permanent": true}, {"source": "/docs/how-to-guides/scaffolding/full-stack-application", "destination": "/docs/core-development-concepts/scaffolding/full-stack-application", "permanent": true}, {"source": "/docs/how-to-guides/scaffolding/graphql-api", "destination": "/docs/core-development-concepts/scaffolding/graphql-api", "permanent": true}, {"source": "/docs/how-to-guides/scaffolding/extend-graphql-api", "destination": "/docs/core-development-concepts/scaffolding/extend-graphql-api", "permanent": true}, {"source": "/docs/how-to-guides/scaffolding/react-application", "destination": "/docs/core-development-concepts/scaffolding/react-application", "permanent": true}, {"source": "/docs/tutorials/webiny-cli/adding-custom-commands", "destination": "/docs/core-development-concepts/extending-and-customizing/adding-custom-cli-commands", "permanent": true}, {"source": "/docs/tutorials/create-a-package-in-webiny-project", "destination": "/docs/core-development-concepts/extending-and-customizing/create-a-package-in-webiny-project", "permanent": true}, {"source": "/docs/how-to-guides/integrations/integrate-tailwindcss", "destination": "/docs/core-development-concepts/extending-and-customizing/integrate-tailwindcss", "permanent": true}, {"source": "/docs/how-to-guides/environment-variables", "destination": "/docs/core-development-concepts/basics/environment-variables", "permanent": true}, {"source": "/docs/key-topics/ci-cd/introduction", "destination": "/docs/core-development-concepts/ci-cd/introduction", "permanent": true}, {"source": "/docs/key-topics/ci-cd/environments", "destination": "/docs/core-development-concepts/ci-cd/environments", "permanent": true}, {"source": "/docs/key-topics/ci-cd/version-control", "destination": "/docs/core-development-concepts/ci-cd/version-control", "permanent": true}, {"source": "/docs/key-topics/ci-cd/cloud-infrastructure-state-files", "destination": "/docs/core-development-concepts/ci-cd/cloud-infrastructure-state-files", "permanent": true}, {"source": "/docs/key-topics/ci-cd/testing", "destination": "/docs/core-development-concepts/ci-cd/testing", "permanent": true}, {"source": "/docs/key-topics/ci-cd/workflows", "destination": "/docs/core-development-concepts/ci-cd/workflows", "permanent": true}, {"source": "/docs/how-to-guides/scaffolding/ci-cd", "destination": "/docs/core-development-concepts/ci-cd/setup", "permanent": true}, {"source": "/docs/key-topics/deployment/introduction", "destination": "/docs/infrastructure/basics/introduction", "permanent": true}, {"source": "/docs/key-topics/deployment/environments", "destination": "/docs/infrastructure/basics/environments", "permanent": true}, {"source": "/docs/how-to-guides/deployment/preview-deployments", "destination": "/docs/infrastructure/basics/preview-deployments", "permanent": true}, {"source": "/docs/how-to-guides/deployment/destroy-cloud-infrastructure", "destination": "/docs/infrastructure/basics/destroy-cloud-infrastructure", "permanent": true}, {"source": "/docs/how-to-guides/deployment/build-and-deploy-hooks", "destination": "/docs/infrastructure/basics/build-and-deploy-hooks", "permanent": true}, {"source": "/docs/key-topics/deployment/iac-with-pulumi", "destination": "/docs/infrastructure/pulumi-iac/iac-with-pulumi", "permanent": true}, {"source": "/docs/how-to-guides/deployment/execute-pulumi-commands", "destination": "/docs/infrastructure/pulumi-iac/execute-pulumi-commands", "permanent": true}, {"source": "/docs/how-to-guides/deployment/aws/configure-aws-credentials", "destination": "/docs/infrastructure/aws/configure-aws-credentials", "permanent": true}, {"source": "/docs/how-to-guides/deployment/aws/use-aws-profiles", "destination": "/docs/infrastructure/aws/use-aws-profiles", "permanent": true}, {"source": "/docs/how-to-guides/deployment/connect-custom-domain", "destination": "/docs/infrastructure/additional-resources/connect-custom-domain", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/introduction", "destination": "/docs/architecture/introduction", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/api/introduction", "destination": "/docs/architecture/api/introduction", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/api/overview", "destination": "/docs/architecture/api/overview", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/api/graphql-requests", "destination": "/docs/architecture/api/graphql-requests", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/api/file-upload", "destination": "/docs/architecture/api/file-upload", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/api/file-download", "destination": "/docs/architecture/api/file-download", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/api/default-vpc", "destination": "/docs/architecture/api/default-vpc", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/api/custom-vpc", "destination": "/docs/architecture/api/custom-vpc", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/admin/introduction", "destination": "/docs/architecture/admin/introduction", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/admin/overview", "destination": "/docs/architecture/admin/overview", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/admin/serving-application-files", "destination": "/docs/architecture/admin/serving-application-files", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/website/introduction", "destination": "/docs/architecture/website/introduction", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/website/overview", "destination": "/docs/architecture/website/overview", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/website/prerendering-pages", "destination": "/docs/architecture/website/prerendering-pages", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/website/serving-pages", "destination": "/docs/architecture/website/serving-pages", "permanent": true}, {"source": "/docs/key-topics/security-framework/introduction", "destination": "/docs/core-development-concepts/security-framework/introduction", "permanent": true}, {"source": "/docs/key-topics/security-framework/api-security", "destination": "/docs/core-development-concepts/security-framework/api-security", "permanent": true}, {"source": "/docs/key-topics/security-framework/react-security", "destination": "/docs/core-development-concepts/security-framework/react-security", "permanent": true}, {"source": "/docs/webiny-overview/performance-benchmark/introduction", "destination": "/docs/performance-and-load-benchmark/introduction", "permanent": true}, {"source": "/docs/webiny-overview/performance-benchmark/headless-cmswrite-benchmark", "destination": "/docs/performance-and-load-benchmark/headless-cms/write-benchmark", "permanent": true}, {"source": "/docs/webiny-overview/performance-benchmark/headless-cms-read-benchmark", "destination": "/docs/performance-and-load-benchmark/headless-cms/read-benchmark", "permanent": true}, {"source": "/docs/webiny-overview/performance-benchmark/pb-deliver-a-page", "destination": "/docs/performance-and-load-benchmark/page-builder/deliver-a-page", "permanent": true}, {"source": "/docs/key-topics/multi-tenancy", "destination": "/docs/overview/features/multi-tenancy", "permanent": true}, {"source": "/docs/how-to-guides/webiny-applications/admin-area/introduction", "destination": "/docs/overview/applications/admin", "permanent": true}, {"source": "/docs/how-to-guides/upgrade-webiny", "destination": "/docs/release-notes/upgrade-webiny", "permanent": true}, {"source": "/docs/headless-cms/extending/define-content-models-via-code", "destination": "/docs/headless-cms/extending/content-models-via-code", "permanent": true}, {"source": "/docs/how-to-guides/working-with-workspaces", "destination": "/docs/get-started/welcome", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/api/overview-vpc-custom", "destination": "/docs/architecture/api/overview-vpc-custom", "permanent": true}, {"source": "/docs/key-topics/cloud-infrastructure/api/overview-vpc-default", "destination": "/docs/architecture/api/overview-vpc-default", "permanent": true}, {"source": "/docs/key-topics/page-builder/prerendering-pages", "destination": "/docs/architecture/website/prerendering-pages", "permanent": true}, {"source": "/docs/key-topics/ui-composer/creating-elements", "destination": "/docs/page-builder/extending/create-a-page-element", "permanent": true}, {"source": "/docs/key-topics/ui-composer/introduction", "destination": "/docs/get-started/welcome", "permanent": true}, {"source": "/docs/references/page-builder/plugins", "destination": "/docs/page-builder/references/plugins", "permanent": true}, {"source": "/docs/tutorials/install-webiny", "destination": "/docs/get-started/welcome", "permanent": true}, {"source": "/docs/webiny-overview/performance-benchmark/headless-cms-write-benchmark", "destination": "/docs/performance-and-load-benchmark/introduction", "permanent": true}, {"source": "/docs/page-builder/extending/docs/page-builder/references/plugins", "destination": "/docs/page-builder/references/plugins", "permanent": true}, {"source": "/docs/page-builder-rendering-upgrade", "destination": "/docs/release-notes/5.34.0/page-builder-pe-rendering-engine-migration", "permanent": true}, {"source": "/docs/release-notes/5.34.0/page-elements-migration/upgrade-guide", "destination": "/docs/release-notes/5.34.0/page-builder-pe-rendering-engine-migration", "permanent": true}, {"source": "/docs/5.35.0/typography-variant-paragraph-categorization", "destination": "/docs/release-notes/5.35.0/upgrade-guide#typography-variant-was-categorized-as-a-paragraph-double-check-if-a-different-category-is-more-appropriate", "permanent": true}, {"source": "/docs/5.35.0/partially-migrated-theme-object-typography", "destination": "/docs/release-notes/5.35.0/upgrade-guide#could-not-migrate-some-of-the-theme-object-s-typography-related-code", "permanent": true}, {"source": "/docs/5.35.0/custom-emotion-plugins", "destination": "/docs/release-notes/5.35.0/upgrade-guide#found-custom-emotion-plugins-in-your-project-please-make-sure-to-update-them-to-the-latest-version", "permanent": true}, {"source": "/docs/5.35.0/page-builder-rendering-upgrade", "destination": "/docs/release-notes/5.35.0/upgrade-guide#some-of-the-theme-related-upgrades-were-not-applied-because-you-are-using-an-older-version-of-the-page-builder-rendering-engine", "permanent": true}, {"source": "/docs/admin-area/extending/cognito-federation", "destination": "/docs/enterprise/cognito-federation", "permanent": true}, {"source": "/docs/5.39.x/admin-area/extending/cognito-federation", "destination": "/docs/5.39.x/enterprise/cognito-federation", "permanent": true}, {"source": "/docs/5.38.x/admin-area/extending/cognito-federation", "destination": "/docs/5.38.x/enterprise/cognito-federation", "permanent": true}, {"source": "/docs/5.37.x/admin-area/extending/cognito-federation", "destination": "/docs/5.37.x/enterprise/cognito-federation", "permanent": true}, {"source": "/docs/admin-area/new-app-tutorial/introduction", "destination": "/docs/5.39.x/admin-area/new-app-tutorial/introduction", "permanent": true}, {"source": "/docs/admin-area/new-app-tutorial/scaffolding", "destination": "/docs/5.39.x/admin-area/new-app-tutorial/scaffolding", "permanent": true}, {"source": "/docs/admin-area/new-app-tutorial/get-started", "destination": "/docs/5.39.x/admin-area/new-app-tutorial/get-started", "permanent": true}, {"source": "/docs/admin-area/new-app-tutorial/extending-entities", "destination": "/docs/5.39.x/admin-area/new-app-tutorial/extending-entities", "permanent": true}, {"source": "/docs/admin-area/new-app-tutorial/security", "destination": "/docs/5.39.x/admin-area/new-app-tutorial/security", "permanent": true}, {"source": "/docs/admin-area/new-app-tutorial/securing-graphql-api", "destination": "/docs/5.39.x/admin-area/new-app-tutorial/securing-graphql-api", "permanent": true}, {"source": "/docs/admin-area/new-app-tutorial/securing-client-app", "destination": "/docs/5.39.x/admin-area/new-app-tutorial/securing-client-app", "permanent": true}, {"source": "/docs/core-development-concepts/security-framework/introduction", "destination": "/docs/security/security-framework/introduction", "permanent": true}, {"source": "/docs/core-development-concepts/security-framework/api-security", "destination": "/docs/security/security-framework/api-security", "permanent": true}, {"source": "/docs/core-development-concepts/security-framework/react-security", "destination": "/docs/security/security-framework/react-security", "permanent": true}]}