---
id: b9db081f
title: Audit Logs
description: A simple solution to monitor user activity across the Webiny platform for security and accountability purposes.
---

import { Alert } from "@/components/Alert";

import alLead from "./assets/al.png";
import alTrackingChanges from "./assets/al-overview.png";
import alFilter from "./assets/al-filter.gif";
import alDirectLink from "./assets/al-direct-link.png";
import alPayload from "./assets/al-payload.png";

<Alert type="success" title="What You’ll Learn">

- Audit Logs features and benefits

</Alert>

<Image src={alLead} title="Audit Logs" />

## Overview

For our Enterprise customers, Webiny built the Audit Logs application, a powerful monitoring solution designed to track changes across the entire Webiny application suite. 

Audit Logs keep a detailed record of all read, write, and delete operations, providing organizations with valuable insights to monitor, investigate, and resolve potential issues. From detecting security incidents to boosting user accountability, this application is key for maintaining a secure and compliant environment.

## Key Functionalities

1. **Tracking Changes:**
Audit Logs capture changes made across various Webiny applications, including the Advanced Publishing Workflow, File Manager, Form Builder, Headless CMS, i18n, Mailer, Page Builder, and Security. This comprehensive coverage ensures that all critical activities are logged and available for review.

<Image src={alTrackingChanges} title="Audit Logs" />

2. **Filtering:**
To streamline the review process, Webiny offers robust filtering capabilities within Audit Logs. Users can easily find relevant records by applying specific filters, minimizing the time and effort required for analysis.

<Image src={alFilter} title="Filtering" />

3. **Direct Link to the Record Changed:**
Audit Logs provide users with direct links to modified records, facilitating quick access to relevant information. This feature enhances efficiency and enables swift action, ensuring timely response to any identified issues.

<Image src={alDirectLink} title="Direct Link to Record" />

4. **View the Payload:**
Users can view the full payload for each logged action within Audit Logs, gaining comprehensive insight into the details of each operation. This visibility enables thorough analysis and facilitates informed decision-making.

<Image src={alPayload} title="View the Payload" />

## Benefits
The introduction of Audit Logs marks a significant step forward in enhancing enterprise security and accountability within the Webiny ecosystem. By providing visibility into CMS activities, supporting compliance with industry regulations, and empowering users to track and analyze critical changes, Audit Logs become an essential component of any organization's security infrastructure.

## Get Started
If you are one of our Enterprise customers, reach out to our team to get started. We welcome your feedback and suggestions on how we can further enhance this application.