---
id: aafea42f
title: Lexical Tools
description: Learn how to use Lexical tools to manipulate Lexical state.
---

<CanIUseThis since={"5.38.0"} />

<WhatYouWillLearn>

- how to parse HTML to Lexical state
- how to convert Lexical state to HTML

</WhatYouWillLearn>

## Overview

Webiny uses the [Lexical](https://lexical.dev/) text editor framework to power its Rich Text Editor components. It's very powerful, and enables us to build a feature rich and extendable text editors. The downside is that the output of the editor is not HTML, as you would expect from a WYSIWYG editor.

To help you convert Lexical's native state shape into HTML, and vice-versa, we've created a set of utilities, which reside in our `@webiny/lexical-converter` package.

This article demonstrates how to use this utility package to convert your HTML or Lexical data into the desired form.

## HTML to Lexical

To parse HTML to a valid Lexical state, Lexical editor requires a DOM document. Every environment is different, so there's an extra step you need to take before you use our tool, and that is converting your HTML to a DOM document.

Browsers have a native [DOMParser](https://developer.mozilla.org/en-US/docs/Web/API/DOMParser) class you can just instantiate and parse your HTML.

Node.js is a bit more involved, because DOM is not a native thing for Node.js. You'll need to use a library like [JSDOM](https://github.com/jsdom/jsdom), to parse your HTML to a DOM document.

### Browser Usage

In the browser environment, you can simply use the `DOMParser` class.

```ts Parse HTML to a DOM document in the browser
const html = `<p>My input HTML</p>`;
const parser = new DOMParser();
const domDocument = parser.parseFromString(html, "text/html");
```

### Node.js Usage

In the Node.js environment, first install a DOM library of choice. We'll use JSDOM:

```shell Install JSDOM
// NPM
npm i jsdom

// Yarn
yarn add jsdom
```

Then use it to parse your HTML, and get a DOM document:

```ts Parse HTML to a DOM document in Node.js
import JSDOM from "jsdom";

const html = `<p>My input HTML</p>`;
const { window } = new JSDOM(html);
const domDocument = window.document;
```

### Convert DOM to Lexical State

Once you have your DOM document, you can convert it to Lexical state:

```ts Convert DOM to Lexical State
import { createHtmlToLexicalParser } from "@webiny/lexical-converter";

// Create a parser.
const parser = createHtmlToLexicalParser();

// Get a DOM document (as described in previous sections).
const document = getDomDocument();

// Parse DOM document to Lexical state.
const lexicalState = parser(document);
```

### Custom Nodes

You can configure your Lexical parser with [custom Lexical nodes](https://lexical.dev/docs/concepts/nodes#creating-custom-nodes). This will often be necessary if your HTML contains tags unknown to Webiny.

```ts Add Custom Nodes
import { createHtmlToLexicalParser } from "@webiny/lexical-converter";

// Create a parser with custom nodes.
const parser = createHtmlToLexicalParser({
  editorConfig: {
    nodes: [MyCustomNode]
  }
});

// Get a DOM document (as described in previous sections).
const document = getDomDocument();

// Parse DOM document to Lexical state.
const lexicalState = parser(document);
```

### Custom Node Mapper

You might also need to perform some actions on your node instance, after the DOM is parsed into a Lexical state. To process each parsed node, configure the parser with a `nodeMapper` property:

```ts Add a Custom Node Mapper
import { LexicalNode } from "lexical";
import { createHtmlToLexicalParser } from "@webiny/lexical-converter";
import { $isHeadingNode } from "@webiny/lexical-nodes";

// This is a custom mapper that will pass some styling information to a matching node.
const addCustomThemeStyleToHeadings = (node: LexicalNode): LexicalNode => {
  // Use utilities provided by Webiny to check for built-in node types.
  if ($isHeadingNode(node)) {
    node.setThemeStyles([{ styleId: "my-default-id", type: "typography" }]);
  }

  return node;
};

// Create a parser with a custom node mapper.
const parser = createHtmlToLexicalParser({
  nodeMapper: addCustomThemeStyleToHeadings
});

// Get a DOM document (as described in previous sections).
const document = getDomDocument();

// Parse DOM document to Lexical state.
const lexicalState = parser(document);
```

## Lexical to HTML

To convert raw Lexical state to HTML, Lexical editor requires DOM `document` to be present globally. In the browser, this means on the `window` object (`window.document`), and in Node.js, this means on the `global` object (`global.document`).

### Browser Usage

In the browser environment, DOM is present by definition, so no extra steps are necessary.

### Node.js Usage

In the Node.js environment, first install a DOM library of choice. We'll use JSDOM:

```shell Install JSDOM
// NPM
npm i jsdom

// Yarn
yarn add jsdom
```

Then use it to set up the environment:

```ts Set up the necessary objects
const dom = new jsdom.JSDOM();
global.window = dom.window;
global.document = dom.window.document;
global.DocumentFragment = dom.window.DocumentFragment;
```

To see a real-life example of this setup, have a look at our [default Lexical to HTML implementation](https://github.com/webiny/webiny-js/blob/v5.39.0/packages/api-headless-cms/src/htmlRenderer/LexicalRenderer.ts).

Once DOM objects are configured, you can render your Lexical state to HTML. Webiny provides a Lexical transformer utility, which allows to render to various output formats.

### Render to an HTML string

To convert Lexical state to plain HTML string, use the `toHtml` method. This will simply output everything as a `string`.

```ts Convert Lexical state to an HTML string
import { createLexicalStateTransformer } from "@webiny/lexical-converter";

const transformer = createLexicalStateTransformer();
const html = transformer.toHtml(lexicalState);
```

### Render to an array of objects

To convert Lexical state to an array of objects, where each object represents a top-level block element, use the `flatten` method. Each block will be converted to a standalone HTML string, and you will also get an instance of the Lexical Node object along with the block HTML.

```ts Convert Lexical state to an array of objects
import { createLexicalStateTransformer } from "@webiny/lexical-converter";

const transformer = createLexicalStateTransformer();
const output = transformer.flatten(lexicalState);
```

The output of this method will look something like this:

```ts Pseudo-code demonstrating the output of the flatten method
const output = [
  {
    node: expect.any(HeadingNode),
    html: `<h1 dir="ltr"><span style=\"white-space: pre-wrap;\">Heading</span></h1>`
  },
  {
    node: expect.any(ParagraphNode),
    html: `<p dir="ltr"><br></p>`
  }
];
```

To see various use cases for these methods, have a look at the [test file](https://github.com/webiny/webiny-js/blob/v5.39.0/packages/lexical-converter/__tests__/stateTransformer.test.ts).
