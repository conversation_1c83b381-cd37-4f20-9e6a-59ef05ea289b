---
id: aafea433
title: Folder Level Permissions
description: Learn about the Folder Level Permissions feature and how to use it.
---

import flp from "./assets/flp.png";
import flpManagePermissionsDialog from "./assets/flp-manage-permissions-dialog.png";
import flpManagePermissionsOption from "./assets/flp-manage-permssions-option.png";
import flpIconOnFolder from "./assets/flp-icon-on-folder.png";

<CanIUseThis enterprise={true} since={"5.38.0"} />

<WhatYouWillLearn>

- an overview of the features the Folder Level Permissions feature provides and how to use it
- how the enable Folder Level Permissions

</WhatYouWillLearn>

## Overview

With the 5.38.0 release, we've expanded Webiny's [Advanced Access Control Layer (AACL)](/docs/release-notes/5.37.0/changelog#introducing-advanced-access-control-layer-aacl-2576), by introducing a brand-new feature called **Folder Level Permissions**.

<Image src={flp} alt={"Folder Level Permissions"} shadow={false} />

With this feature, users can define access permissions on a folder level. More specifically, users that are designated as **folder owners** can now define which users (and also teams with the [Teams](/docs/{version}/enterprise/aacl/teams) feature enabled) can access which folders, and what actions they can perform on those folders:

- **Viewer** - users can view content, but not modify it
- **Editor** - users can view and modify content
- **Owner** - users can edit and manage content permissions

Folder Level Permissions can be used across the three main Webiny applications: **Headless CMS** (content entries), **File Manager** (files), and **Page Builder** (pages).

## Enabling Folder Level Permissions and Feature Overview

For Webiny Enterprise users, apart from [linking their Webiny project](/docs/{version}/wcp/link-a-project) with Webiny Control Panel (WCP), there are no additional steps required to enable Folder Level Permissions.

Once linked, Folder Level Permissions will be automatically enabled and full access users can start using it all three applications: **Headless CMS**, **File Manager**, and **Page Builder**.

<Alert type="info">

Once enabled, note that only users that have the **Full Access** security role assigned can use the Folder Level Permissions feature. They are the ones that can then assign new owners to folders that can then manage permissions for other users.

</Alert>

For example, if we were to open the Page Builder app, right-clicking on a folder in the tree on the left will open a context menu, now with the **Manage Permissions** option included:

<Image src={flpManagePermissionsOption} alt={"Manage Permissions Option"} />

Clicking on the **Manage Permissions** option will open a dialog where we can assign users and teams to the folder, and also define their permissions:

<Image src={flpManagePermissionsDialog} alt={"Manage Permissions Dialog"} />

Once permissions are assigned, the icon of the folder changes, indicating that the folder has permissions assigned:

<Image src={flpIconOnFolder} alt={"Folder Level Permissions Assigned"} />

## FAQ

### When I Enable Folder Level Permissions, Will All My Existing Users Be Able To Use It?

No. Only users that have the **Full Access** security role assigned can use the Folder Level Permissions feature.

### In terms of access for other users, will anything change once I enable Folder Level Permissions?

No. The only thing that will change is that users that have the **Full Access** security role assigned will be able to use the Folder Level Permissions feature.

But in terms of what users can access, nothing will change. For example, if a user has access to a specific folder, they will still have access to it after enabling Folder Level Permissions. Only by using the Folder Level Permissions feature and setting permissions on a folder level, can you change what users can access.

### Can I Use Folder Level Permissions With The Teams Feature?

Yes. You can assign teams to folders, and then define their permissions.

### Can I assign permissions to the root folder?

No. The root folder is always accessible to all users.

A workaround for this is to create a new folder, and then move all the content from the root folder to the new folder. Then, you can assign permissions to the new folder.

### How does Folder Level Permissions feature work with existing security layer?

Folder Level Permissions feature is an extension of the existing [security layer](/docs/{version}/enterprise/aacl/introduction#the-three-tiers-of-webiny-s-security-layer). It does not replace it.

This means that existing security roles and security teams are still the first thing that is checked when a user tries to access a resource. For example, if user's security role doesn't grant access to Page Builder, then the user will not be able to access Page Builder, even if they have permissions assigned to a folder in Page Builder.

### Can I Use Folder Level Permissions with API Tokens?

At the moment, the answer is no. API tokens are not subject to folder level permissions. They will always have access to all folders.


