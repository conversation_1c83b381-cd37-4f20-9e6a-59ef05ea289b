---
id: 070aa2ee
title: Environment Utilities
description: Learn about environment-related utility functions available within the Admin application.
---

import { WhatY<PERSON><PERSON><PERSON><PERSON>earn } from "@/components/WhatYouWillLearn";

<WhatYouWillLearn>
  - what are environment-related utility functions
  - what environment-related utility functions are available
</WhatYouWillLearn>

## Overview

This article covers a couple of environment-related utility functions that can be used within the Admin application, or, in other words, when developing extensions for the Admin application.

<Alert>

  To learn more about extensions in general, please visit the [Extensions](/docs/{version}/core-development-concepts/basics/extensions) article.

</Alert>

## Available Utility Functions

The following is a list of available utility functions:

```tsx
import {
  getApiUrl,
  getGqlApiUrl,
  getHeadlessCmsGqlApiUrl,
  getLocaleCode,
  getTenantId,
  isLocalhost,
} from "@webiny/app-admin"; // Use `@webiny/app` for versions 5.41.3 or older.

// Returns URL of Webiny's backend API.
getApiUrl(); // https://xyz.cloudfront.net

// Returns URL of Webiny's backend GraphQL API.
getGqlApiUrl(); // https://xyz.cloudfront.net/graphql

// Returns URLs of Webiny's backend Headless CMS GraphQL API.
getHeadlessCmsGqlApiUrl(); // { preview: "...", manage: "...", read: "..."}

// Returns locale used on the page.
getLocaleCode(); // en-US

// Returns current tenant.
getTenantId(); // root

// Returns `true` if the application is run on localhost.
isLocalhost(); // true
```

<Alert>

  For versions 5.41.3 or older, instead of the `@webiny/app-admin` package, please use the `@webiny/app` package.

</Alert>
