---
id: aafea428
title: Customize Entry List Bulk Actions
description: Learn how to add, replace, or remove bulk actions in the Entry List.
---

import hcmsBulkActions from "./assets/hcms-bulk-actions.png";
import hcmsBulkActionsSimple from "./assets/hcms-bulk-actions-simple.gif";
import hcmsBulkActionsAsync from "./assets/hcms-bulk-actions-async.gif";
import hcmsBulkActionsBackgroundTask from "./assets/hcms-bulk-actions-background-task.gif";
import hcmsBulkActionsDiscover from "./assets/hmcs-bulk-actions-discover.png";
import hcmsBulkActionsPosition from "./assets/hcms-bulk-actions-position.png";

import { Alert } from "@/components/Alert";

<Alert type="info" title="Can I Use This?">

This feature is available since Webiny **v5.38.0**.

</Alert>

<Alert type="success" title="What you will learn">

- how to add a custom bulk action to the Entry List
- how to discover existing bulk action names
- how to change the position, remove, or replace an existing bulk action

</Alert>

## Overview

In Headless CMS, pre-built actions empower users to modify status, delete, or move multiple entries effortlessly. These actions become visible when you select one or more entries from the list.

To work with bulk actions, you need to use the `ContentEntryListConfig` component. For this article, we will use the `BulkAction` in the `Browser` namespace.

```diff-tsx apps/admin/src/App.tsx
import React from "react";
import { Admin } from "@webiny/app-serverless-cms";
import { Cognito } from "@webiny/app-admin-users-cognito";
+ import { ContentEntryListConfig } from "@webiny/app-headless-cms";
import "./App.scss";

+ // You can destructure child components to make the code more readable and easier to work with.
+ const { Browser } = ContentEntryListConfig;

export const App = () => {
    return (
      <Admin>
        <Cognito />
+       <ContentEntryListConfig>
+          <Browser.BulkAction name={"demo-bulk-action”} element={<span>Demo Bulk Action</span>} />
+       </ContentEntryListConfig>
      </Admin>
    );
};
```

`Browser` is the crucial component of the Admin app's Content Entry List. This is where users can easily browse through entries, apply filters, perform searches, create new entries, and organize them into folders.

If users select one or more entries from the list, the bulk action bar will appear.

<Image src={hcmsBulkActions} title="Browser and the Built-in Bulk Actions" />

## Add a Bulk Action

To add a new bulk action, use the `ContentEntryListConfig` component and mount it within your Admin app. This component will serve as the foundation for your bulk actions.

To ensure UI consistency, use the `useButtons` hook to access four button components:

- `ButtonDefault`
- `ButtonPrimary`
- `ButtonSecondary`
- `IconButton`

Choose the one that best suits your design needs. In the following examples, we will use the `IconButton` component.

### Simple Bulk Action

Here is an example of creating a bulk action that copies the selected entries to the clipboard in JSON format.

<Image src={hcmsBulkActionsSimple} title="Custom Simple Bulk Action" />

<Alert type={"info"} title="Extension Available!">

This example can be installed directly into your project by running:<br/>
`yarn webiny scaffold extension headless-cms/bulk-action-simple`

Extension source code, and the accompanying content model, can be found [here](https://github.com/webiny/webiny-examples/tree/master/headless-cms/bulk-action-simple).

</Alert>

```tsx
import React from "react";
import { useSnackbar } from "@webiny/app-admin";
import { ContentEntryListConfig } from "@webiny/app-headless-cms";
import { ReactComponent as CopyIcon } from "@material-design-icons/svg/outlined/content_copy.svg";

const { BulkAction } = ContentEntryListConfig.Browser;
const { useWorker, useButtons, useDialog } = BulkAction;

const ActionCopyJson = () => {
  const { IconButton } = useButtons();
  const { showSnackbar } = useSnackbar();
  const worker = useWorker();

  const copyJson = () => {
    navigator.clipboard.writeText(JSON.stringify(worker.items, null, 2));
    showSnackbar("JSON data copied to clipboard.");
  };

  return (
    <IconButton
      icon={<CopyIcon />}
      onAction={copyJson}
      label={`Copy as JSON`}
      tooltipPlacement={"bottom"}
      disabled={worker.isSelectedAll}
    />
  );
};

export const Extension = () => {
  return (
    <>
      <ContentEntryListConfig>
        <BulkAction
          name={"copy-json"}
          element={<ActionCopyJson />}
        />
      </ContentEntryListConfig>
    </>
  );
};
```

The `items` property obtained from `useWorker()` is a crucial part of managing bulk actions: these are the currently selected items within the context of the Content Entry List.

By declaring the `modelIds` prop, you can define in which Content Model Entry List you want to show your bulk action. If you exclude this prop, the bulk action will be registered for all models in the system. For instance, in the given example, next time you open the `article` content model browser and choose one or multiple entries, the defined bulk action will be shown.

This is the whole process of registering a new bulk action element.

### Asynchronous Bulk Action

Occasionally, you might need to perform a bulk action that involves various asynchronous tasks, like syncing chosen entries with an external service. In these situations, we can leverage the `useWorker` and `useDialogs` hooks to ensure a smooth user experience:

- `useWorker` provides the currently selected items and the `processInSeries` method to perform our callback.
- `useDialog` provides methods to display confirmation and result dialogs.

Here’s an example of creating a bulk action that syncs the selected entries with an external service using `fetch`, and shows the result of each iteration.

<Image src={hcmsBulkActionsAsync} title="Asyncronous Bulk Action" />

<Alert type={"info"} title="Extension Available!">

This example can be installed directly into your project by running:<br/>
`yarn webiny scaffold extension headless-cms/bulk-action-asynchronous`

Extension source code, and the accompanying content model, can be found [here](https://github.com/webiny/webiny-examples/tree/master/headless-cms/bulk-action-asynchronous).

</Alert>

```tsx
import React from "react";
import { ContentEntryListConfig } from "@webiny/app-headless-cms";
import { ReactComponent as SyncIcon } from "@material-design-icons/svg/outlined/sync.svg";

const { BulkAction } = ContentEntryListConfig.Browser;
const { useWorker, useButtons, useDialog } = BulkAction;

const ActionSync = () => {
  const { IconButton } = useButtons();
  const { showConfirmationDialog, showResultsDialog } = useDialog();
  const worker = useWorker();

  const openSyncDialog = () =>
    showConfirmationDialog({
      title: "Sync",
      message: `You are about to sync the selected entries. Are you sure you want to continue?`,
      loadingLabel: `Processing`,
      execute: async () => {
        await worker.processInSeries(async ({ item, report }) => {
          try {
            const response = await fetch(
              "https://any.url.com",
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json"
                },
                body: JSON.stringify(item.id)
              }
            );

            report.success({
              title: item.meta.title,
              message: `Entry successfully synced, response status: ${response.status}`
            });
          } catch (e) {
            report.error({
              title: item.meta.title,
              message: e.message
            });
          }
        }, 5);

        worker.resetItems();

        showResultsDialog({
          results: worker.results,
          title: "Sync",
          message: "Operation completed, here below you find the complete report:"
        });
      }
    });

  return (
    <IconButton
      icon={<SyncIcon />}
      onAction={openSyncDialog}
      label={"Sync"}
      tooltipPlacement={"bottom"}
      disabled={worker.isSelectedAll}
    />
  );
};

export const Extension = () => {
  return (
    <>
      <ContentEntryListConfig>
        <BulkAction
          name={"sync"}
          element={<ActionSync />}
        />
      </ContentEntryListConfig>
    </>
  );
};
```

For the `IconButton` component, the `onAction` prop can accept any method. In the current scenario, we are using the `showConfirmationDialog` method to open a dialog.

`showConfirmationDialog` is designed to receive a callback function, which will be executed when the users confirm their action. You can pass any callback function to this method or use the `processInSeries` method from the `useWorker` hook.

`processInSeries` requires a callback as its first parameter. This callback is provided with the current item of the list being iterated and `report`: use this information to collect the result of the operation and indicate whether it was successful or not. The second parameter is the `chunk` size, which determines how many items are processed in each batch. You can adjust this value to control the size of each batch.

`showResultsDialog` is used after the completion of the sending process to present the user with a summary of the actions performed, including information about successful entries sent and any errors encountered during the process. This allows the user to review a detailed report of the entire operation.

By declaring the `modelIds` prop, you can define in which Content Model Entry List you want to show your bulk action. If you exclude this prop, the bulk action will be registered for all models in the system. For instance, in the given example, next time you open the `article` content model browser and choose one or multiple entries, the defined bulk action will be shown.

### Bulk Action via Background Task

When working with large datasets, performing bulk actions in the background is essential for maintaining a smooth user experience. Webiny allows editors to perform bulk actions asynchronously in the background, eliminating the need for sequential processing on the client side. This functionality is powered by the [Webiny background task feature](/docs/core-development-concepts/background-tasks/about-background-tasks), which simplifies large-scale operations.

In this tutorial, we'll walk you through creating a plugin to enable bulk actions in the background. You don't need to be familiar with the background task infrastructure, as Webiny provides simplified APIs that allow you to focus on your business logic.

By the end of this article, you'll learn how to:

- Create a bulk action task that runs in the background.
- Develop an admin plugin that manages triggers and background processes for the specific task.

<Image src={hcmsBulkActionsBackgroundTask} title="Bulk Action via Background Task" />

<Alert type={"info"} title="Extension Available!">

This example can be installed directly into your project by running:<br/>
`yarn webiny scaffold extension headless-cms/bulk-action-background-task`

Extension source code, and the accompanying content model, can be found [here](https://github.com/webiny/webiny-examples/tree/master/headless-cms/bulk-action-background-task).

</Alert>

#### Defining the Bulk Action Task

When creating a bulk action task, it’s important to define both the logic for listing and processing entries. Another critical aspect of bulk action tasks is state management. As entries are processed, their state must change to ensure they aren't queried multiple times during subsequent listing operations. This is particularly important when dealing with large datasets. Without a mechanism to mark entries as processed (e.g., by updating a field), the system could repeatedly process the same items, leading to inefficiency and redundancy.

In this example, we update the entry title with a unique tag (e.g., the current year) during the bulk action. This not only serves the purpose of the task but also prevents the entry from being listed again in subsequent operations, as the title no longer matches the filtering criteria.

```ts
import {
  createBulkAction,
  IProcessEntry,
  IListEntries
} from "@webiny/api-headless-cms-bulk-actions";
import { getUpdatedTag } from "@demo/shared";
import { HcmsBulkActionsContext } from "@webiny/api-headless-cms-bulk-actions/types";
import { CmsEntryListParams, CmsModel } from "@webiny/api-headless-cms/types";

// Class for listing entries that need updating
class ListLatestEntriesUpdatedThisYear implements IListEntries {
  private readonly context: HcmsBulkActionsContext;

  constructor(context: HcmsBulkActionsContext) {
    this.context = context;
  }

  async execute(modelId: string, params: CmsEntryListParams) {
    const model = await this.context.cms.getModel(modelId);
    if (!model) {
      throw new Error(`Model with ${modelId} not found!`);
    }

    const [entries, meta] = await this.context.cms.listLatestEntries(model, {
      ...params,
      where: {
        ...params.where,
        title_not_startsWith: getUpdatedTag() // Filter entries that haven't been updated
      }
    });

    return {
      entries,
      meta
    };
  }
}

// Class for processing the selected entries by prepending the year
class PrependCurrentYearToTitle implements IProcessEntry {
  private readonly context: HcmsBulkActionsContext;

  constructor(context: HcmsBulkActionsContext) {
    this.context = context;
  }

  async execute(model: CmsModel, id: string) {
    const entry = await this.context.cms.getEntryById(model, id);
    await this.context.cms.createEntryRevisionFrom(model, id, {
      title: getUpdatedTag() + entry.values.title
    });
  }
}

// Function that ties everything together
export const createBulkActionUpdateYear = () => {
  return [
    createBulkAction({
      name: "updateYear", // Unique name for the bulk action - Camel Case
      dataLoader: (context: HcmsBulkActionsContext) =>
        new ListLatestEntriesUpdatedThisYear(context),
      dataProcessor: (context: HcmsBulkActionsContext) =>
        new PrependCurrentYearToTitle(context)
    })
  ];
};
```

The bulk action task uses two classes:
1. `ListLatestEntriesUpdatedThisYear`: Lists the entries that haven't been updated yet, filtering out entries where the title already includes the current year.
2. `PrependCurrentYearToTitle`: For each entry, it appends the current year to the title and saves the new revision.

#### Building the Admin Plugin

In this section, we will create an admin interface to trigger the bulk action. You will observe similarities with other task definitions. A critical aspect is defining how to invoke the background task when selecting all entries.

The admin plugin must accommodate two methods for processing entries:

- Invoking the newly defined Bulk Action Task using its name in PascalCase (`UpdateYear`).
- Providing the client-side code that executes while processing the entries.

At this stage, please note that real-time status updates on background tasks are not yet supported. Users will need to manually refresh the page to view the current status of their tasks.

```tsx
import React from "react";
import { useRecords } from "@webiny/app-aco";
import { useSnackbar } from "@webiny/app-admin";
import { ContentEntryListConfig, useCms, useModel } from "@webiny/app-headless-cms";
import { getUpdatedTag } from "@demo/shared";
import { ReactComponent as CalendarIcon } from "@material-design-icons/svg/outlined/calendar_month.svg";

const { BulkAction } = ContentEntryListConfig.Browser;
const { useWorker, useButtons, useDialog } = BulkAction;

const ActionUpdateYear = () => {
  const { IconButton } = useButtons();
  const worker = useWorker();
  const { showConfirmationDialog, showResultsDialog } = useDialog();
  const { model } = useModel();
  const { createEntryRevisionFrom } = useCms();
  const { updateRecordInCache } = useRecords();
  const { showSnackbar } = useSnackbar();

  const openUpdateYearDialog = () =>
    showConfirmationDialog({
      title: "Update Year",
      message: "You are about to update the year for the selected entries. Are you sure you want to continue?",
      loadingLabel: "Processing",
      execute: async () => {
        if (worker.isSelectedAll) {
          // Invoke the bulk action task for all selected entries
          await worker.processInBulk({ action: "UpdateYear" });
          worker.resetItems();

          showSnackbar(
            "All entries will be updated. This process will be carried out in the background and may take some time.",
            { dismissIcon: true, timeout: -1 }
          );
          return;
        }

        // Process each selected entry individually
        await worker.processInSeries(async ({ item, report }) => {
          try {
            const response = await createEntryRevisionFrom({
              model,
              id: item.id,
              input: { title: getUpdatedTag() + item.title }
            });

            const { error } = response;
            if (error) throw new Error(error.message || "Unknown error while publishing the entry");

            updateRecordInCache(response.entry);

            report.success({
              title: item.meta.title,
              message: "Entry successfully sent"
            });
          } catch (e) {
            report.error({
              title: item.meta.title,
              message: e.message
            });
          }
        });

        worker.resetItems();

        showResultsDialog({
          results: worker.results,
          title: "Update Year",
          message: "Operation completed. Below is the complete report:"
        });
      }
    });

  return (
    <IconButton
      icon={<CalendarIcon />}
      onAction={openUpdateYearDialog}
      label="Update Year"
      tooltipPlacement="bottom"
    />
  );
};

export const Extension = () => {
  return (
    <>
      <ContentEntryListConfig>
        <BulkAction name={"update-year"} element={<ActionUpdateYear />} />
      </ContentEntryListConfig>
    </>
  );
};
```
## Discover Bulk Actions

This section demonstrates how you can discover the names of existing bulk actions. This is important for further sections on positioning, removing, and replacing bulk actions.

The easiest way to discover existing bulk actions is to use your browser's React Dev Tools plugins, select at least one entry and look for the `Buttons` element inside `BulkActions`. From there, you can either look for `actions` prop or look at the child elements and their keys:

<Image src={hcmsBulkActionsDiscover} title="Discover Existing Bulk Actions" />

## Position a Bulk Action

To position your custom bulk action before or after an existing action, you can use the `before` and `after` props on the `<Browser.BulkAction>` element:

```tsx
<ContentEntryListConfig>
  <Browser.BulkAction name={"copy-json"} element={<ActionCopyJson />} before={"delete"} />
</ContentEntryListConfig>
```

<Image src={hcmsBulkActionsPosition} title="Position Your Action Before Another Action" />

## Remove a Bulk Action

Sometimes you might want to remove an existing bulk action. All you need to do is reference the action by name and pass a `remove` prop to the `<Browser.BulkAction>` element:

```tsx
<ContentEntryListConfig>
  <Browser.BulkAction name={"delete"} remove />
</ContentEntryListConfig>
```

## Replace a Bulk Action

To replace an existing bulk action with a new action element, you need to reference an existing action by name and pass a new component via the `element` prop:

```tsx
<ContentEntryListConfig>
  <Browser.BulkAction name={"delete"} element={<span>A new action!</span>} />
</ContentEntryListConfig>
```
