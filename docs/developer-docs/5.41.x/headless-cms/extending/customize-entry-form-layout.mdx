---
id: 68ccc9a8
title: Customize Entry Form Layout
description: How to create a custom form layout for content model data entry.
---

import { Alert } from "@/components/Alert";

<CanIUseThis since={"5.28.0"} />

<WhatYouWillLearn>

  - how to render custom form layout
  - how to render UI elements based on user's permissions
  - how to use form data to render custom UI elements

</WhatYouWillLearn>

## Overview

Out of the box, our Headless CMS allows you to organize content model fields in a grid layout. That is fine for a basic UI, with several fields. However, as your content model grows, you may need to organize the UI to be more visually appealing and easier to navigate for your content editors.

Custom form layouts will also come in handy if you need to show or hide certain elements of the form based on content editor's permissions, or even render additional UI like helper text, links, images, etc.

## Getting Started

<ExtensionsGettingStarted
  type={"admin"}
  name={"customCmsEntryFormLayout"}
  dependencies={"@webiny/app-headless-cms,@webiny/api-serverless-cms,@webiny/ui"}
  fullExampleLink={"https://github.com/webiny/webiny-examples/tree/master/custom-cms-entry-form-layout"}
  fullExampleDownloadLink={"custom-cms-entry-form-layout"}
/>

<Alert type={"warning"}>

  Note that code examples shown in this guide are working with the **Pizza** content model, whose creation is not covered in this guide. But, in the [full example](https://github.com/webiny/webiny-examples/blob/master/custom-cms-entry-form-layout/5.41.x/extensions/models/src/index.ts), the model is defined programmatically, so, you can use it to follow along.

</Alert> 

## Simple Layout

The following plugin renders a simple content form for the `pizza` content model.

Note that the `fields` object contains key-value pairs of content model fields, with key being the `fieldId` (defined in the content model editor), and value being a React element rendered using the appropriate field renderer. All you need to do is place those field elements in your new layout.

```tsx extensions/customCmsEntryFormLayout/src/index.tsx
import React from "react";
import { CmsContentFormRendererPlugin } from "@webiny/app-headless-cms";

export const Extension = () => {
    return (
        <>
            <CmsContentFormRendererPlugin
                modelId={"pizza"}
                render={({ fields }) => {
                    return <div>{fields.title}</div>;
                }}
            />
        </>
    );
};
```

## Conditional UI

If we want to render UI based on user's permissions, we'll need to utilize the `useSecurity` React hook. In this example, we're creating a more elaborate layout for our form, using the `Tabs` component:


```tsx extensions/customCmsEntryFormLayout/src/index.tsx
import React from "react";
import { useSecurity } from "@webiny/app-serverless-cms";
import { CmsContentFormRendererPlugin } from "@webiny/app-headless-cms";
import { Grid, Cell } from "@webiny/ui/Grid";
import { Tabs, Tab } from "@webiny/ui/Tabs";

type CmsContentFormRendererProps = React.ComponentProps<typeof CmsContentFormRendererPlugin>;

const PizzaLayout: CmsContentFormRendererProps["render"] = ({ fields }) => {
    // Access security identity.
    const { getPermission } = useSecurity();

    // Get the necessary permission.
    const bakeryPermission = getPermission("bakery");

    // Check if the user has the permission to edit a recipe.
    const canEditRecipe = bakeryPermission && bakeryPermission["canEditRecipe"] === true;

    return (
        <Tabs>
            <Tab label="General">
                <Grid>
                    <Cell span={12}>{fields["name"]}</Cell>
                </Grid>
                <Grid>
                    <Cell span={6}>{fields["price"]}</Cell>
                    <Cell span={6}>{fields["numberOfIngredients"]}</Cell>
                </Grid>
            </Tab>
            {/* Hide the Recipe tab if the user doesn't have the required permission. */}
            {canEditRecipe && (
                <Tab label="Recipe">
                    <Grid>
                        <Cell span={12}>{fields["recipe"]}</Cell>
                    </Grid>
                </Tab>
            )}
            <Tab label="History">
                <Grid>
                    <Cell span={12}>{fields["history"]}</Cell>
                </Grid>
            </Tab>
        </Tabs>
    );
};

export const Extension = () => {
    return (
        <>
            <CmsContentFormRendererPlugin modelId="pizza" render={PizzaLayout} />
        </>
    );
};
```

Here's what a default layout looks like:

![Default form layout](./assets/customize-entry-form/defaultLayout.png)

With our extension, we get our custom layout:

![Custom form layout](./assets/customize-entry-form/customLayout.png)

You can see the `Recipe` tab is hidden. That's because we don't have the necessary permission to view it.

## Custom UI Elements

Using form data you can also render other UI elements, or even show/hide fields depending on the value of some other field. In the following example, we show a warning if the price is less than `20` and number of ingredients is greater than `6`:

```diff-tsx extensions/customCmsEntryFormLayout/src/index.tsx
import React from "react";
import { useSecurity } from "@webiny/app-serverless-cms";
import { CmsContentFormRendererPlugin } from "@webiny/app-headless-cms";
import { Grid, Cell } from "@webiny/ui/Grid";
import { Tabs, Tab } from "@webiny/ui/Tabs";
+ import { Alert } from "@webiny/ui/Alert";

type CmsContentFormRendererProps = React.ComponentProps<typeof CmsContentFormRendererPlugin>;

+ const PizzaLayout: CmsContentFormRendererProps["render"] = ({ fields, data }) => {
    // Access security identity.
    const { getPermission } = useSecurity();

    // Get the necessary permission.
    const bakeryPermission = getPermission("bakery");

    // Check if the user has the permission to edit a recipe.
    const canEditRecipe = bakeryPermission && bakeryPermission["canEditRecipe"] === true;

    const priceTooLow = data["price"] < 20 && data["numberOfIngredients"] > 6;

    return (
        <Tabs>
            <Tab label="General">
+               {priceTooLow && (
+                   <Grid>
+                       <Cell span={12}>
+                           <Alert type={"warning"} title={"Please double-check your input"}>
+                               The price of <strong>{data["price"]}</strong> seems too low for a pizza
+                               with over <strong>6</strong> ingredients.
+                           </Alert>
+                       </Cell>
+                   </Grid>
+               )}

                <Grid>
                    <Cell span={12}>{fields["name"]}</Cell>
                </Grid>
                <Grid>
                    <Cell span={6}>{fields["price"]}</Cell>
                    <Cell span={6}>{fields["numberOfIngredients"]}</Cell>
                </Grid>
            </Tab>
            {/* Hide the Recipe tab if the user doesn't have the required permission. */}
            {canEditRecipe && (
                <Tab label="Recipe">
                    <Grid>
                        <Cell span={12}>{fields["recipe"]}</Cell>
                    </Grid>
                </Tab>
            )}
            <Tab label="History">
                <Grid>
                    <Cell span={12}>{fields["history"]}</Cell>
                </Grid>
            </Tab>
        </Tabs>
    );
};

export const Extension = () => {
    return (
        <>
            <CmsContentFormRendererPlugin modelId="pizza" render={PizzaLayout} />
        </>
    );
};
```

Using this approach, you can add a nice touch to your UI and guide the content editor with additional logic, validate the input, and add other helpful elements which would otherwise be really difficult to define on the content model itself:

![Custom UI elements](./assets/customize-entry-form/customElements.png)
