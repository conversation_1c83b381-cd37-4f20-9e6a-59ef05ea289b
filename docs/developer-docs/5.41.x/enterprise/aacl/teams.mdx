---
id: aafea434
title: Teams
description: Learn about the Teams feature and how to use it.
---

import aaclTeams from "./assets/aacl-teams.png";
import aaclTeamsMenu from "./assets/aacl-teams-menu.png";
import aaclTeamsAutocomplete from "./assets/aacl-teams-autocomplete.png";
import usersRoles from "./assets/users-roles.png";
import usersTeamsRoles from "./assets/users-teams-roles.png";

<CanIUseThis enterprise={true} since={"5.37.0"} />

<WhatYouWillLearn>

- an overview of the features the Teams feature provides and how to use it
- how the enable Teams

</WhatYouWillLearn>

## Overview

With the Open Source and Business tiers, admin users can only be linked with one or more security roles.

<Image src={usersRoles} title={"Roles Assigned To Users"} shadow={false} />

This means that if you want to assign the same set of roles to multiple users, you would have to manually assign them to each user.

And although this approach might work for some users, it can quickly become cumbersome to manage. This is where **Teams** comes in. With it, users can be assigned into a team, where each team can be linked with one or more roles.

<Image src={usersTeamsRoles} title={"Users Assigned to Teams"} shadow={false} />

This feature is especially useful for larger organizations, where it's common to have multiple teams working on different projects. Also, it's a great way to simplify the process of managing permissions for multiple users, as you can simply assign a role to a team, instead of assigning it to each individual user.

Additionally, the Teams feature can be used in conjunction with [Folder Level Permissions (FLP)](/docs/enterprise/aacl/folder-level-permissions) to further enhance the security of your Webiny project. Instead of just being able to define folder level permissions for individual users, you can now define them for teams as well. Make sure to check out the FLP documentation to learn more about this feature. 

## Enabling Teams and Feature Overview

For Webiny Enterprise users, apart from [linking their Webiny project](/docs/wcp/link-a-project) with Webiny Control Panel (WCP), there are no additional steps required to enable Teams.

Once linked, Teams will be automatically enabled and the module can be accessed from the main menu:

<Image src={aaclTeamsMenu} title={"Teams Available From the Main Menu"} />

Via the Teams module, users can create new teams that consist of one or more security roles.

<Image src={aaclTeams} title={"Creating a New Team"} />

Once a team is created, users can assign it to one or more users. This can be done by editing the user and selecting the team via the Team field.

<Image src={aaclTeamsAutocomplete} title={"Assigning Admin Users To Teams"} />

## FAQ

### Can I Assign Multiple Security Roles To a User Without The Teams Enabled ?

From [Webiny 5.41.0 onwards](/docs/release-notes/5.41.0/changelog#multiple-roles-and-teams-assignments-4198), you can assign multiple roles to a user.

### Can I Assign Multiple Roles To a Team?

Yes, you can assign multiple roles to a team.

### Can I Assign Users To Multiple Teams?

From [Webiny 5.41.0 onwards](/docs/release-notes/5.41.0/changelog#multiple-roles-and-teams-assignments-4198), you can assign users to multiple teams.

### Can I use Teams with the Open Source or Business tier?

No. Teams are only available with the Enterprise tier.
