---
id: 0f4cee12
title: Watch Command
description: Learn how to continuously rebuild and redeploy your code using the "webiny watch" command.
---

import { Alert } from "@/components/Alert";

<Alert type="info" title="Can I Use This?">

The `webiny watch` command is available since Webiny **v5.5.0**.

</Alert>

<Alert type="success" title="What you'll learn">

- how to use the `webiny watch` command to develop on top of Webiny
- how the `webiny watch` command works for frontend and backend development

</Alert>

## Overview

The watch command is the number one command you'll be using while developing on top of Webiny.

It supports both frontend and backend development, and it works with the three project applications every Webiny project consists of:

1. **API** ([`apps/api`](https://github.com/webiny/webiny-js/blob/v5.40.0/packages/cwp-template-aws/template/ddb/apps/api))
2. **Admin** ([`apps/admin`](https://github.com/webiny/webiny-js/blob/v5.40.0/packages/cwp-template-aws/template/common/apps/admin))
3. **Website** ([`apps/website`](https://github.com/webiny/webiny-js/blob/v5.40.0/packages/cwp-template-aws/template/common/apps/website))

To use it, you run the `webiny watch` command followed by the project application you want to watch:

```bash
# Watches API project application.
# Code changes are automatically deployed to AWS Lambda.
yarn webiny watch api --env dev

# Watches Admin project application.
yarn webiny watch admin --env dev

# Watches Website project application.
yarn webiny watch website --env dev
```

<Alert type="info">

  Extensions are the primary way to develop on top Webiny and extend it. To learn more, check out the [Extensions](/docs/core-development-concepts/basics/extensions) article.

</Alert>

## How It Works

### Frontend Development

When it comes to frontend development (**Admin** and **Website** project applications), the watch command offers an experience similar to other existing frontend development solutions out there. Once started, the watch command:

1. spins up a local development server that serves your application
2. the application is automatically rebuilt and refreshed in the browser whenever a code change is detected 

<Alert>

  Note that you must have the **API** project application already deployed before watching **Admin** and **Website** project applications. This is because of the fact that these applications depend on Webiny's backend APIs to work as expected.

</Alert>


### Backend Development

When it comes to backend development (**API** project application), the watch command doesn't spin up a local development server, but it watches for changes and continuously deploys them the cloud (AWS Lambda). This approach sort of emulates the local development server experience, because changes are automatically reflected in the cloud (as soon as they are deployed).

<Alert type="info" title="New Watch Command Available">

  With the [5.41.0 release](/docs/release-notes/5.41.0/changelog) and with the introduction of the [New Watch Command (Local AWS Lambda Development)](/docs/release-notes/5.41.0/changelog#tag-beta-introducing-the-new-watch-command-local-aws-lambda-development-4185), we've made significant improvements to the way backend development is done. The feature is still in beta, but we encourage you to try it out and provide feedback.
 
</Alert>

## FAQ

### Can I run Webiny fully locally, without deploying it to AWS?

Because Webiny is built on top of AWS and its proprietary services, it's not possible to run Webiny fully locally. At the very least, you need to deploy the **API** project application to AWS, because it's the backbone of the entire system. The **Admin** and **Website** project applications can be run locally, but they depend on the **API** project application to work as expected. 



