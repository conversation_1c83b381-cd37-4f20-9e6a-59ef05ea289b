---
id: 59e54e70
title: How To Customize Elasticsearch Index
description: Learn how to add customizations to the Elasticsearch Index.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- how to customize Elasticsearch index for each application

</Alert>

<Alert type="danger">This article requires advanced knowledge of the Elasticsearch.</Alert>

## Overview

### Where Do We Use The Elasticsearch Index?

We use the Elasticsearch indexes for our File Manager, Form Builder, Headless CMS and Page Builder applications.

In File Manager, Form Builder and Page Builder applications we use one index per tenant and locale combination, if locale specific indexes are set.

In Headless CMS we use index for each tenant, locale and model combination because of the complexity of the data stored into Elasticsearch.

By the default, we have a basic setup for applications that use Elasticsearch:

- [common dynamic mapping for our system fields](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-elasticsearch/src/indexConfiguration/common.ts)
- [the base mapping configuration for all indexes](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-elasticsearch/src/indexConfiguration/base.ts)

Also, each of the applications has its own default plugin for the index:

- File Manager Files [plugin](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-file-manager-ddb-es/src/elasticsearch/indices/base.ts)
- Form Builder Forms and Submissions [plugin](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-form-builder-so-ddb-es/src/elasticsearch/indices/base.ts)
- Headless CMS Entries [plugin](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-headless-cms-ddb-es/src/elasticsearch/indices/base.ts)
- Page Builder Pages [plugin](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-page-builder-so-ddb-es/src/elasticsearch/indices/base.ts)

Each of those plugins define how the Elasticsearch index for that particular application will look like when it is created. The plugins use [the base mapping configuration](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-elasticsearch/src/indexConfiguration/base.ts).

<Alert type="info">
  We do not store all the data into the Elasticsearch because there is no need for it (eg. Headless
  CMS Models and Groups).
</Alert>

<Alert type="warning">
  Elasticsearch index is created when adding new locale, which also includes the initial
  installation of the system.
</Alert>

### When Would You Use The Possibility To Change The Elasticsearch Index?

Good example on when to change the default index configuration, or add a new one, is when you are using a locale which is not supported by our default template, for example: Japanese.

The Japanese Elasticsearch index configuration will look a lot different from our default one because of different text analyzers and mapping.

There is official [Elasticsearch blog](https://www.elastic.co/blog/how-to-implement-japanese-full-text-search-in-elasticsearch) on how to implement Japanese full-text search.

<Alert type="info">
  The last added plugin is always used to create the index, assuming it is passing the `.canUse()`
  check on the plugin.
</Alert>

## Available Plugins

### File Manager

For the File Manager Files we have the [FileElasticsearchIndexPlugin](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-file-manager-ddb-es/src/plugins/FileElasticsearchIndexPlugin.ts).

### Form Builder

For the Form Builder Forms and Submissions we have the [FormElasticsearchIndexPlugin](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-form-builder-so-ddb-es/src/plugins/FormElasticsearchIndexPlugin.ts).

### Headless CMS

For the Headless CMS Entries we have [CmsEntryElasticsearchIndexPlugin](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-headless-cms-ddb-es/src/plugins/CmsEntryElasticsearchIndexPlugin.ts).

### Page Builder

For the Page Builder Pages we have the [PageElasticsearchIndexPlugin](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-page-builder-so-ddb-es/src/plugins/definitions/PageElasticsearchIndexPlugin.ts).

## How To Create Your Own Index Configuration?

### Rules

There is a single rule that you will need to follow if you want to successfully create the index with your configuration:

The plugin you create will only get applied if the `.canUse()` method is passing the check.

We intended it to be used to check if the index configuration can be applied given the locale that is set at the moment index is going to be created.

<Alert type="info">
  If you want the index configuration to be always applied, no matter the locale, just return `true`
  in the `.canUse()` method in the plugin.
</Alert>

### How Are The Index Names Created?

#### Overview

Index name can contain dashes (-), underscores (\_), numbers (0-9) and english letters (a-z). It is created by our configuration method, so you do not need to worry about that.

<Alert type="warning">
  Note that each tenant, locale and CMS model combination creates it's own index.
  
  Page Builder, Form Builder, and some other apps, always create a single index per tenant and locale combination.
</Alert>

For example, creating an Article model in `root` tenant, `en-US` locale will create an index with the name: `root-headless-cms-en-us-articles`.

Creating a same model in a different tenant, with a different locale, will create a different index. For example: `another-tenant-headless-cms-de-de-articles`.

This way we ensure that data is completely separated between tenants, locales and models (or Page Builder/Form Builder/...).

##### Shared Indexes

Sometimes you might want to share the same index between multiple tenants, and that is possible via Shared Indexes.

When you turn this option on, the index name will be shared for each tenant and model combination. The language component still creates a different index for each locale.

For example, creating an Article model in the `root` tenant, `en-US` locale will create an index with the name: `root-en-us-headless-cms-articles`.
If you create an Article model in the `another-tenant` tenant, `en-US` locale, the index will be the same: `root-en-us-headless-cms-articles`.

Only if you create an Article model in another locale, the index name will be different: `root-de-de-headless-cms-articles`.
This is due to the nature of the Elasticsearch/OpenSearch field mapping and configuration for different languages.

###### **OpenSearch**

To make the `OpenSearch` share indexes, you need to add the following configuration to your `webiny.project.ts` file:
```typescript
import { createCoreApp } from "@webiny/serverless-cms-aws";

export default createCoreApp({
    pulumiResourceNamePrefix: "wby-",
    openSearch: {
        sharedIndexes: true
    }
});
```


```typescript
import { createApiApp } from "@webiny/serverless-cms-aws";

export default createApiApp({
    pulumiResourceNamePrefix: "wby-",
    openSearch: {
        sharedIndexes: true
    }
});
```

###### **Elasticsearch**

To make the `Elasticesearch` share indexes, you need to add the following configuration to your `webiny.project.ts` file:
```typescript
import { createCoreApp } from "@webiny/serverless-cms-aws";

export default createCoreApp({
    pulumiResourceNamePrefix: "wby-",
    elasticSearch: {
        sharedIndexes: true
    }
});
```


```typescript
import { createApiApp } from "@webiny/serverless-cms-aws";

export default createApiApp({
    pulumiResourceNamePrefix: "wby-",
    elasticSearch: {
        sharedIndexes: true
    }
});
```

<Alert type="danger">
  You should `NOT` change the `sharedIndexes` configuration after the initial deployment. If you do, you will lose all the data.
</Alert>

##### Index Prefix

Also, via the `webiny.application.ts` configuration file, and, more specifically, via the `indexPrefix` property in the `elasticSearch` configuration section, it's also possible to prefix all index names with a custom prefix. To learn more, check out the [Using a Shared Amazon OpenSearch Service Domain](/docs/{version}/infrastructure/basics/modify-cloud-infrastructure#using-a-shared-amazon-open-search-service-domain) section.

Use case for this would be for when you have multiple environments on a single Elasticsearch instance. You can prefix each environment indexes with, for example, `dev_`, `prod_`, `staging_`, `test_`, etc...

#### File Manager

Index name for the File Manager Files is built out of the `tenant id`, `locale code` if enabled, and `-file-manager` suffix.
If your tenant is a `root` tenant, the index will look like `root-file-manager`.
If your tenant is a `root` tenant, and you have locale (`en-US`) for index name enabled, the index will look like `root-en-us-file-manager`.

You can see how the index name is created in [this](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-file-manager-ddb-es/src/configurations.ts) file.

#### Form Builder

Index name for the Form Builder Forms and Submissions is built out of the `tenant id`, `locale code` if enabled, and `-form-builder` suffix.
If your tenant is a `root` tenant, the index will look like `root-form-builder`.
If your tenant is a `root` tenant, and you have locale (`jp`) for index name enabled, the index will look like `root-jp-form-builder`.

You can see how the index name is created in [this](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-form-builder-so-ddb-es/src/configurations.ts) file.

#### Headless CMS

Index name for the Headless CMS Entries is built out of `tenant id`, `-headless-cms`, `locale code` and `model id`.
If your tenant is a `root` tenant, locale is `en-US` and model is `Articles`, the index will look like `root-headless-cms-en-us-articles`.

You can see how the index name is created in [this](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-headless-cms-ddb-es/src/configurations.ts) file.

#### Page Builder

Index name for the Page Builder Pages is built out of the `tenant id`, `locale code` if enabled, and `-page-builder` suffix.
If your tenant is a `root` tenant, the index will look like `root-page-builder`.
If your tenant is a `root` tenant, and you have locale (`de-DE`) for index name enabled, the index will look like `root-de-de-page-builder`.

You can see how the index name is created in [this](https://github.com/webiny/webiny-js/blob/83fe117e7a7ec9f2d916850cf9fc5148a866442a/packages/api-page-builder-so-ddb-es/src/configurations.ts) file.

### Examples

Headless CMS index plugin `CmsEntryElasticsearchIndexPlugin`, which disables mappings on a `largeText` field in `de` locale:

```typescript
import { base as baseConfiguration } from "@webiny/api-elasticsearch/indexConfiguration/base";
import { CmsEntryElasticsearchIndexPlugin } from "@webiny/api-headless-cms-ddb-es/plugins/CmsEntryElasticsearchIndexPlugin";
const plugin = new CmsEntryElasticsearchIndexPlugin({
  body: {
    ...baseConfiguration,
    mappings: {
      ...baseConfiguration.mappings,
      properties: {
        ...baseConfiguration.mappings.properties,
        // we do not want to map a field with name "largeText"
        largeText: {
          enabled: false
        }
      }
    }
  },
  locales: ["de"]
});
```

Page Builder index plugin `PageElasticsearchIndexPlugin`, which disables indexing on a `largeText` field on all locales.

```typescript
import { base as baseConfiguration } from "@webiny/api-elasticsearch/indexConfiguration/base";
import { PageElasticsearchIndexPlugin } from "@webiny/api-page-builder-so-ddb-es/plugins/definitions/PageElasticsearchIndexPlugin";
const plugin = new PageElasticsearchIndexPlugin({
  order: 351,
  body: {
    ...baseConfiguration,
    mappings: {
      ...baseConfiguration.mappings,
      properties: {
        ...baseConfiguration.mappings.properties,
        // we do not want to index a field with name "largeText"
        largeText: {
          enabled: false
        }
      }
    }
  }
});
```

### Useful Links

- [Elasticsearch - Index Mapping](https://www.elastic.co/guide/en/elasticsearch/reference/7.17/mapping.html)
- [Elasticsearch - Index Settings](https://www.elastic.co/guide/en/elasticsearch/reference/7.17/index-modules.html#index-modules-settings)
