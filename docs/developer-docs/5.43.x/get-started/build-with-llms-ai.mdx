---
id: aafeab3f
title: AI Assistant and MCP Server
description: Learn how to use AI assistant and Remote MCP Server effectively in your Webiny development workflow.
---

import { Alert } from "@/components/Alert";
import { TabsComponent, TabsItem } from "@/components/TabsComponent";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to use AI assistant with Webiny documentation
- how to connect to the Remote Webiny MCP Server for enhanced AI assistance
- how to access Webiny documentation in plain text format for LLMs

</Alert>

In this chapter, you'll learn how you can use AI assistants and LLMs effectively in your Webiny development workflow.

---

## AI Assistant in Documentation

The Webiny documentation is equipped with an AI Assistant that can answer your questions and help you build customizations with Webiny.

### Open the AI Assistant

To open the AI Assistant, Click the "Ask AI" icon in the bottom right corner of the documentation.

You can then ask the AI Assistant any questions about Webiny, such as:

- What is a Webiny project application?
- How to create a custom content model?
- How to extend the GraphQL API?
- How to deploy Webiny to AWS?
- How to customize the Admin Area?

The AI Assistant will provide you with relevant documentation links, code snippets, and explanations to help you with your development.

---

## MCP Remote Server

The Webiny documentation provides a remote Model Context Protocol (MCP) server that allows you to find information from the Webiny documentation right in your IDEs or AI tools, such as Cursor.

Webiny hosts a Streamable HTTP MCP server available at `https://docs.webiny.com/mcp`. You can add it to AI agents that support connecting to MCP servers.

<TabsComponent>
  <TabsItem title="Cursor">

    [Click here](https://cursor.com/install-mcp?name=webiny&config=eyJ1cmwiOiJodHRwczovL2RvY3Mud2ViaW55LmNvbS9tY3AifQ%3D%3D) to add the Webiny MCP server to Cursor.

    To manually connect to the Webiny MCP server in Cursor, add the following to your `.cursor/mcp.json` file or Cursor settings, as explained in the [Cursor documentation](https://docs.cursor.com/context/model-context-protocol):

```json
{
  "mcpServers": {
    "webiny": {
      "url": "https://docs.webiny.com/mcp"
    }
  }
}
```

  </TabsItem>
  <TabsItem title="VSCode">

    [Click here](https://vscode.dev/redirect/mcp/install?name=webiny&config=%7B%22type%22%3A%22http%22%2C%22url%22%3A%22https%3A%2F%2Fdocs.webiny.com%2Fmcp%22%7D) to add the Webiny MCP server to VSCode.

    To manually connect to the Webiny MCP server in VSCode, add the following to your `.vscode/mcp.json` file in your workspace:

```json
{
  "servers": {
    "webiny": {
      "type": "http",
      "url": "https://docs.webiny.com/mcp"
    }
  }
}
```

    Learn more in the [VSCode documentation](https://code.visualstudio.com/docs/copilot/chat/mcp-servers).

  </TabsItem>
  <TabsItem title="Claude Desktop">

    To connect to the Webiny MCP server in Claude Desktop, add the following to your Claude Desktop configuration file:

    **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
    **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "webiny": {
      "command": "npx",
      "args": [
        "@modelcontextprotocol/server-fetch",
        "https://docs.webiny.com/mcp"
      ]
    }
  }
}
```

    Learn more in the [Claude Desktop documentation](https://docs.anthropic.com/en/docs/build-with-claude/computer-use).

  </TabsItem>
</TabsComponent>

### What the MCP Server Provides

The Webiny MCP server gives your AI assistant access to:

- **Complete Webiny documentation** - All developer guides, tutorials, and reference materials
- **Code examples** - Real-world code snippets and implementation patterns
- **API references** - GraphQL schemas, REST endpoints, and SDK documentation
- **Architecture guides** - Cloud infrastructure patterns and deployment strategies
- **Best practices** - Recommended approaches for building with Webiny

With the MCP server connected, you can ask your AI assistant questions like:

- "How do I create a custom GraphQL resolver in Webiny?"
- "Show me how to extend the Headless CMS with custom fields"
- "What's the best way to deploy a Webiny project to production?"
- "How do I implement custom authentication in Webiny?"

The AI assistant will have access to the most up-to-date Webiny documentation to provide accurate and relevant answers.


## FAQ

### Can I use the MCP server with any AI assistant?

The MCP server works with any AI assistant or tool that supports the Model Context Protocol. This includes popular tools like Cursor, VSCode with Copilot, Claude Desktop, and many others.

### Is the Webiny MCP server free to use?

Yes, the Webiny MCP server is free to use and provides access to all public Webiny documentation.

### How often is the MCP server updated?

The MCP server is automatically updated whenever the Webiny documentation is updated, ensuring you always have access to the latest information.
