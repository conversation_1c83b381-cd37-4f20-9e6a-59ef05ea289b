---
id: aafeab3f
title: AI Assistant and MCP Server
description: Learn how to use AI assistant and Remote MCP Server effectively in your Webiny development workflow.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to use AI assistant with Webiny documentation
- how to connect to the Remote Webiny MCP Server for enhanced AI assistance

</Alert>

In this chapter, you'll learn how you can use AI assistants and Remote MCP Server effectively in your Webiny development workflow.

## AI Assistant in Documentation

The Webiny documentation is equipped with an AI Assistant that can answer your questions and help you build customizations with Webiny. To open the AI Assistant, Click the "Ask AI" button in the bottom right corner of the documentation.

You can then ask the AI Assistant any questions about Webiny and the AI Assistant will provide you with relevant documentation links, code snippets, and explanations to help you with your development.

## Webiny MCP Server

The Webiny documentation provides a remote Model Context Protocol (MCP) server that allows you to find information from the Webiny documentation right in your IDEs or AI tools, such as Cursor.

Webiny hosts a Remote MCP server available at https://webiny-mcp-server.developers-d83.workers.dev/sse  
You can add it to AI agents that support connecting to MCP servers.

### Cursor

[Click here](https://cursor.com/install-mcp?name=webiny&config=eyJ1cmwiOiJodHRwczovL3dlYmlueS1tY3Atc2VydmVyLmRldmVsb3BlcnMtZDgzLndvcmtlcnMuZGV2L3NzZSJ9) to add the Webiny MCP server to Cursor.

To manually connect to the Webiny MCP server in Cursor, add the following to your `.cursor/mcp.json` file or Cursor settings, as explained in the [Cursor documentation](https://docs.cursor.com/context/model-context-protocol):

```json
{
  "mcpServers": {
    "webiny": {
      "url": "https://webiny-mcp-server.developers-d83.workers.dev/sse"
    }
  }
}
```

### VSCode

[Click here](https://vscode.dev/redirect/mcp/install?name=webiny&config=%7B%22type%22%3A%22http%22%2C%22url%22%3A%22https%3A%2F%2Fwebiny-mcp-server.developers-d83.workers.dev%2Fsse%22%7D) to add the Webiny MCP server to VSCode.

To manually connect to the Webiny MCP server in VSCode, add the following to your `.vscode/mcp.json` file in your workspace:

```json
{
  "servers": {
    "webiny": {
      "type": "http",
      "url": "https://webiny-mcp-server.developers-d83.workers.dev/sse"
    }
  }
}
```

Learn more in the [VSCode documentation](https://code.visualstudio.com/docs/copilot/chat/mcp-servers).

## FAQ

### Can I use the MCP server with any AI assistant?

The MCP server works with any AI assistant or tool that supports the Model Context Protocol. This includes popular tools like Cursor, VSCode with Copilot, Claude Desktop, and many others.

### Is the Webiny MCP server free to use?

Yes, the Webiny MCP server is free to use and provides access to all public Webiny documentation.

### How often is the MCP server updated?

The MCP server is automatically updated whenever the Webiny documentation is updated, ensuring you always have access to the latest information.
