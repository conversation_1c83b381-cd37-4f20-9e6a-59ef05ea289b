---
id: 8a6fo7e4
title: Built-in Background Tasks
description: You will learn about Background Tasks, how to create new definitions, how to trigger them and how to handle the task run.
---

import { Alert } from "@/components/Alert";
import { CanIUseThis } from "@/components/CanIUseThis";
import { WhatY<PERSON><PERSON>illLearn } from "@/components/WhatYouWillLearn";

<CanIUseThis since={"5.39.0"} />

<WhatYouWillLearn>

- what are available built-in background tasks
- how to run built-in background tasks

</WhatYouWillLearn>

## Built-in Background Tasks

The following is a list of background tasks that are included in Webiny by default.

### Amazon OpenSearch - Reindexing Background Task

<Alert type="info">

  This task can be used with the Amazon DynamoDB + Amazon OpenSearch [database setup](/docs/architecture/introduction#different-database-setups). It can be also used with the legacy Amazon DynamoDB + Amazon Elasticsearch setup.
 
</Alert>

The **Reindexing Task** is a background task that scans through the DynamoDB Elasticsearch table and pushes the data into the specific Elasticsearch/OpenSearch index.
Internally it is a bit more than that, but this is the basic idea. You can check the [definition](https://github.com/webiny/webiny-js/blob/abb442c6d67c97980b8053c5e53db7fb4fec88b4/packages/api-elasticsearch-tasks/src/tasks/reindexing/reindexingTaskDefinition.ts#L12) and the [runner](https://github.com/webiny/webiny-js/blob/abb442c6d67c97980b8053c5e53db7fb4fec88b4/packages/api-elasticsearch-tasks/src/tasks/reindexing/ReindexingTaskRunner.ts#L40) code for more information.

The task can be triggered either via code or via the GraphQL API.

##### Triggering the Task Via Code

Let's say that you have a Page Builder lifecycle event hook which triggers the task when the page is published, but only on index which contains the word `page` in its name.

```typescript
const myHook = () => {
  return new ContextPlugin(async context => {
    context.pageBuilder.onPageAfterPublish(async () => {
      await context.tasks.trigger({
        definition: "elasticsearchReindexing",
        input: {
          matching: "page"
        }
      });
    });
  });
};
```

##### Triggering the Task Via GraphQL API

The task can be triggered via the GraphQL API, with the following mutation:

```graphql
mutation TriggerTask {
  backgroundTasks {
    triggerTask(definition: elasticsearchReindexing, input: { matching: "page" }) {
      data {
        id
      }
      error {
        message
        code
        data
        stack
      }
    }
  }
}
```

The task disables indexing on all matching indexes, and enables it when the task is finished.
This is done to reduce the strain on the Elasticsearch/OpenSearch cluster as the reindexing operation is a heavy one.

### Amazon OpenSearch - Create Index Background Task

<Alert type="info">

  This task can be used with the Amazon DynamoDB + Amazon OpenSearch [database setup](/docs/architecture/introduction#different-database-setups). It can be also used with the legacy Amazon DynamoDB + Amazon Elasticsearch setup.

</Alert>

The **Create Index** background task is a task which creates all missing indexes in the Amazon OpenSearch cluster.

The task goes through all the CMS Models and creates indexes which do not exist.
The task also creates Form Builder and Page Builder indexes.

The task can be triggered either via code or via the GraphQL API.

##### Triggering the Task Via GraphQL API

```graphql
mutation TriggerCreateMissingIndexesTask {
  backgroundTasks {
    triggerTask(
      definition: elasticsearchCreateIndexes
      input: {
        matching: "string matching your index, or nothing" # optional
      }
    ) {
      data {
        id
      }
      error {
        message
        code
        data
        stack
      }
    }
  }
}
```

Then you can check the task status and output with the following query:

```graphql
query GetCreateIndexTask {
  backgroundTasks {
    getTask(id: "id you got from task trigger") {
      data {
        id
        name
        taskStatus
        output
      }
      error {
        message
        code
        data
        stack
      }
    }
  }
}
```
