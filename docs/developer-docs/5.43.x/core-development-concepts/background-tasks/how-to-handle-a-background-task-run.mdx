---
id: 8d6fo7e4
title: How to Handle a Background Task
description: You will learn about Background Tasks, how to create new definitions, how to trigger them and how to handle the task run.
---

import {Alert} from "@/components/Alert";
import {CanIUseThis} from "@/components/CanIUseThis";
import {WhatY<PERSON><PERSON>illLearn} from "@/components/WhatYouWillLearn";

<CanIUseThis since={"5.39.0"} />

<WhatYouWillLearn>

- how to handle the Background Task run
- what are available methods
- how to log important information

</WhatYouWillLearn>

## Handling The Task Run

When you define the task, you need to provide the `run` method, as it is the method getting executed when the task is being executed.
The `run` method receives an object parameter with the following variables, defined in the interface [`ITaskRunParams`](https://github.com/webiny/webiny-js/blob/abb442c6d67c97980b8053c5e53db7fb4fec88b4/packages/tasks/src/types.ts#L265) :

- `context`
- `input`
- `response`
- `isCloseToTimeout()`
- `isAborted()`
- `store`

### The `context` Object

Via the `context` object, you can access whole Webiny system.
The base interface of the `context` object is [Context](https://github.com/webiny/webiny-js/blob/abb442c6d67c97980b8053c5e53db7fb4fec88b4/packages/tasks/src/types.ts#L261), but you can pass your own, which must extend the base interface.

You can pass your own Ge interface/type of the `context` variable when defining the task:
```typescript
import {createTaskDefinition} from "@webiny/tasks";
import {MyCustomContext} from "./types";

const myTask = createTaskDefinition<MyCustomContext>({
    id: "myPublicTask",
    title: "A Task Accessible Via API",
    async run({context}) {
        // context is of type MyCustomContext
        await context.yourCustomMethod();
    }
});
```

### The `input` Object

The `input` object is the input which was sent to the task when it was triggered.

By default, it is of plain object type `Record<string, any>`, but you can pass your own type/interface when defining the task:
```typescript
import {createTaskDefinition} from "@webiny/tasks";
import {MyCustomContext} from "./types";

interface MyCustomInput {
    myCustomProperty: string;
}

const myTask = createTaskDefinition<MyCustomContext, MyCustomInput>({
    id: "myPublicTask",
    title: "A Task Accessible Via API",
    async run({input}) {
        // input is of type MyCustomInput
        const {myCustomProperty} = input;
    }
});
```

### The `response` Object

The `response` object handles the output from the task run.
Available methods are:
- done
- continue
- error
- aborted

#### The `done` Method

This method signalizes that the task has finished its job and that the Step Function will finish as well.
It also signalizes that task status should be set to `success`. Optional message will be stored as well.
```typescript
import {createTaskDefinition} from "@webiny/tasks";

const myTask = createTaskDefinition({
    id: "myPublicTask",
    title: "A Task Accessible Via API",
    async run({response}) {
        return response.done("Optional message about the task getting done.");
    }
});
```

Interface for the response object is defined [here](https://github.com/webiny/webiny-js/blob/84d28b3e100a0317a0c3d265d06efaea50971cd3/packages/tasks/src/response/abstractions/TaskResponse.ts#L62).
- `message` - optional message you want to store when the task is done
- `output` - optional output object you want to store when the task is done


#### The `continue` Method

This method signalizes that the task has not finished its job and that the Step Function should continue running the Lambda handler.

Note that the `continue` method MUST receive some data, which is of same type as the input variable, as the first parameter.

Why? Because the data you send into `continue` is the data you will receive on the next iteration of the task run as the `input` variable.

The `continue` method accepts a second, optional, parameter via which you can set waiting time for the next task run.
You can either send a `seconds` property, which takes a number, or a `date` property, which takes a `Date` object.

```typescript
import {createTaskDefinition} from "@webiny/tasks";

const myTask = createTaskDefinition({
    id: "myPublicTask",
    title: "A Task Accessible Via API",
    async run({input, response}) {
        return response.continue({
            ...input,
            aChangedInputProperty: 1,
        },
            // optional options
        {
            seconds: 30, // wait 30 seconds before the next task run
            date: new Date("2024-02-25T00:00:00Z") // wait until the specified date before the next task run
        });
    }
});
```

<Alert type="danger">
  If you are setting waiting time for the `continue` method, note that the maximum waiting time is 355 days, which is almost the maximum life time for the AWS Step Function.
</Alert>

#### The `error` Method

This method signalizes that the task has finished its job with an error and that the Step Function will finish as well, in an error state.
It also signalizes that task status should be set to `error`.
```typescript
import {createTaskDefinition} from "@webiny/tasks";

const myTask = createTaskDefinition({
    id: "myPublicTask",
    title: "A Task Accessible Via API",
    async run({response}) {
        return response.error({
            message: "Error message",
            code: "ERROR_CODE",
            data: {
                // optional data
            }
        });
    }
});
````

#### The `abort` Method

This method signalizes that the task was aborted by the user. Note that this is not an error, but a user triggered abort.

When you write your code, you must check if the task was aborted via the `isAborted()` method, and if it was, you must return the `abort` response.
This is meant to be used while the task has some code which loops continuously, like reading a lot of records from the database, with paginating through the records.

```typescript
import {createTaskDefinition} from "@webiny/tasks";

const myTask = createTaskDefinition({
    id: "myPublicTask",
    title: "A Task Accessible Via API",
    async run({response, isAborted, input}) {
        let dbReadParams = {
            ...input,
        };
        let result: DbResponse | null = null;
        while ((result = await listFromDb(dbReadParams))) {
          if (isAborted()) {
              return response.abort();
          }
          // continue with the loop
        }
        return response.done();
    }
});
````

### The `isCloseToTimeout` Method

The `isCloseToTimeout` method is a method which returns a boolean value, which tells you if the Lambda instance is close to the timeout.
This is useful when you have a lot of code to run, or you have some idea that the code will take some time to execute, and you want to check if you have enough time to finish the code execution.

```typescript
import {createTaskDefinition} from "@webiny/tasks";

interface MyInput {
    after?: null | undefined;
}

const myTask = createTaskDefinition<Context, MyInput>({
    id: "myPublicTask",
    title: "A Task Accessible Via API",
    async run({response, isCloseToTimeout, input}) {
        let dbReadParams = {
            ...input,
        };
        let result: DbResponse;
        while ((result = await listFromDb(dbReadParams))) {
          // do your magic...
          
          // assign the cursor to the after property of the dbReadParams
          dbReadParams.after = result.cursor;
          if (isCloseToTimeout()) {
              // on next iteration, we want to continue with new dbReadParams 
              return response.continue(dbReadParams);
          }
          // continue with the loop
        }
        return response.done();
    }
});
```

The `isCloseToTimeout` method accepts an optional parameter, which is a number of seconds under which will this method return `true`. The default is `180` seconds.

<Alert type="warning">

  As a developer, you are responsible for checking if the task is close to the timeout, and for handling the task run accordingly.

  Webiny provides you with the background task mechanism, but you must handle the task run, and timeouts, yourself.

</Alert>

### The `isAborted` Method

The `isAborted` method is a method which returns a boolean value, which tells you if the task was aborted by the user.

```typescript
import {createTaskDefinition} from "@webiny/tasks";

const myTask = createTaskDefinition({
    id: "myPublicTask",
    title: "A Task Accessible Via API",
    async run({response, isAborted, input}) {
        let dbReadParams = {
            ...input,
        };
        let result: DbResponse | null = null;
        while ((result = await listFromDb(dbReadParams))) {
          if (isAborted()) {
              return response.abort();
          }
          // continue with the loop
        }
        return response.done();
    }
});
````

### The `store` Object

The `store` object is of `ITaskManagerStore` interface type, which is defined [here](https://github.com/webiny/webiny-js/blob/abb442c6d67c97980b8053c5e53db7fb4fec88b4/packages/tasks/src/runner/abstractions/TaskManagerStore.ts#L42).

It is used for storing and retrieving data from the task run, including task logs.

Available methods on the object are:
- `getTask`
- `getStatus`
- `updateTask`
- `updateInput`
- `getInput`
- `addInfoLog`
- `addErrorLog`

## Logging

Using the `addInfoLog` and `addErrorLog` methods, you can store some important log into the database.

Because the Background Task Lambda handler can be executed multiple times, depending on the developer response, the logging has been implemented on per iteration base.

It means that each of the execution iteration gets its own record, which contain individual logs from that execution iteration.

<Alert type="warning" title="Logging">

  Do not use the `addInfoLog` and `addErrorLog` methods for a lot of logging, or logging large amounts of data.
  
  The logs are stored in the database as you send them, and if you send a lot of logs, or large logs, you will hammer the database.
  
  Use the built-in logs only for important information, like starting or finishing a part of a task.

</Alert>
