---
id: 3c91ab47
title: Confirmation Dialog for Folder Drag & Drop
description: Learn how to prevent accidental folder moves.
---

import { CanIUseThis } from "@/components/CanIUseThis";
import { Image } from "@/components/Image";
import { WhatYouWillLearn } from "@/components/WhatYouWillLearn";

import folderDropConfirmation from "./assets/hcms-folder-drop-confirmation.gif";


<CanIUseThis since={"5.43.2"} />

<WhatYouWillLearn>

  - how to prevent accidental folder moves

</WhatYouWillLearn>

To help prevent accidental folder moves, Webiny introduces a confirmation dialog that can be enabled through configuration.

```tsx
import { ContentEntryListConfig } from "@webiny/app-headless-cms";

// You can destructure child components to make the code more readable and easier to work with.
const { Browser } = ContentEntryListConfig;

<ContentEntryListConfig>
    <Browser.Folder.DropConfirmation value={true} modelIds={["article"]} />
</ContentEntryListConfig>
```

By declaring the `modelIds` prop, you can enable folder move confirmation only for specific content models. If omitted, the confirmation will apply to all models.

When active, this feature prompts users to confirm their action before moving a folder from one location to another. 

<Image src={folderDropConfirmation} title={"Confirm folder move: prompt appears before completing drag-and-drop."} />



