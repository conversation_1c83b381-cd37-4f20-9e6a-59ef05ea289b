---
id: 5f147eea
title: Welcome to Webiny Docs
description: Learn how to install Webiny and customize its capabilities as a developer.
pageHeader: false
fullWidth: true
---

import { <PERSON> } from "@/components/landing-pages/Hero";
import { Divider } from "@/components/landing-pages/Divider";
import { InstallWebinyBanner } from "@/components/landing-pages/InstallWebinyBanner";
import { FeatureList } from "@/components/landing-pages/FeatureList";
import { Section, SectionTitle, SectionRow, SectionBox } from "@/components/landing-pages/Section";

import { ReactComponent as WhyWebinyIcon } from "./assets/feature-icons/why-webiny.svg";
import { ReactComponent as SecurityIcon } from "./assets/feature-icons/security.svg";
import { ReactComponent as InfrastructureIcon } from "./assets/feature-icons/infrastructure.svg";
import { ReactComponent as MultiTenancyIcon } from "./assets/feature-icons/multi-tenancy.svg";
import { ReactComponent as FrameworkIcon } from "./assets/feature-icons/framework.svg";
import { ReactComponent as SelfHostingIcon } from "./assets/feature-icons/selfhosting.svg";

import { ReactComponent as HeadlessCmsIcon } from "./assets/product-icons/headless-cms.svg";
import { ReactComponent as PageBuilderIcon } from "./assets/product-icons/page-builder.svg";
import { ReactComponent as FileManagerIcon } from "./assets/product-icons/file-manager.svg";
import { ReactComponent as FormBuilderIcon } from "./assets/product-icons/form-builder.svg";
import { ReactComponent as ApwIcon } from "./assets/product-icons/apw.svg";
import { ReactComponent as AdminIcon } from "./assets/product-icons/admin.svg";

import { ReactComponent as VideoIcon } from "./assets/development-icons/video.svg";
import { ReactComponent as ConceptsIcon } from "./assets/development-icons/core-concepts.svg";
import { ReactComponent as CustomizeIcon } from "./assets/development-icons/customize.svg";
import { ReactComponent as CiCdIcon } from "./assets/development-icons/ci-cd.svg";
import { ReactComponent as SecurityFrameworkIcon } from "./assets/development-icons/security.svg";
import { ReactComponent as BgTasksIcon } from "./assets/development-icons/background-tasks.svg";

import { ReactComponent as UserGuidesIcon } from "./assets/resources-icons/user-guides.svg";
import { ReactComponent as ReleaseNotesIcon } from "./assets/resources-icons/release-notes.svg";
import { ReactComponent as CompanyHandbookIcon } from "./assets/resources-icons/company-handbook.svg";
import { ReactComponent as CommunityIcon } from "./assets/resources-icons/community.svg";

<Hero />
<Divider />
<InstallWebinyBanner />

<FeatureList
  items={[
    {
      label: "Why & when to use Webiny?",
      link: "/docs/overview/why-and-when-to-use-webiny",
      icon: WhyWebinyIcon
    },
    {
      label: "Security overview",
      link: "/docs/overview/features/security",
      icon: SecurityIcon
    },
    {
      label: "Serverless infrastructure",
      link: "/docs/overview/features/serverless-infrastructure",
      icon: InfrastructureIcon
    },
    {
      label: "Multi-tenancy",
      link: "/docs/overview/features/multi-tenancy",
      icon: MultiTenancyIcon
    },
    {
      label: "Development framework",
      link: "/docs/overview/features/framework",
      icon: FrameworkIcon
    },
    {
      label: "Self-hosting",
      link: "/docs/overview/features/self-hosted",
      icon: SelfHostingIcon
    }
  ]}
/>

<Divider />

<Section>
  <SectionTitle
    title="Products"
    subText="Learn about how to customize and build on top of existing Webiny products."
    categoryTitle="Products"
  />
  <SectionRow>
    <SectionBox title="Headless CMS" Icon={HeadlessCmsIcon} learnMoreLink="/docs/headless-cms/overview">
      <p>
        Learn how to create plugins and customize the Headless CMS experience to your content needs. From extending the built-in GraphQL capabilities to modifying the editorial experience. 
      </p>
    </SectionBox>
    <SectionBox title="Page Builder" Icon={PageBuilderIcon} learnMoreLink="/docs/page-builder/overview">
      <p>
        Learn how to introduce custom dynamic and interactive elements to the Page Builder editor, how to theme the layouts to your brand and how to modify the editor UI to better match the needs of your content creators.
      </p>
    </SectionBox>
  </SectionRow>
  <SectionRow>
    <SectionBox title="File Manager" Icon={FileManagerIcon} learnMoreLink="/docs/file-manager/extending/create-a-file-type-plugin">
      <p>
        Webiny's File Manager is a scalable DAM solution - in this section, you'll learn how you can add additional meta information to your file entries, introduce support for new file types, or even replace the File Manager with a custom DAM. 
      </p>
    </SectionBox>
    <SectionBox title="Form Builder" Icon={FormBuilderIcon} learnMoreLink="/docs/form-builder/theming/introduction">
      <p>
        In this section, you'll learn how to customize the layout and visual aspects of your forms.
      </p>
    </SectionBox>
    <SectionBox title="Admin Area" Icon={AdminIcon} learnMoreLink="/docs/admin-area/basics/framework">
      <p>
        The Admin Area is an app inside which all other apps live. You can white-label the UI to match the brand of your customer, introduce new apps into the menu, modify the start page, and more. 
      </p>
    </SectionBox>
  </SectionRow>
</Section>

<Divider />

<Section>
  <SectionTitle
    title="Development with Webiny"
    subText="If you're only starting your development journey with Webiny, make sure to check out these resources."
    categoryTitle="Development"
  />
  <SectionRow>
    <SectionBox title="Video training course" Icon={VideoIcon} learnMoreLink="https://www.youtube.com/playlist?list=PL9HlKSQaEuXTGvfbFiBg0u_gOhphvof2r">
      <p>
        For developers just getting started with Webiny, we've prepared a short onboarding video course to help you take your first step in development with Webiny.
      </p>
    </SectionBox>
    <SectionBox title="Core development concepts" Icon={ConceptsIcon} learnMoreLink="/docs/core-development-concepts/basics/tools-and-libraries">
      <p>
        Once you're past the basics of how to develop with Webiny, this category of articles is a good next step. 
      </p>
      <p>
        You'll learn about the core concepts such as the watch command, project organization, the CLI utility and more.
      </p>
    </SectionBox>
    <SectionBox title="Customize and extend" Icon={CustomizeIcon} learnMoreLink="/docs/core-development-concepts/extending-and-customizing/extend-graphql-api">
      <p>
        Learn how to extend the GraphQL API, CLI utility, add custom routes to the Lambda function, intercept requests or customize the built-in Lexical editor.
      </p>
    </SectionBox>
  </SectionRow>
  <SectionRow>
    <SectionBox title="CI/CD setup" Icon={CiCdIcon} learnMoreLink="/docs/core-development-concepts/ci-cd/introduction">
      <p>
        If you need to configure and deploy Webiny from a CI/CD environment, follow this guide.
      </p>
    </SectionBox>
    <SectionBox title="Security Framework" Icon={SecurityFrameworkIcon} learnMoreLink="/docs/core-development-concepts/security-framework/introduction">
      <p>
        Webiny has a simple, but powerful security framework that allows you to integrate with different SSO systems as well as customize the authentication and authorization flows. 
      </p>
    </SectionBox>
    <SectionBox title="Background tasks" Icon={BgTasksIcon} learnMoreLink="/docs/core-development-concepts/background-tasks/about-background-tasks">
      <p>
        Webiny supports long-running tasks that are executed in the background. In this set of articles, you'll learn how to leverage this mechanism inside your own custom plugins and apps built inside Webiny. 
      </p>
    </SectionBox>
  </SectionRow>
</Section>

<Divider />

<Section>
  <SectionTitle
    title="Additional resources"
    subText="Check out some of the additional resources to learn more and to engage closer with our community."
    categoryTitle="Resources"
  />
  <SectionRow>
    <SectionBox title="User guides" Icon={UserGuidesIcon} learnMoreLink="/docs/user-guides/overview">
      <p>
        Guides to help non-technical users master Webiny and many of its features.
      </p>
    </SectionBox>
    <SectionBox title="Release notes" Icon={ReleaseNotesIcon} learnMoreLink="/docs/release-notes/upgrade-webiny">
      <p>
        Check out the latest updates and how to upgrade Webiny to the latest version.
      </p>
    </SectionBox>
  </SectionRow>
  <SectionRow>
    <SectionBox title="Company handbook" Icon={CompanyHandbookIcon} learnMoreLink="/docs/handbook/company/why-webiny-exists">
      <p>
        We are a company made of brilliant individuals. If you want to learn more about what drives us and we think about the purpose of Webiny, check out our company handbook.
      </p>
    </SectionBox>
    <SectionBox title="Community" Icon={CommunityIcon} learnMoreLink="/slack">
      <p>
        We're proud to be an open-source project and our community is a great place to connect with other like-minded individuals that are using Webiny.
      </p>
    </SectionBox>
  </SectionRow>
</Section>
