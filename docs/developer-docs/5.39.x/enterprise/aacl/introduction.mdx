---
id: aafea432
title: Introduction
description: Learn what are the three tiers of Webiny's security layer.
---

import aaclCustomAccess from "./assets/aacl-custom-access.png";
import aaclCustomAccessUpgrade from "./assets/aacl-custom-access-upgrade.png";

<CanIUseThis enterprise={true} since={"5.37.0"} />

<WhatYouWillLearn>

- what are the three tiers of Webiny's security layer

</WhatYouWillLearn>

## The Three Tiers of Webiny's Security Layer

Webiny's security layer is divided into three tiers. The higher the tier, the more features are available.

All Webiny projects start with the **Open Source** tier. The tier is free to use but is limited when it comes to defining fine-grained permissions, allowing only the **No Access** and **Full Access** to be selected when defining permissions for individual Webiny apps.

Trying to select **Custom Access** will result in an alert message being shown, informing the user that the feature is only available with the Advanced Access Control Layer (AACL), which is available on the **Business** and **Enterprise** tiers.

<Image src={aaclCustomAccessUpgrade} title={"Selecting Custom Access Level on Open Source Tier"} />

To upgrade to **Business** tier, users [link their project](/docs/{version}/wcp/link-a-project) with [Webiny Control Panel (WCP)](/docs/{version}/wcp/overview), from where they can activate the Advanced Access Control Layer (AACL) for their project. By doing this, users will be able to define fine-grained permissions for individual Webiny apps.

<Image
  src={aaclCustomAccess}
  title={"Selecting Custom Access with Advanced Access Control Layer (AACL) Enabled"}
/>

Finally, for the most advanced use cases, users can upgrade to the **Enterprise** tier. On top of the features available with the first two tiers, the Enterprise tier introduces **Teams**, **Folder Level Permissions** and **Private Files**.

More on this in the following section.
