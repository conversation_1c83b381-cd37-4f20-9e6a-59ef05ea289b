---
id: f9da9454
title: Private files
description: Learn about the Private Files feature and how to use it.
---

import pf from "./assets/private-files.png";
import { Alert } from "@/components/Alert";

<CanIUseThis enterprise={true} since={"5.39.0"} />

<WhatYouWillLearn>

- an overview of the Private Files feature and how to use it

</WhatYouWillLearn>

## Overview

With the 5.39.0 release, Webiny received a feature that enables you to control who can see and access files inside File Manager. This feature is designed to protect highly sensitive files from leaking or being publicly shared. 

<Image src={pf} alt={"Private Files"} shadow={false} />

With this feature, after uploading a file inside the File Manager, users can set the Access Control setting on the newly uploaded file. The Access Control setting can take two values: 
- **Public** -> Anyone on the public internet can access the file given the link to the file
- **Private** -> Only registered Webiny Admin users can access and view the file

Once a file is marked as `Private` even if a direct link to the file is shared with 3rd party users, they will not be able to access the file. The feature works regardless of the file type. You can protect images, documents, videos or any other file type.

<Alert type="warning">
It's important to note that public files are automatically cached on the CDN and in the browser. In case you switch a file from public to private, the CDN cache will be flushed, but users who have previously accessed the file might still have it in their browser cache.
</Alert>




