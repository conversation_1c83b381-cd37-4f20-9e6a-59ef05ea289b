---
id: ab399897
title: Webpack Config
description: Learn how to modify webpack config of the Admin app.
---

import { CanIUseThis } from "@/components/CanIUseThis";
import { WhatYouWillLearn } from "@/components/WhatYouWillLearn";

<CanIUseThis since={"5.39.0"} />

<WhatYouWillLearn>

- how to modify Webpack config of the Admin app

</WhatYouWillLearn>

## Overview

Admin app is built using a Webpack config derived from a very popular `create-react-app` boilerplate for React apps. Most of what you need is already covered by the config. However, if you need to modify our default config, there's a simple way to access and modify the default config.

## Modify Webpack Config

```ts apps/admin/webiny.config.ts
import { createAdminAppConfig } from "@webiny/serverless-cms-aws";

export default createAdminAppConfig(({ config }) => {
  config.webpack(config => {
    // Add your modifications here, and return the config object.
    return config;
  });
});
```
