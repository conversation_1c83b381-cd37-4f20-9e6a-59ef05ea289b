---
id: cbc49e38
title: Customize File List Actions
description: Learn how to add, replace, or remove actions in the File List.
---

import fmAddFileAction from "./assets/fm-add-file-action.gif";
import fmAddFolderAction from "./assets/fm-add-folder-action.gif";
import fmDefaultActions from "./assets/fm-default-actions.gif";
import fmPositionFileActions from "./assets/fm-position-file-action.png";
import fmPositionFolderActions from "./assets/fm-position-folder-action.png";
import fmDiscoverFileActions from "./assets/fm-discover-file-actions.png";
import fmDiscoverFolderActions from "./assets/fm-discover-folder-actions.png";

import { Alert } from "@/components/Alert";

<Alert type="info" title="Can I Use This?">

This feature has been available since Webiny **v5.39.0**.

</Alert>

<Alert type="success" title="What you will learn">

- how to add a custom action to the File List
- how to discover existing action names
- how to change the position, remove, or replace an existing action

</Alert>

## Overview

In File Manager, pre-built actions empower users to edit, move or delete files and folders directly from the file list table.

There are two distinct types of actions: `FolderAction`, which is designed specifically for folders, and `FileAction`, which is designed specifically for individual file entries.

<Image src={fmDefaultActions} title="Default table actions" />

## Using the code examples

The following code examples follow our usual configuration pattern. You must add the code from the examples to your `apps/admin/src/App.tsx`. Here's an example:

```diff-tsx apps/admin/src/App.tsx
import React from "react";
import { Admin } from "@webiny/app-serverless-cms";
import { Cognito } from "@webiny/app-admin-users-cognito";
+ import { FileManagerViewConfig } from "@webiny/app-file-manager";
import "./App.scss";

+ // You can destructure config components to make the code more readable and easier to work with.
+ const { Browser } = FileManagerViewConfig;

export const App = () => {
    return (
      <Admin>
        <Cognito />
+       <FileManagerViewConfig>
+         {* Config components go here. *}
+       </FileManagerViewConfig>
      </Admin>
    );
};
```

## Folder Actions

### Add a Folder Action

To add a new action, use the `Browser.FolderAction` component and mount it within your Admin app. This component will serve as the foundation for your action.

To help developers keep the UI consistent, you'll find a handy `OptionsMenuItem` component, inheriting UI rules and guidelines from the Webiny core package.

Here is an example of creating a folder action component that copies the selected folder to the clipboard in JSON format.

```tsx CopyFolderData.tsx
import React from "react";
import { ReactComponent as CopyIcon } from "@material-design-icons/svg/outlined/content_copy.svg";
import { useFolder } from "@webiny/app-aco";
import { useSnackbar } from "@webiny/app-admin";
import { FileManagerViewConfig } from "@webiny/app-file-manager";

export const CopyFolderData = () => {
  const { folder } = useFolder();
  const { showSnackbar } = useSnackbar();
  const { OptionsMenuItem } = FileManagerViewConfig.Browser.FolderAction;

  const copyJson = () => {
    navigator.clipboard.writeText(JSON.stringify(folder, null, 2));
    showSnackbar("JSON data copied to clipboard.");
  };

  if (!folder) {
    return null;
  }

  return <OptionsMenuItem icon={<CopyIcon />} label={"Copy as JSON"} onAction={copyJson} />;
};
```

You can pass the custom component to the folder action definition using the `element` prop.

```tsx
  <FileManagerViewConfig>
    <Browser.FolderAction
      name={"copy-json"}
      element={<CopyFolderData />}
    /> 
  </FileManagerViewConfig> 
```

This is the whole process of registering a new folder action element.

<Image src={fmAddFolderAction} title="Custom Folder Action" />


### Discover Folder Actions

This section demonstrates how you can discover the names of existing folder actions. This is important for further sections on positioning, removing, and replacing existing actions.

The easiest way to discover existing folder actions is to use your browser's React Dev Tools plugins and search for the `BaseFolderAction`:

<Image src={fmDiscoverFolderActions} title="Discover Existing Folder Actions" />

### Position a Folder Action

To position your custom folder action before or after an existing action, you can use the `before` and `after` props on the `<Browser.FolderAction>` element:

```tsx
<FileManagerViewConfig>
  <Browser.FolderAction 
    name={"copy-json"}
    element={<CopyFolderData />}
    before={"delete"}
  />
</FileManagerViewConfig>
```

<Image src={fmPositionFolderActions} title="Position Your Action Before Another Action" />

### Remove a Folder Action

You may want to remove an existing action. All you need to do is reference the action by name and pass a `remove` prop to the `<Browser.FolderAction>` element:

```tsx
<FileManagerViewConfig>
  <Browser.FolderAction name={"delete"} remove />
</FileManagerViewConfig>
```

### Replace a Folder Action

To replace an existing action with a new action element, you need to reference an existing action by name and pass a new component via the `element` prop:

```tsx
<FileManagerViewConfig>
  <Browser.FolderAction
    name={"delete"}
    element={<span>A new action!</span>}
  />
</FileManagerViewConfig>
```

## File Actions

### Add a File Action

To add a new action, use the `Browser.FileAction` component and mount it within your Admin app. This component will serve as the foundation for your action.

To ensure consistency in the UI, developers can use two components that inherit rules and guidelines from the Webiny core package:

- `OptionsMenuItem`: triggers a defined action
- `OptionsMenuLink`: takes the user to a new location, such as a new web page

Here is an example of creating a file action component that copies the selected file to the clipboard in JSON format.

```tsx CopyFileData.tsx
import React from "react";
import { ReactComponent as CopyIcon } from "@material-design-icons/svg/outlined/content_copy.svg";
import { useSnackbar } from "@webiny/app-admin";
import { FileManagerViewConfig, useFile } from "@webiny/app-file-manager";

export const CopyFileData = () => {
  const { file } = useFile();
  const { showSnackbar } = useSnackbar();
  const { OptionsMenuItem } = FileManagerViewConfig.Browser.FileAction;

  const copyJson = () => {
    navigator.clipboard.writeText(JSON.stringify(file, null, 2));
    showSnackbar("JSON data copied to clipboard.");
  };

  if (!file) {
    return null;
  }

  return <OptionsMenuItem icon={<CopyIcon />} label={"Copy as JSON"} onAction={copyJson} />;
};
```

You can pass the custom component to the file action definition using the `element` prop.

```tsx
  <FileManagerViewConfig>
    <Browser.FileAction
      name={"copy-json"}
      element={<CopyFileData />}
    /> 
  </FileManagerViewConfig> 
```

This is the whole process of registering a new file action element.

<Image src={fmAddFileAction} title="Custom File Action" />


### Discover File Actions

This section demonstrates how you can discover the names of existing file actions. This is important for further sections on positioning, removing, and replacing existing actions.

The easiest way to discover existing file actions is to use your browser's React Dev Tools plugins and search for the `BaseFileAction`:

<Image src={fmDiscoverFileActions} title="Discover Existing File Actions" />

### Position a File Action

To position your custom file action before or after an existing action, you can use the `before` and `after` props on the `<Browser.FileAction>` element:

```tsx
<FileManagerViewConfig>
  <Browser.FileAction 
    name={"copy-json"}
    element={<CopyFileData />}
    before={"delete"}
  />
</FileManagerViewConfig>
```

<Image src={fmPositionFileActions} title="Position Your Action Before Another Action" />

### Remove a File Action

You may want to remove an existing action. All you need to do is reference the action by name and pass a `remove` prop to the `<Browser.FileAction>` element:

```tsx
<FileManagerViewConfig>
  <Browser.FileAction name={"delete"} remove />
</FileManagerViewConfig>
```

### Replace a File Action

To replace an existing action with a new action element, you need to reference an existing action by name and pass a new component via the `element` prop:

```tsx
<FileManagerViewConfig>
  <Browser.FileAction
    name={"delete"} 
    element={<span>A new action!</span>} 
  />
</FileManagerViewConfig>
```
