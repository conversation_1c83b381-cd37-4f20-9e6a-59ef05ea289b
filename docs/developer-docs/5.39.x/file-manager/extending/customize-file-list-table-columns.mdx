---
id: cbc49b96
title: Customize File List Table Columns
description: Learn how to add, replace, or remove columns in the File List Table.
---

import fmCustomCellRenderer from "./assets/fm-custom-cell-renderer.png";
import fmDefaultTable from "./assets/fm-default-table.png";
import fmDiscoverColumn from "./assets/fm-discover-column.png";
import fmHideableColumn from "./assets/fm-hideable-column.gif";
import fmSimpleColumn from "./assets/fm-simple-column.png";
import fmVisibleColumn from "./assets/fm-visible-column.gif";

import { Alert } from "@/components/Alert";

<Alert type="info" title="Can I Use This?">

This feature has been available since Webiny **v5.39.0**.

</Alert>

<Alert type="success" title="What you will learn">

- how to add a column
- how to discover existing column names
- how to change the position, remove, or replace a column

</Alert>

## Overview

File Manager allows you to view files and folders in a table or grid format. The table displays predefined columns, such as:

- **Name**: presenting the `name` field
- **Type**: presenting the `type` fields
- **Size**: presenting the `size` fields
- **Author**: presenting the `createdBy` field
- **Created**: presenting the `createdOn` field
- **Modified**: presenting the `savedOn` field


<Image src={fmDefaultTable} title="Default table columns" />

## Using the code examples

The following code examples follow our usual configuration pattern. You need to add the code from the examples to your `apps/admin/src/App.tsx`. Here's an example:

```diff-tsx apps/admin/src/App.tsx
import React from "react";
import { Admin } from "@webiny/app-serverless-cms";
import { Cognito } from "@webiny/app-admin-users-cognito";
+ import { FileManagerViewConfig } from "@webiny/app-file-manager";
import "./App.scss";

+ // You can destructure config components to make the code more readable and easier to work with.
+ const { Browser } = FileManagerViewConfig;

export const App = () => {
    return (
      <Admin>
        <Cognito />
+       <FileManagerViewConfig>
+         {* Config components go here. *}
+       </FileManagerViewConfig>
      </Admin>
    );
};
```

<Alert type="info" title="Custom field setup">

The following code examples are built using the [custom file field](/docs/{version}/file-manager/extending/customize-file-fields) `copyright`.

</Alert>

## Add a Column

To add a new column, use the `Browser.Table.Column` component and mount it within your Admin app. This component will serve as the foundation for your columns.

### Simple Column

Here is an example of creating a column to show the `copyright` field data within the table. 
The `Browser.Table.Column` component receives the following mandatory props:

- `name` used to target the field you want to show and serves as a unique identifier
- `header` used for formatting the column header

```tsx
<FileManagerViewConfig>
  <Browser.Table.Column
    name={"extensions.copyright"}
    header={"Copyright"}
  />   
</FileManagerViewConfig> 
```

This is the whole process of registering a column.

<Image src={fmSimpleColumn} title="Define a simple custom column" />

### Custom Column Cell Rendering

Sometimes, you may want to modify how the data in a cell is displayed using a specific component. This allows you to have full control over how the information is presented.

For example, you could create a `CellCopyright` component that displays:

- a dash for folder rows
- a dash in case the copyright is not set
- the `copyright` field value prepended by  the `©` symbol 


```tsx
export const CellCopyright = () => {
  // You can destructure child methods to make the code more readable and easier to work with.
  const { useTableRow, isFolderRow } = FileManagerViewConfig.Browser.Table.Column;
  // useTableRow() allows you to access the entire data of the current row.
  const { row } = useTableRow();

  // isFolderRow() allows for custom rendering when the current row is a folder.
  if (isFolderRow(row)) {
    return <>{"-"}</>;
  }

  // If copyright is not set, let's render a dash
  if (!row?.extensions?.copyright) {
    return <>{"-"}</>;
  }

  // Let's render the file copyright.
  return <>© {row.extensions.copyright}</>;
};
```

Using the `cell` prop, you can pass the custom component to the column definition.

```diff-tsx
  <FileManagerViewConfig>
    <Browser.Table.Column
      name={"extensions.copyright"}
      header={"Copyright"}
+     cell={<CellCopyright />}
    />   
  </FileManagerViewConfig> 
```

<Image src={fmCustomCellRenderer} title="Define a custom cell renderer" />

### Custom column size
To set the initial size of a column, you can use the `size` property. By default, the size is set to `100`.

However, this is not a value in pixels but more of a proportion with the other columns within the table. If you want to double the size of a specific column, you can pass `200` as the value.

In addition, you can allow or disallow users to adjust the column width according to their preferences by defining the `resizable` prop.

```diff-tsx
  <FileManagerViewConfig>
    <Browser.Table.Column
      name={"extensions.copyright"}
      header={"Copyright"}
+     size={75}
+     resizable={false} // The column is not resizable by the user. 
    />   
  </FileManagerViewConfig> 
```

### Custom column visibility

The `visible` property in data tables is crucial for user experience. By default, the column is visible to users. But, it can be set to hide by default, which is useful when dealing with large datasets.

Users have the ability to show/hide columns by using the column settings menu.

```diff-tsx
  <FileManagerViewConfig>
    <Browser.Table.Column
     name={"extensions.copyright"}
      header={"Copyright"}
+     visible={false}
    />   
  </FileManagerViewConfig> 
```

<Image src={fmVisibleColumn} title="Define column visibility" />

In addition to controlling the initial visibility of columns, you can further enhance user customization with the `hideable` feature.

When the `hideable` property is set to false, users are restricted from dynamically toggling the visibility of the column. In this scenario, the column remains fixed and visible, adhering to the configuration set by developers.

```diff-tsx
  <FileManagerViewConfig>
    <Browser.Table.Column
      name={"price"}
      header={"Price"}
      modelIds={["property"]}
+     hideable={false}
    />   
  </FileManagerViewConfig> 
```

<Image src={fmHideableColumn} title="Prevent users from toggling column visibility" />

### Custom column class names

You can easily add custom CSS class names to columns using the `className` property. The class names provided will be injected in both the column header and cell.

```diff-tsx
  <FileManagerViewConfig>
    <Browser.Table.Column
      name={"extensions.copyright"}
      header={"Copyright"}
+     className={"custom-copyright-className"}
    />   
  </FileManagerViewConfig> 
```

## Discover Columns

This section demonstrates how you can discover the names of existing columns. This is important for further sections on positioning, removing, and replacing columns.

The easiest way to discover existing columns is to use your browser's React Dev Tools plugins and search for the `BaseColumns`:

<Image src={fmDiscoverColumn} title="Discover existing columns" />

## Position a Column

To position your column before or after an existing column, you can use the `before` and `after` props on the `<Browser.Table.Column>` element:

```tsx
<FileManagerViewConfig>
  <Browser.Table.Column
    name={"extensions.copyright"}
    header={"Copyright"} 
    before={"createdBy"} 
  />
</FileManagerViewConfig>
```

## Remove a Column

Sometimes you might want to remove an existing column. All you need to do is reference the column by name and pass a `remove` prop to the `<Browser.Table.Column>` element:

```tsx
<FileManagerViewConfig>
  <Browser.Table.Column name={"type"} remove />
</FileManagerViewConfig>
```

## Replace a Column

To replace an existing column with a new cell renderer, you need to reference an existing column by name and pass a new component via the `cell` prop:

```tsx
<FileManagerViewConfig>
  <Browser.Table.Column 
    name={"type"} 
    header={"Custom Type"} 
    cell={<span>{"Custom Type Cell"}</span>} 
  />
</FileManagerViewConfig>
```
