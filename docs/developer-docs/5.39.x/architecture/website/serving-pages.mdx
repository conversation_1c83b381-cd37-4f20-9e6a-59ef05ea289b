---
id: aafeab37
title: Serving Pages
description: Learn how the prerendered pages are statically served to actual website visitors.
---

import { Alert } from "@/components/Alert";
import websiteVisit from "./assets/serving-pages/webiny_website_visit.png";

<Alert type="success" title="What you’ll learn">

- how the previously prerendered pages are statically served to actual website visitors

</Alert>

## Diagram

<Alert type="info">

For brevity, the diagram doesn't include network-level cloud infrastructure resources, like region, VPC, availability zones, and so on. Check out the [Deployment Modes](/docs/{version}/architecture/deployment-modes/introduction) section if you're interested in that aspect of the deployed cloud infrastructure.

</Alert>

<Alert type="info">

Note that the Amazon DynamoDB and Amazon OpenSearch databases are deployed as part of the [Core](/docs/{version}/architecture/core/overview) project application. Also, the GraphQL Handler AWS Lambda function is deployed as part of the [API](/docs/{version}/architecture/api/overview) project application

These are still included in the diagram, just so it's more clear to the reader.

</Alert>

<Image
  src={websiteVisit}
  title="Webiny Cloud Infrastructure - Website - Serving Pages"
  shadow={false}
/>

## Description

The diagram shows how pages, prerendered as part of the **Prerendering Pages** flow, are served to actual website visitors.

It's important to point out that, although the initial HTML and page metadata are always returned from the upper Amazon S3 bucket <diagram-letter>B</diagram-letter>, all of the static assets (CSS, JS, images, ...) are still returned from the lower one <diagram-letter>C</diagram-letter>, to which the React application was uploaded during the deployment of the **Website** application.

This way, the static assets don't have to be uploaded twice (to both Amazon S3 buckets <diagram-letter>B</diagram-letter> <diagram-letter>C</diagram-letter>), making the deployment process a bit faster and easier to maintain.

<Alert type="info">

Learn more about the prerendering flow in the [Prerendering Pages](/docs/{version}/architecture/website/prerendering-pages) section.

</Alert>

The flow consists of the following four steps:

1. Users visit a particular URL which leads to your website, with a web browser of their choice.
2. The HTTP request, issued by the web browser, reaches the Amazon CloudFront <diagram-letter>A</diagram-letter>, which forwards it to the Amazon S3 bucket <diagram-letter>B</diagram-letter>. This is where the prerendered content was previously stored.
3. The page HTML, located in the S3 bucket <diagram-letter>B</diagram-letter>, is returned to the Amazon CloudFront <diagram-letter>A</diagram-letter>, which caches it first, and then returns it back to the user's browser. Note that if the page for given URL wasn't found, a default **not found** page is returned instead.
4. User's browser receives the HTML and starts to fetch the linked static assets (JS, CSS, images, ...). As mentioned in the description section, these assets are always located on the `/static/*` path, and the HTTP requests for these are always redirected to the lower Amazon S3 bucket <diagram-letter>C</diagram-letter>, on the Amazon CloudFront <diagram-letter>A</diagram-letter>.

## FAQ

### Are there any Amazon CloudFront caching rules in place?

By default, all of the files that are served via the `/static/*` path are cached for 30 days. The rest is cached for 30 seconds. If need be, this can be additionally adjusted via code.

### What is the `static` folder anyways?

The Website React application is actually a [create-react-app](https://create-react-app.dev/) application, which, upon creating a production build, places all of the static files (JS, CSS, images, ...) into this folder.
