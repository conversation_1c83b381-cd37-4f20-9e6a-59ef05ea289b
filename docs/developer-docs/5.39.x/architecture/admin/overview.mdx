---
id: aafeab32
title: Overview
description: Learn about the necessary cloud infrastructure resources on which the Admin Area project application relies on.
---

import { Alert } from "@/components/Alert";
import overview from "./assets/overview/webiny_admin_overview.png";

<Alert type="success" title="What you’ll learn">

- the necessary cloud infrastructure resources on which the **Admin Area** project application relies on
- what does the **Admin Area** project application represent

</Alert>

## The Admin Area Project Application

The **Admin Area** project application represents, as the name itself suggests, your administration area, which is a simple React single-page-application (SPA).

With only two cloud infrastructure resources, the Amazon CloudFront and Amazon S3, hosting single page applications is simple, and most importantly, cost-effective.

## Diagram

<Alert type="info">

For brevity, the diagram doesn't include network-level cloud infrastructure resources, like region, VPC, availability zones, and so on. Check out the [Deployment Modes](/docs/{version}/architecture/deployment-modes/introduction) section if you're interested in that aspect of the deployed cloud infrastructure.

</Alert>

<Image src={overview} title="Webiny Cloud Infrastructure - Admin Area - Overview" shadow={false} />

## Description

The diagram gives an overview of the complete cloud infrastructure that's needed to host the Admin Area application.

As we can see, it consists of two resources - Amazon CloudFront <diagram-letter>A</diagram-letter> and Amazon S3 bucket <diagram-letter>B</diagram-letter>.

In the upcoming [Serving Application Files](/docs/{version}/architecture/admin/serving-application-files) section, we examine how the Admin Area application is served to actual users.
