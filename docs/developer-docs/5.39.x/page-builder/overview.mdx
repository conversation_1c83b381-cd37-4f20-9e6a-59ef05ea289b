---
id: 9f49865c
title: Webiny Page Builder
description: Learn about Webiny's Page Builder, an open-source no-code page builder running on top of AWS serverless infrastructure.
---

import { InstallWebinyBanner } from "@/components/landing-pages/InstallWebinyBanner";
import { Divider } from "@/components/landing-pages/Divider";
import { Section, SectionTitle, SectionRow, SectionBox } from "@/components/landing-pages/Section";

<InstallWebinyBanner />

Webiny Page Builder is a powerful no-code visual builder created to empower non-technical users such as marketing teams to create rich, interactive and dynamic experiences. 

Although the Page Builder is created to be used by non-technical people, as an engineer you still have a lot of customization possibilities to extend and adapt that experience to both your users and content editors.  

Below you'll find some key articles to get you started with customizing the page builder, and for additional learning make sure to check out the other articles in the sidebar. 

<Divider/>


<Section>
  <SectionTitle
    title="Basics"
    subText="Learn the basics of Page Builder."
    categoryTitle="Basics"
  />

  <SectionRow>
    <SectionBox title="Theming" learnMoreLink="/docs/page-builder/theming/introduction">
      <p>
        An overview of how theming works in Webiny's Page Builder application.
      </p>
    </SectionBox>
    <SectionBox title="Page Builder Video Course" learnMoreLink="https://www.youtube.com/playlist?list=PL9HlKSQaEuXQdyCQDH_w7VQQcZbc67cPU">
      <p>
        Video course to help you master the core functionality of Page Builder.
      </p>
    </SectionBox>
  </SectionRow>

</Section>

<Section>
  <SectionTitle
    title="extensions"
    subText="Learn how to customize and extend the capabilities of the Page Builder app."
    categoryTitle="Extending"
  />

  <SectionRow>
    <SectionBox title="Create a Custom Page Element" learnMoreLink="/docs/page-builder/extending/create-a-page-element">
      <p>
        Learn how to create a custom page element that can be rendered on pages created with Webiny's Page Builder app.
      </p>
    </SectionBox>
    <SectionBox title="Extend GraphQL API" learnMoreLink="/docs/page-builder/extending/extend-graphql-api">
      <p>
        Learn how to extend the Page Builder-related GraphQL types and operations.
      </p>
    </SectionBox>
    <SectionBox title="Extend Page Settings" learnMoreLink="/docs/page-builder/extending/extend-page-settings">
      <p>
        Learn how to extend page settings by introducing new fields to it.
      </p>
    </SectionBox>
  </SectionRow>

</Section>

<Section>
  <SectionTitle
    title="Additional resources"
    subText="For more learning make sure to check out these articles."
    categoryTitle="Resources"
  />

  <SectionRow>
    <SectionBox title="Lexical Editor" learnMoreLink="/docs/page-builder/extending/lexical-editor">
      <p>
        Learn how to configure the Lexical Editor in Page Builder.
      </p>
    </SectionBox>
    <SectionBox title="Lifecycle Events" learnMoreLink="/docs/page-builder/references/lifecycle-events">
      <p>
        Learn about Page Builder lifecycle events, how they work and how to subscribe to a lifecycle event.
      </p>
    </SectionBox>
    <SectionBox title="Plugin References" learnMoreLink="/docs/page-builder/references/plugins">
      <p>
        Page Builder plugin references.
      </p>
    </SectionBox>
  </SectionRow>

</Section>
