---
id: 8f6fo7a4
title: About Background Tasks
description: You will learn about Background Tasks, how to create new definitions, how to trigger them and how to handle the task run.
---

import {Alert} from "@/components/Alert";
import {CanIUseThis} from "@/components/CanIUseThis";
import {WhatYouWillLearn} from "@/components/WhatYouWillLearn";

<Alert type="danger" title="Use with caution!">
  This feature is experimental and is subject to change in future releases.
</Alert>

<CanIUseThis since={"5.39.0"} />

<WhatYouWillLearn>

- what are Background Tasks

</WhatYouWillLearn>

## Overview

In the 5.39.0 version of Webiny we have introduced a Background Task feature.

This feature enables our users to run operations which take a long time to finish - more than available `15 minutes` maximum run time of the AWS Lambda.

The Background Task run limit is `1 year`, from the time when the task is triggered, which is the maximum execution time for the `AWS Step Function`.

This functionality uses the `AWS Event Bridge`, `AWS Step Function` and `AWS Lambda`.

## AWS Services Used in the Background Tasks
To find out more about these services, please visit the following links:
- [`AWS Event Bridge`](https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-what-is.html)
- [`AWS Step Function`](https://docs.aws.amazon.com/step-functions/latest/dg/welcome.html)
- [`AWS Lambda`](https://docs.aws.amazon.com/lambda/latest/dg/welcome.html)


<Alert title="Resumable Background Tasks" type="warning">
  Currently, Background Tasks are not using the Task Token, so they are not resumable. We will be
  working on the solution for this in the future.
</Alert>
