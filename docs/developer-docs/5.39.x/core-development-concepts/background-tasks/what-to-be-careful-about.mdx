---
id: 8f6fo7c4
title: What to be careful about?
description: You will learn about Background Tasks, how to create new definitions, how to trigger them and how to handle the task run.
---

import {Alert} from "@/components/Alert";
import {CanIUseThis} from "@/components/CanIUseThis";
import {WhatYouWillLearn} from "@/components/WhatYouWillLearn";

<Alert type="danger" title="Use with caution!">
  This feature is experimental and is subject to change in future releases.
</Alert>

<CanIUseThis since={"5.39.0"} />

<WhatYouWillLearn>
  
- what to be careful about
- limits of AWS Services

</WhatYouWillLearn>

## What To Be Careful About

### Lambda Timeout

AWS Lambda can run up to 15 minutes. Webiny provides a mechanism for checking on how much time is there left before the Lambda times out, but it is up to the developer to use it. You can read about it [here](#the-is-close-to-timeout-method).

### Step Function Timeout

AWS Step Function can live up to 1 year. This is the maximum time for the Step Function, set by AWS, and it cannot be increased.

### Step Function Execution Limit

AWS Step Function has a hard limit of 25,000 state changes - executions. It cannot be increased.

### Step Function State Change Limit

AWS Step Function does not provide a built-in mechanism to limit the number of state changes on each task run.

This means that a task can go into unwanted loop and execute up to 25,000 times, which is something that we want to avoid, or at least, control it.

What Webiny does to help with this is count the number of the Lambda handler executions, and if the number is greater than defined, it will end the task with an error.

Default limit: `500`.

To change the limit, see [`maxIterations`](#advanced-definition) parameter when defining the Background Task.


<Alert title="Resumable Background Tasks" type="warning">
  Currently, Background Tasks are not using the Task Token, so they are not resumable. We will be
  working on the solution for this in the future.
</Alert>
