---
id: aafeab3f
title: Install Webiny
description: Learn how to create a new Webiny project and deploy it into your AWS account.
---

import { Alert } from "@/components/Alert";
import { TabsComponent, TabsItem } from "@/components/TabsComponent";

<Alert type="success" title="WHAT YOU’LL LEARN">

- how to create a brand new Webiny project
- how to deploy it to your [AWS](https://aws.amazon.com/) account

</Alert>

---

## Prerequisites

Before proceeding, make sure you have the following:

1. **Node.js >=14**
   - Node.js versions **14** or greater
   - If you don't have Node.js installed, the easiest way to install it is by [downloading the official binary](https://nodejs.org/en/)
2. **yarn ^1.22.0 || >=2**
   - Webiny works with both yarn versions [1 (classic)](https://yarnpkg.com/en/docs/install) and [>=2 (berry)](https://yarnpkg.com/)
   - for version 1 - **1.22.0** or later is required
3. **AWS account and user credentials**
   - in order to deploy Webiny, you must have a valid [AWS account and user credentials](/docs/infrastructure/aws/configure-aws-credentials) set up on your system

## Project Setup

Once you have all the prerequisites in place, we recommend creating a new Webiny project using `create-webiny-project` - a tool that sets everything up automatically for you. So, in your terminal of choice, run the following command:

```
npx create-webiny-project my-new-project
```

From there, follow the on-screen setup instructions, which includes answering a couple of project-related questions, and doing your first deployment.

### Pick Your Database

Webiny supports two storage types at the moment: **DynamoDB** and **DynamoDB+Elasticsearch**.
Choose which ever suits your needs more. Please, keep reading to find out more about the storage types...

<Alert type="info">
  Please choose your database with consideration for both present and future project requirements,
  as once you've made your selection, it cannot be changed later.
</Alert>

<TabsComponent>
  <TabsItem title="DynamoDB">

As noted, this option should be used if you intend to have a small to medium size project: up to a few hundred (even thousands) records, per application, in the database.

<Alert type="info">

Because the DynamoDB does not provide advanced filtering or sorting we had to implement our own.

</Alert>

Basically, we load the smallest amount of records possible from the DynamoDB and then perform filtering and sorting in the code to lower the DynamoDB cost.
Unfortunately, it might happen that we load all the records from a certain application, depending on the filtering applied, but generally that will not be the case.

  </TabsItem>
  <TabsItem title="DynamoDB + Elasticsearch">

<Alert type="danger" title="AWS RESOURCES ARE NOT FREE!">

Although Webiny is using serverless resources, the Elasticsearch option relies on **AWS ElasticsearchService**, which is not free.
It is the only non-serverless component of our infrastructure and is charged per hour. It amounts to roughly $25/month.

Please keep this in mind, and if you just want to try Webiny, it is better to use the DynamoDB-only option.
If you are testing with the Elasticsearch, don't forget to destroy the infrastructure when you're done, to avoid the unnecessary expenses.

</Alert>

Feel free to choose this option when you want to build some larger application, or you know that you will have a lot of records stored.

DynamoDB + Elasticsearch project has exactly the same functionalities (filtering and sorting) as the DynamoDB one but, when having a lot of records, it will be faster to search and sort in the project deployed with Elasticsearch.

  </TabsItem>
</TabsComponent>

## First Deployment

Once your new project has been created, it's time to deploy it into your AWS account.

To do that, in your terminal, navigate to your project folder, and run the following command:

```
yarn webiny deploy
```

By executing this command, all of the project applications will first get built, and, along with needed cloud infrastructure resources, deployed into your AWS account.

<Alert type="info" title="Project Applications">

Learn more about project applications and project organization in general, in the [Project Applications and Packages](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages) key topic.

</Alert>

<Alert type="success" title="Cloud Infrastructure">

Learn what gets deployed into your AWS account and how it all works in the [Cloud Infrastructure](/docs/{version}/architecture/introduction) key topics section.

</Alert>

Note that the first deployment can take up to 20 minutes! So, even though it might look nothing is happening in the terminal, please be patient and let the process finish. If something went wrong, an error will be shown.

Finally, once the deployment has been done, you are presented with the URL over which you can access your Admin Area, and finish the installation.

![Admin Area - Installation Wizard](./assets/installation-wizard.png)

<Alert type="info" title="Misplaced Admin area URL?">

Running the `yarn webiny info` command in your Webiny project folder will give you all of the relevant project URLs.

</Alert>

<Alert type="warning" title="Telemetry">

Please note that, by default, Webiny collects anonymous usage information, which is exclusively used for improving the product and understanding usage patterns. Please take a look at our [Telemetry](/docs/{version}/webiny-telemetry/webiny-telemetry) page for more information on this subject.

If, at any point in time, you wish to disable this behaviour, you can do it with the [Webiny CLI](/docs/{version}/core-development-concepts/basics/webiny-cli), by running the following command: `yarn webiny disable-telemetry`.

</Alert>

## Troubleshooting

**Deploying my project takes a long time to finish.**

We're aware of this fact, and this is mainly because of the [Amazon Elasticsearch Service](https://aws.amazon.com/elasticsearch-service/). While other cloud infrastructure resources get deployed reasonably fast, this service can take anywhere from 15 to 30 minutes to become ready.

Unfortunately, this is a well-known issue for quite some time, and until the present, there haven't been any positive improvements.

**SubscriptionRequiredException: The AWS Access Key Id needs a subscription for the service**

In some cases, upon deploying a new Webiny project, it's possible for users to experience the following error being thrown in their terminal:

```
SubscriptionRequiredException: The AWS Access Key Id needs a subscription for the service
```

The following are some of the reasons why you might be receiving this error:

1. you didn't verify your phone number
2. you didn't verify your payment method
3. you didn't select a support subscription (free or other)
4. if you created a brand new AWS account, it wasn't yet processed on the AWS side

The account signup and verification process can be completed via https://portal.aws.amazon.com/billing/signup?type=resubscribe#/resubscribed.

**Pulumi is throwing a `syscall.loadsystemlibrary` exception**
There could be various reasons why this error was thrown. But, if deploying from a Windows machine, we've seen that it could be
caused by an anti-virus software that you might have installed on your machine. Our suggestion is to try to temporary disable it and see if that produces any positive outcome.

**Newer Apple M1 devices**

If you're using a next-generation Apple M1 device, there is a chance you might run into problems while deploying your new Webiny project. For example:

```
 +  pulumi:pulumi:Stack api-dev creating assertion failed [inst.has_value()]: failed to decode instruction: 0x0
```

This is probably due to the fact that the underlying software simply isn't ready yet for the new M1-based OS you're running. Based on [this](https://github.com/hashicorp/terraform/issues/27350) article, we've seen that doing the following export does resolve the shown issue in some cases:

```
export GODEBUG=asyncpreemptoff=1
```

Please try executing the command in your terminal, and run the deployment again. If this does not help, feel free to [ask](https://www.webiny.com/slack) for additional help.
