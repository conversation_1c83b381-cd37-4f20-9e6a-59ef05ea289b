---
id: aafea93b
title: Extend the GraphQL API
description: Learn how to extend the Headless CMS-related GraphQL types and operations.
---

import { Alert } from "@/components/Alert";
import myPostsModel from "./assets/extend-graphql-api/my-posts-model.png";
import myPostsQuery from "./assets/extend-graphql-api/my-posts-query.png";

<Alert type="success" title="What you'll learn">

- how to extend the Headless CMS-related GraphQL types and operations

</Alert>

<Alert type="info">

Use the [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) command to continuously deploy application code changes into the cloud and instantly see them in action. For quick (manual) testing, you can use the built-in [API Playground](/docs/{version}/admin-area/basics/api-playground).

</Alert>

## Custom Queries

Let's say we wanted to extend our GraphQL schema with a custom `listMyPosts` query, which, as the name suggests, would enable us to quickly retrieve all posts created via the Headless CMS application, for the currently logged in user.

In other words, we want to return all content entries of the **Post** content model, where the `createdBy` points to the currently logged in user. For demonstration purposes, our **Post** content model will be very simple:

![The Post Content Model](./assets/extend-graphql-api/my-posts-model.png)

<Alert type="success">

The `createdBy` field is automatically assigned to every content entry and it represents the currently logged in user.

</Alert>

Creating the new `listMyPosts` query can be achieved via a single [`GraphQLSchemaPlugin`](https://github.com/webiny/webiny-js/blob/v5.11.0/packages/handler-graphql/src/plugins/GraphQLSchemaPlugin.ts#L10) plugin.

```ts api/code/headlessCMS/src/plugins/posts.ts
import { GraphQLSchemaPlugin } from "@webiny/handler-graphql/plugins";
import { ListResponse } from "@webiny/handler-graphql/responses";
import { CmsEntry, CmsEntryMeta } from "@webiny/api-headless-cms/types";

// Make sure to import the `Context` interface and pass it to the `GraphQLSchemaPlugin`
// plugin. Apart from making your application code type-safe, it will also make the
// interaction with the `context` object significantly easier.
import { Context } from "~/types";

export default [
  new GraphQLSchemaPlugin<Context>({
    // Extend the `Query` type with the `listMyPosts` query. Note the `PostListResponse` type.
    // It exists because we've previously created the `Post` content model via Admin Area.
    typeDefs: /* GraphQL */ `
      extend type Query {
        # List posts that were created by the currently logged in user.
        listMyPosts: PostListResponse
      }
    `,
    // In order for the `listMyPosts` to work, we also need to create a resolver function.
    resolvers: {
      Query: {
        listMyPosts: async (_, args: { id: string }, context) => {
          const { security, cms } = context;

          // Retrieve the `post` model.
          const model = await cms.getModel("post");

          // Use the `cms.listLatestEntries` method to fetch latest entries for the currently
          // logged in user. Note that you could also use the `listPublished` method here instead
          // of `cms.listLatestEntries`, if a list of published posts is what you need.
          const response: [CmsEntry[], CmsEntryMeta] = await cms.listLatestEntries(model, {
            where: {
              // Retrieving the currently logged in user is as easy
              // as calling the `security.getIdentity` method.
              createdBy: security.getIdentity().id
            }
          });

          return new ListResponse(...response);
        }
      }
    }
  })
];
```

<Alert type="info">

The code above can be placed in the [`api/code/headlessCMS/src/plugins/posts.ts`](https://github.com/webiny/webiny-examples/blob/master/headless-cms/extend-graphql-api/custom-queries/api/code/headlessCMS/src/plugins/posts.ts) file, which doesn't exist by default, so you will have to create it manually. Furthermore, once the file is created, make sure that it's actually imported and registered in the [`api/code/headlessCMS/src/index.ts`](https://github.com/webiny/webiny-examples/blob/master/headless-cms/extend-graphql-api/custom-queries/api/code/headlessCMS/src/index.ts) entrypoint file.

</Alert>

With all the changes in place, we should be able to run the following GraphQL mutation:

```graphql
{
  listPosts {
    data {
      title
      text
    }
  }
  listMyPosts {
    data {
      title
      text
    }
  }
}
```

For example:

![Using the listMyPosts Query](./assets/extend-graphql-api/my-posts-query.png)

As we can see, the `listPosts` query returned a total of three posts. On the other hand, the `listMyPosts` only returned posts for the currently logged in user, which is the expected result.

## FAQ

### What is the `context` object and where are all of its properties coming from?

In the shown examples, you may have noticed we were using the `context` object in GraphQL resolver functions. This object contains multiple different properties, mainly being defined from different Webiny applications that were imported in the Headless CMS GraphQL API's [`api/code/headlessCMS/src/index.ts`](https://github.com/webiny/webiny-examples/blob/master/headless-cms/extend-graphql-api/custom-queries/api/code/headlessCMS/src/index.ts) entrypoint file.

That's why, for example, we were able to utilize the [`cms.models.get`](https://github.com/webiny/webiny-js/blob/v5.11.0/packages/api-headless-cms/src/content/plugins/crud/contentModel.crud.ts#L126) and [`cms.entries.listLatest`](https://github.com/webiny/webiny-js/blob/v5.11.0/packages/api-headless-cms/src/content/plugins/crud/contentEntry.crud.ts#L286) methods.

For easier discovery and type safety, we suggest a type is always assigned to the `context` object in your GraphQL resolver functions.
