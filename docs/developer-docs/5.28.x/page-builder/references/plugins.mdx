---
id: aafeab5d
title: Plugin References
description: Page Builder plugin references
---

## Plugins

### `pb-editor-page-element`

```ts
export type PbEditorPageElementPlugin = Plugin & {
  type: "pb-editor-page-element";
  elementType: string;
  toolbar?: {
    // Element title in the toolbar.
    title?: string | PbEditorPageElementTitle;
    // Element group this element belongs to.
    group?: string;
    // A function to render an element preview in the toolbar.
    preview?: ({ theme: PbTheme }) => ReactNode;
  };
  // Help link
  help?: string;
  // Whitelist elements that can accept this element (for drag&drop interaction)
  target?: string[];
  // Array of element settings plugin names.
  settings?: Array<string | Array<string | any>>;
  // A function to create an element data structure.
  create: (
    options: { [key: string]: any },
    parent?: PbEditorElement
  ) => Omit<PbEditorElement, "id">;
  // A function to render an element in the editor.
  render: (params: { theme?: PbTheme; element: PbEditorElement; isActive: boolean }) => ReactNode;
  // A function to check if an element can be deleted.
  canDelete?: (params: { element: PbEditorElement }) => boolean;
  // Executed when another element is dropped on the drop zones of current element.
  onReceived?: (params: {
    state?: EventActionHandlerCallableState;
    meta: EventActionHandlerMeta;
    source: PbEditorElement | DragObjectWithTypeWithTarget;
    target: PbEditorElement;
    position: number | null;
  }) => EventActionHandlerActionCallableResponse;
  // Executed when an immediate child element is deleted
  onChildDeleted?: (params: {
    element: PbEditorElement;
    child: PbEditorElement;
  }) => PbEditorElement | undefined;
  // Executed after element was created
  onCreate?: string;
  // Render element preview (used when creating element screenshots; not all elements have a simple DOM representation
  // so this callback is used to customize the look of the element in a PNG image)
  renderElementPreview?: (params: {
    element: PbEditorElement;
    width: number;
    height: number;
  }) => ReactElement;
};
```

### `pb-render-page-element`

```ts
export type PbRenderElementPlugin = Plugin & {
  type: "pb-render-page-element";
  // Name of the pb-element plugin this render plugin is handling.
  elementType: string;
  // A function to render an element in the actual page.
  render: (params: { theme: PbTheme; element: PbElement }) => React.ReactNode;
};
```

### `pb-editor-page-element-advanced-settings`

```ts
export type PbEditorPageElementAdvancedSettingsPlugin = Plugin & {
  type: "pb-editor-page-element-advanced-settings";
  elementType: string;
  // A function to render the element settings in the editor sidebar.
  render(params?: { Bind: BindComponent; data: any; submit: () => void }): ReactElement;
  onSave?: (data: FormData) => FormData;
};
```

### `pb-editor-page-element-style-settings`

```ts
export type PbEditorPageElementStyleSettingsPlugin = Plugin & {
  type: "pb-editor-page-element-style-settings";
  render(params: { options?: any }): ReactElement;
  elements?: boolean | string[];
};
```

### `pb-theme`

```ts
export type PbTheme = {
  colors: { [key: string]: string };
  elements: { [key: string]: any };
  typography: {
    [key: string]: {
      label: string;
      component: string | React.ComponentType<any>;
      className: string;
    };
  };
};
```

### `pb-page-layout`

```ts
export type PbPageLayoutPlugin = Plugin & {
  layout: PbPageLayout;
};
```

### `pb-page-element-button-click-handler`

```ts
export type PbButtonElementClickHandlerVariable = {
  // Variable key, received by the custom handler.
  name: string;
  // Variable label, shown in button "Action" settings.
  label: string;
  // Variable default value.
  defaultValue: any;
};

export type PbButtonElementClickHandlerPlugin<TVariables = Record<string, any>> = Plugin & {
  type: "pb-page-element-button-click-handler";
  // Title shown in the Page Builder button configuration settings;
  title: string;
  // List of variables the user will be able to set via button "Action" settings.
  variables?: PbButtonElementClickHandlerVariable[];
  // Your custom button handler, receives the variables set by the user.
  handler: (params: { variables: TVariables }) => void | Promise<void>;
};
```
