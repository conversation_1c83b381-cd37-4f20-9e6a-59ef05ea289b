---
id: aafeab5b
title: Layouts
description: How are theme layouts defined, and what do they do.
---

import { Alert } from "@/components/Alert";

<!-- Always include this section on top of the page. -->

<Alert type="success" title="What You’ll Learn">

- what is a layout
- what are the internals of a layout
- how to create your custom layout and use it on a page

</Alert>

## Overview

A layout defines the content hierarchy. It is a pattern that defines a website’s structure. It provides clear paths for navigation within webpages and puts the most important elements of a website front and center.

The most used layouts consist of three parts:

- header at the top containing links that help users to navigate around the website
- page content in the center
- footer at the very bottom containing various information about the website

## Using Layout in Page Builder

Layouts are linked to page categories. When you create a page category, say "Blog", you need to select a layout inside which that content will be displayed.
The options available inside the layout dropdown are registered by the [`pb-page-layout`](/docs/{version}/page-builder/references/plugins#pb-page-layout) plugin.

Each page created inside the "Blog" category, by default, use that layout.
However, inside the page settings, you can overwrite that on a per-page basis if you need to.

![Webiny page categories](./images/webiny-page-category.gif)

## Layout internals

A layout is a simple React component that receives several props. Here is an example of the static layout which comes with the default theme:

```ts apps/theme/pageBuilder/layouts/Static.tsx
import React from "react";
import Header from "../components/Header";
import Footer from "../components/Footer";
import { PbPageData } from "@webiny/app-page-builder/types";

type StaticProps = {
  children: React.ReactNode;
  settings: Record<string, any>;
  page: PbPageData;
};

const Static = ({ children, ...rest }: StaticProps) => {
  return (
    <React.Fragment>
      <Header {...rest} />
      {children}
      <Footer {...rest} />
    </React.Fragment>
  );
};

export default Static;
```

As you can see from the example above, we simply render a `Header`, `Footer`, and page content in between.

If you are creating your own layout, the structure is all up to you.
You can insert any `DOM` structure with any custom CSS classes you require. You can also import and use pretty much any 3rd party NPM library here.

## Create Custom Layout

Now that we know what is a layout and what are its internals.
Let's create a custom layout of our own.

Create a new file named `SimpleLayout.tsx` inside `apps/theme/pageBuilder/layouts` and add the following content in it:

```ts apps/theme/pageBuilder/layouts/SimpleLayout.tsx
import React from "react";
import Header from "../components/Header";
import { PbPageData } from "@webiny/app-page-builder/types";

type LayoutProps = {
  children: React.ReactNode;
  settings: Record<string, any>;
  page: PbPageData;
};

const SimpleLayout = ({ children, ...rest }: LayoutProps) => {
  return (
    <React.Fragment>
      <Header {...rest} />
      {children}
    </React.Fragment>
  );
};

export default SimpleLayout;
```

Now we register this layout as the [`pb-page-layout`](/docs/{version}/page-builder/references/plugins#pb-page-layout) plugin.

```diff-js apps/theme/pageBuilder/index.ts
import StaticLayout from './layouts/Static'
+ import SimpleLayout from './layouts/SimpleLayout'

import { PbPageLayoutPlugin, PbThemePlugin } from '@webiny/app-page-builder/types'

export default [
    {
      type: 'pb-theme',
      theme: {
        colors: {
          //(...)
        },
        elements: {
          //(...)
        },
      },
    } as PbThemePlugin,
    {
      name: 'pb-page-layout-static',
      type: 'pb-page-layout',
      layout: {
        name: 'static',
        title: 'Static page',
        component: StaticLayout,
      },
    } as PbPageLayoutPlugin,
+   {
+    name: 'pb-page-layout-simple',
+    type: 'pb-page-layout',
+    layout: {
+      name: 'simple',
+      title: 'Simple Layout',
+      component: SimpleLayout,
+    },
  } as PbPageLayoutPlugin,
]
```

Let's add the newly created layout to our page in the editor. It looks as shown below:

![Webiny page builder custom layout](./images/webiny-theme-custom-layout.gif)
