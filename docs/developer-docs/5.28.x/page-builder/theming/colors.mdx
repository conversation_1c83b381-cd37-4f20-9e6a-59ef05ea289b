---
id: aafeab5a
title: Colors
description: How are theme colors managed and what things to consider when defining them.
---

import { Alert } from "@/components/Alert";

<!-- Always include this section on top of the page. -->

<Alert type="success" title="What You’ll Learn">

- how are colors defined in Page Builder theme
- how to modify the theme colors

</Alert>

## Overview

The colors used throughout the theme and Page Builder pages are extracted as variables and presented as color presets in the Page Builder editor.

This ensures a visual consistency between the theme and user-created content.

## Color Definition

Colors are defined in the `colors` section of the [`pb-theme`](/docs/{version}/page-builder/references/plugins#pb-theme) plugin.
As a general practice, don't define HEX or RGB colors directly, but reference them as a `CSS` variable, like the following example:

```json apps/theme/pageBuilder/index.ts
{
  "colors": {
    "primary": "var(--webiny-theme-color-primary)",
    "secondary": "var(--webiny-theme-color-secondary)",
    "background": "var(--webiny-theme-color-background)",
    "surface": "var(--webiny-theme-color-surface)",
    "textPrimary": "var(--webiny-theme-color-text-primary)"
  }
}
```

<Alert type="info">

Defining colors as `CSS` variable is important because when you select a color in the Page Builder editor for a certain element property, be that:

- `background-color`
- `border-color`
- `shadow`

the value of the color is stored in the database.

In the case of `CSS` variables, the `CSS` variable is stored, meaning that if you later decide to change the value of that color, you just change it in your CSS file, and all the existing content will pick up the new value.
On the other hand, if we stored `HEX` or `RGB` values, all the existing content will not pick up the new value.

</Alert>

You can add as many colors as you want, and name them whatever best fits your requirements. Make sure to have the actual color definitions somewhere inside your theme stylesheet.

### Prefix CSS Variables

We also recommend that all the color variables have a prefix `--webiny-theme-color-{name}`.

The reason for this is that your website theme and its stylesheets are imported into the Page Builder inside the admin interface. Using this prefix we ensure that there are no clashes between the style names of your website and Webiny administration.

The reason why we import the website theme into the administration is that inside the Page Builder we want to show you how your page looks like when a user is browsing your website, and that is only possible if we have the same stylesheets as your website.

## Sass Variables

Webiny themes use Sass variables, and all the colors are also mapped to CSS variables for ease of use and to enable better
style inheritance. <!-- Add the link to style inheritance article -->
If you open the [variables.scss](https://github.com/webiny/webiny-js/blob/next/apps/theme/pageBuilder/styles/variables.scss) file you'll find the default definitions inside.

## Update the Theme Colors

As an example, updating the following colors in the [variables.scss](https://github.com/webiny/webiny-js/blob/next/apps/theme/pageBuilder/styles/variables.scss)
file would result in a different value of primary and secondary colors as shown below:

```diff-scss apps/theme/pageBuilder/styles/variables.scss
// Some code is removed for the sake of brevity.
(...)//colors$webiny-pb-theme-primary:#77a6f7!default$webiny-pb-theme-secondary:#00887a!default$webiny-pb-theme-background:#eaecec!default$webiny-pb-theme-surface:#ffffff!default$webiny-pb-theme-on-primary:#ffffff!default$webiny-pb-theme-on-secondary: #ffffff !default;
$webiny-pb-theme-on-surface: #000000 !default;
$webiny-pb-theme-on-background: #131313 !default;
+ $webiny-pb-theme-text-primary: #293042 !default;
+ $webiny-pb-theme-text-secondary: #616161 !default;
$webiny-pb-theme-color-border: #e8ebee !default;

//...
```

After making these changes the Page Builder elements reflect the updated primary and secondary colors as shown below:

![Webiny page builder theme colors](./images/webiny-theme-colors.gif)
