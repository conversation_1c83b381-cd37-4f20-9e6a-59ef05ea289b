---
id: aafeab42
title: Advanced Publishing Workflow
description: A workflow driven publishing process.
---

import { Alert } from "@/components/Alert";
import YouTube from "@/components/YouTube";

<Alert type="success" title="WHAT YOU'LL LEARN">

- Advanced Publishing Workflow feature overview

</Alert>

<YouTube id="NNRghN1yY_g" />

The Advanced Publishing Workflow (APW) is an application that runs on top of the Page Builder and the Headless CMS. By default, those applications come with a simplified publishing workflow. For example, users can either publish content or set it in a `review requested` state. The actual review is done outside the system, and there's no collaboration platform that's provided to manage this activity.
This is where the APW comes in.

Within the APW, you are able to:

1. Define publishing workflows
2. Have an overview of all the pending reviews where your input is needed
3. Have a daily digest of pending activities
4. Collaborate with multiple peers in different levels or your organization on running a structured peer review process
5. Schedule page publishing and unpublishing activities

You can have multiple publishing workflows, where each workflow has different steps, members, and rules when it applies. You can define workflows that apply to:

- **Page Builder:**
- All pages
- Pages inside specific categories
- One or more individual pages
- **Headless CMS**
- All entries
- Entries inside a particular group
- Specific entries

In practice, you can say have a 2-step workflow that applies every time someone wants to publish a new blog post article. Those steps might be a peer review from a copywriter and a peer review from a marketing person. Then you can have another workflow that has 4-steps, and it only applies to when someone wants to change the homepage. In that case, you might want to get a peer review from someone in the legal department, marketing, sales, the CEO themself.

To each workflow step, you assign users that can provide a signoff and define steps as blocking or non-blocking.

The actual peer review is done by reviewers requesting one or more changes. All changes need to be resolved before a reviewer can provide a signoff for that step.

APW is a powerful utility that brings quality control and structured workflows. It ensures the content an organization publishes is of top quality and free of mistakes. Most of all, it streamlines the communication between multiple members involved in this process.
