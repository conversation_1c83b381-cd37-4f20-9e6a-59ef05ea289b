---
id: aafeab48
title: Form Builder
description: No-code form builder. Build forms in minutes using a drag&drop interface.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- Form Builder features and common use-cases

</Alert>

![Webiny CMS - Form Builder](./assets/webiny-form-builder.jpg)

The Form Builder allows you to build forms using a simple drag&drop editor. You can control the form layout, list of inputs, and their validation rules.

## Features

### No-code builder

Build forms, add validators, webhooks, ReCaptcha, and terms of service conditions, all with no code using a simple drag&drop editor.

There are many different fields you can insert, the main ones are:

- Single-line input
- Multi-line input
- Number
- Toggle
- Checkbox
- Radio button
- Dropdown

Besides fields, you can also control the layout of the form, in terms of grids and row elements. This allows you to model your form in a nice way so the user actually enjoys filling it out.

### Validators

Each of the fields can take multiple validators. There are several built-in validators that differ based on the field type.

With validators you can prevent users from submitting the form with invalid data.

### Form Builder + Page Builder

To add a form to a page, in the Page Builder editor there is a built-in element that you just drag and drop to any page where you want your form to appear. It’s an end-to-end no-code experience.

For more advanced use-cases we provide a [React library](https://github.com/webiny/webiny-js/tree/next/packages/app-form-builder) that you can use to render your forms on any React-based site. And for even more advanced cases, we expose a Form Builder API through which you can retrieve the from as a JSON data structure.

### Themes

Forms are rendered using a built-in theme. A theme is a set of simple React components and some CSS. You can easily change the theme, or create several themes of your own. In the UI you can pick the theme with which your form will render.

### Revisions

When you edit a form your changes are versioned and tracked. You can have multiple versions of the same form active so you measure the performance between different versions. Revisions also help you to go back to a previously saved state of your form.

### Terms of service and reCAPTCHA

Often you might want to add terms to your form which the user needs to accept before they can submit the form. You can easily activate this option for your form inside your form settings.

Similar to the terms of service feature, you can activate the reCAPTCHA feature which prevents and bots from automatically submitting and spamming your forms.

### Webhooks and API

Every time someone submits your form, you can specify a webhook endpoint to which the system will post the form payload. All the submissions are also automatically saved to the Webiny database.

All the form details, from the layout, fields, validators, terms of service to submissions are exposed with the GraphQL API. This makes it easy to retrieve any information you might need in other 3rd party systems.

## When to consider using the Form Builder

Form Builder is great when you have single-step forms. Your typical sales forms, contact us forms, support forms, and similar. Where the Form Builder is not a recommended solution would be for the survey or multi-step forms. It’s one we haven’t yet developed but might come in the future.
