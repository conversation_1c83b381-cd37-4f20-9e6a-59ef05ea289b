---
id: aafeab46
title: File Manager
description: A scaleable digital asset management application built on top of serverless infrastructure.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- File Manager features and use-cases

</Alert>

![Webiny CMS - File Manager](./assets/webiny-file-manager.jpg)

File Manager is a powerful tool to manage all your digital assets, from documents to images. File Manager as an app is integrated across Webiny. Anywhere where a file input is needed, the File Manager app is what you use to upload and select files.

## Features

### Asset organization

All assets can be tagged with one or more tags. You can filter files by one tag, or multiple tags at once. There is also a search bar, for input-based search.

The file File Manager React component also has props you can use to limit the user's selection. You can say "open File Manager, but only show images" and similarly. It provides a better UX to the user.

### Scale

The search and filtering options are powered by Elasticsearch. All assets are stored inside an S3 bucket. This architecture allows your system to scale and easily handle large amounts of assets.

### Image Editor

When it comes to images, there is a built-in image editor. Using the editor you can do basic manipulations like crop, flip, rotate and apply different filters.

### Image delivery API

The image delivery API allows you to resize images by specifying the image dimension as part of the image path. The images are automatically cached on the CDN, and there is a DDoS protection behind the service.

Every image you upload to the File Manager is resized in [several preset sizes](https://github.com/webiny/webiny-js/blob/v5.22.0/packages/api-file-manager/src/handlers/transform/loaders/sanitizeImageTransformations.ts#L1).  
When you request an image URL, you can add a query parameter `width`, e.g. `https:/cdn.com/files/image.png?width=640`, and you'll get the image closest to that size. Having predefined sizes protects you from someone abusing your API by requesting absurd image sizes, but also saves your storage space by limiting the amount of different sizes that will be stored.
