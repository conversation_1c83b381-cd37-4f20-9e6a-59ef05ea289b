---
id: aafeab44
title: Page Builder
description: No-code page builder. Build pages in minutes using a drag&drop interface.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- Features and capabilities of Page Builder app

</Alert>

![Webiny CMS - Page Builder](./assets/webiny-page-builder.jpg)

Page Builder is a powerful no-code editor for creating static and (coming soon) dynamic pages. The pages are made of elements that are React components and are also fully mobile responsive. Check out the main features below for more details.

## Features

### Editor

Page Builder features a powerful no-code drag&drop editor. Using the editor you can build page layouts that are mobile responsive, interactive, and fully SEO optimized. Unlike some other editors which work with static HTML elements, Webiny’s Page Builder works with React components. You can easily create your own elements via plugins. Those elements can contain business logic, be interactive, do API calls, and pretty much anything else that you can code in React.

<Alert type="info">

Checkout this tutorial on how to create [custom Page Builder elements](/docs/{version}/page-builder/extending/create-a-page-element).

</Alert>

### Build static pages

With the Page Builder, you can create both static pages as well as dynamic pages. Static pages are your typical landing pages, dashboards, and similar. Basically, any page that lives under a fixed URL. We call them “static” because of that fixed URL. The static pages can still contain dynamic React components.

### Build dynamic pages (Headless CMS + Page Builder)

<Alert type="info">

This feature is still work in progress, it’s not yet available to be used.

</Alert>

When we talk about dynamic pages, those are pages with a dynamic URL. For example `/blog/{slug}` . Page Builder replaces a tool like Gatsby, Hugo, Jekyll, or other static site generators. Using the same no-code editor you can build layouts and connect them to Webiny’s Headless CMS. Each time you publish a new record in the CMS, a Page Builder dynamic page is used to render this page on your site.

### Prerendering and delivery

All pages static and dynamic are prerendered and positioned as static assets to S3 from where they are delivered to users. Both the HTML as well as all the GraphQL queries are cached and saved as those static assets.

This type of architecture provides you with a scalable solution that can handle large traffic. It also has incremental builds natively supported, so no need for rebuilding and redeploying all pages when a new page is created.

<Alert type="info">

Checkout the Page Builder page delivery performance in the [benchmark section](/docs/{version}/performance-and-load-benchmark/introduction).

</Alert>

### Theme support

The Page Builder is shipped with a basic default theme that is used to render the pages you build with the editor. This theme is a starting point that you can take and modify to fit your desired brand style and visual requirements. We intentionally don't ship several themes with Page Builder, it's up to the developers to create those.

### Customizable

All elements of the Page Builder are fully customizable. You can hide built-in elements and categories, introduce new ones, adapt the default theme used to render the pages, and much more.

## When to consider using the Page Builder

1. When you have a project where the end customer is not as technical but wants the ability to build pages and layouts without any code.
2. When you have a need for a CMS like Squarespace, Wix, or Webflow, but you want it as part of a larger self-hosted solution that you can modify and customize.
3. When you want a more scalable alternative to existing static site generators.
4. When you want to save time in creating page layouts.
