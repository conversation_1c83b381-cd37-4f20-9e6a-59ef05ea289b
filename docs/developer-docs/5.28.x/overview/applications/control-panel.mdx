---
id: aafeab43
title: Control Panel
description: Ensure site reliability best practices through Webiny Control Panel.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- Webiny Control Panel overview

</Alert>

![Webiny Control Panel](./assets/webiny-control-panel.png)

At Webiny, we're reinventing what "self-hosted" means. By giving you the ability to keep your data in your control, we're very aware we're giving you also more work when compared to a typical SaaS solution. That work includes maintaining and scaling your underlying infrastructure where Webiny is hosted. It also includes handling things like backups and point-in-time recovery, monitoring, and so much more.

Traditionally SaaS has a lower total cost of ownership (TCO) when compared to self-hosted solutions, but we believe with Webiny, that's not the case.

Webiny, being architected on top of serverless services, significantly reduces the infrastructure and devops costs, but we're taking it further with Webiny Control Panel (WCP)

WCP is a SaaS cloud offering that sits on top of your self-hosted Webiny instance. It introduces a set of utilities to enforce operational excellence and improve the site reliability while additionally reducing the effort needed from devops teams. With WCP, we believe the TCO of Webiny is on-par, or even lower, than competitive SaaS solutions.

WCP is a paid-for feature that's will be available as a SaaS product through the webiny.com website.

## Features

This is a high-level overview of some of the notable features that will be available in WCP.

1. **Application monitoring**
   - Monitor your Webiny instance API performance, error rates, and overall API consumption.
2. **Cloud Cost Analysis**
   - Understand the consumption and the cost of the underlying AWS resources your Webiny instance is consuming.
3. **Staged Deployments**
   - Make code changes to your Webiny instance and roll them out safely to a fraction of users. Rollback instantly as needed.
4. **Point-in-time data recovery**
   - Up to 30 days old database snapshots you can restore to as needed.
   - Older backups you can store for more extended periods for compliance reasons.
5. **Alerting**
   - Get a notification on different events in your Webiny instance, be that API performance drop, a significant increase in cloud consumption, or a CI/CD deployment error.
