---
id: aafeab40
title: Overview
description: Overview of Webiny CMS the enterprise open-source serverless CMS.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- What is Webiny and what's our mission.
- What apps and features Webiny includes.

</Alert>

Welcome! 👋

Webiny is an open-source content management system designed for enterprises. It's built on top of the serverless infrastructure to enable great scalability and site reliability even in the most demanding periods.

Webiny stands out from the crowd with its unique feature set, architecture, and open-source community. In the following few sections, you'll get to know Webiny, what it can do, and how to best use it to its full advantage.

## Our mission

A bit of a back story about Webiny: the founding team behind the project was running a web agency for over ten years. In that time, we worked with pretty much every CMS under the sun. We were annoyed by the limitations and poor experience of the existing offering.

The self-hosted enterprise CMS solutions were black boxes that were immensely hard to scale, manage and maintain. The SaaS offerings ultimately dictated how we ought to build our products. We didn't have any customization ability and had to give away the control over how and where our data gets stored and delivered.

With that in mind, we set on a path to build a new kind of CMS.

**Open-source is at our core;** Webiny is a CMS you can customize and embed deeply into existing solutions. Hooks, lifecycle events and plugins give you complete control to adapt the platform to your needs.

**Self-hosted**; we believe your data should be under your control. In addition to the CMS, Webiny also gives you the tools and processes to effectively manage and scale your instance.

**Serverless is the next step in the evolution of infrastructure**. It unlocks both scale and reliability at a lower price point. Long gone are days of being afraid of peak events and flash crowds.

**No-code, low-code, and code-code** :) Webiny empowers both developers and non-technical users to create amazing websites and solutions.

## Feature Set

Webiny is not your traditional CMS, nor your typical headless CMS. Webiny includes a set of applications that cater to a broad set of cases. Applications that come with Webiny are:

1. [Headless CMS](/docs/{version}/overview/applications/headless-cms)
2. [Page Builder](/docs/{version}/overview/applications/page-builder)
3. [Form Builder](/docs/{version}/overview/applications/form-builder)
4. [File Manager](/docs/{version}/overview/applications/file-manager)
5. [Advanced Publishing Workflow](/docs/{version}/overview/applications/apw)
6. [Webiny Control Panel](/docs/{version}/overview/applications/control-panel) (also referred to as WCP)
7. [Admin](/docs/{version}/overview/applications/admin)

In addition to the listed applications, Webiny also includes a robust framework with numerous scaffold templates for when you want to extend and build your applications and APIs on top of the existing stack and principles.

Finally, Webiny also includes several noteworthy features:

1. [Multi-tenancy](/docs/{version}/overview/features/multi-tenancy)
2. [Multiple deployment environments](/docs/{version}/overview/features/multiple-environments)
3. [Webiny Framework](/docs/{version}/overview/features/framework)
4. ... and others

This combination of apps, features, serverless infrastructure in an open-source package makes Webiny a standout product from the crowd. One that can satisfy many requirements for even the most demanding enterprises.

## Webiny Plans

Webiny features three plan options:

1. Open-source (FREE)
2. Business (Paid with a free tier)
3. Enterprise (Paid)

All three plans are self-hosted with the ability to customize all aspects of the code-base. Check out our [pricing page](https://www.webiny.com/pricing) for more details on the plan features and differences.

## Technology

Webiny is built using a modern development toolchain that makes the experience for developers much more enjoyable. Technologies Webiny uses:

- [React](https://reactjs.org/)
- [GraphQL](https://graphql.org/)
- [Webpack](https://webpack.js.org/)
- [Babel](https://babeljs.io/)
- [Typescript](https://www.typescriptlang.org/)
- [Jest](https://jestjs.io/)
- [ESLint](https://eslint.org/)
- [Yarn 2](http://yarnpkg.com/)
- [Pulumi](http://pulumi.com/)
- and more

<Alert type="info">

To learn more, please look at the Tools and Libraries key topic.

</Alert>

## Contributing

We welcome anyone who wishes to be a part of our project and help us achieve our mission. If you're looking at code contributions, check [this link](https://github.com/webiny/webiny-js/blob/next/docs/CONTRIBUTING.md); For documentation contribution, check [this one](https://github.com/webiny/docs.webiny.com). If you want to have a chat with the dev team, you'll find us on our [community slack](https://www.webiny.com/slack).

## Staying Informed

Our project and documentation are constantly improving and changing. Anything important we post to our [blog](https://www.webiny.com/blog) and our [newsletter](https://landing.mailerlite.com/webforms/landing/g9f1i1).

Additionally, you can find helpful information on our Twitter handle [@WebinyCMS](https://twitter.com/WebinyCMS).
