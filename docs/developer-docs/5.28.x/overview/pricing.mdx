---
id: aafeab41
title: Pricing Tiers
description: How is Webiny priced and what are the available options.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- What is Webiny's business model?
- What is commercial open-source software?
- What are the different Webiny pricing tiers?

</Alert>

Webiny is an open-source project and at such, all our code is open to the public on our [GitHub repository](https://github.com/webiny/webiny-js). However building an open-source project and a business, in the past have been two different things. But lately, they have come together into what's today known as Commercial Open Source Software (COSS) and this is the model Webiny as a project has embraced.

<Alert type="info" title="Want to learn more about COSS?">

Here's is a great article on the topic of Commercial Open-Source:
https://www.webiny.com/blog/what-is-commercial-open-source

</Alert>

## Webiny's Dual License Model

Majority of Webiny's code base (over 95%) is licensed under the MIT open-source license. As such, you are given the right to use that code for any type of use-cases, projects, be commercial or not. You're allowed to make changes to the project and you can even resell it if you want to. We are a strong believer that open-source is the only way software will be written and distributed in the future and we are commited to such a cause ourselves.

However, as a fairly young company we need to ensure the long-term success of our project. Webiny is already a large project, and it's growing. As such it requires a dedicated team working full time to maintain, build and fix things, alongside the contributions coming from the community. To ensure their long-term sustainability, many open-source projects add a commercial aspect to their project.

This is usually done through either offering a cloud version, or a dual-license offering. Certain features might not be available under the free OSS license, but instead under a commercial license. Which might, or might not, be open-source. <u>**It's important to distiguish that open-source doesn't mean "free"**</u>. If only means the code is open for public to view, modify and enhance. Still to use the code, the user must adhear to the terms of the license.

<Alert type="info" title="Learn more about what it means to be open-source">

What is open-source? https://opensource.com/resources/what-open-source

</Alert>

Alongside the MIT license, Webiny also has a commercial license that comes with a cost, which unlocks additional features and functionality within the system. The revenue we get from this license is directly reinvested into building more features, and making the platform better and enjoyable to use. This is how we are building a sustainable model around our business, and ensuring the long-term development of our project.

## Webiny Pricing Tiers

Webiny comes in these three pricing tiers:

- **Free**
  - for personal and hobby projects
  - has only basic security options
  - doesn't support multi-tenancy
  - all code is licensed under the MIT open-source license
  - 100% free to use without any restrictions
- **Business**
  - for small team projects
  - supports advanced roles and permissions
  - supports optional addons such as Publishing Workflow and Headless Pages
  - supports multi-tenancy
  - all the code is open-source, but it's a mix of the MIT license and the Webiny's commerical license, users are allowed to make modifications to the code regardless of the license
  - price starts at $9 user/month, first user is free
- **Enterprise**
  - for teams working on critical projects
  - comes with SSO and team management
  - includes SLA support and consultancy services
  - all the code is open-source, but it's a mix of the MIT license and the Webiny's commerical license, users are allowed to make modifications to the code regardless of the license
  - price starts at $3,499 / month

For more details on the pricing tier, please visit our [pricing page](https://www.webiny.com/pricing). For any additional questions, please email us at [<EMAIL>](mailto:<EMAIL>).
