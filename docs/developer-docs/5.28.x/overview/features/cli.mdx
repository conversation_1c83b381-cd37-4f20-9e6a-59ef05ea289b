---
id: aafeab4a
title: CLI
description: Webiny CLI overview.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- Webiny **C**ommand **L**ine **I**nterface overview

</Alert>

Webiny CLI is a utility you'll interact with every time you want to deploy your project, build your project, get information on the URLs to your Admin app and APIs, as well as connect your project to the Control Panel.

The CLI is fully customizable and extendible, so you can change and overwrite the current implementation of the core commands and extend the CLI by registering new commands.

The CLI is also how you interact with the `scaffold` command, to extend your project and with the `pulumi` command to manage your AWS infrastructure.

Your CI/CD pipelines will utilize the CLI to deploy code changes and generally manage deployments.
