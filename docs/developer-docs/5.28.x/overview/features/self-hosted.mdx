---
id: aafeab4d
title: Self-hosted
description: Why consider self-hosting your CMS.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- Why you should consider a self-hosted CMS

</Alert>

Many solutions on the market have an open-source self-hosted option and a cloud-hosted SaaS option. However, this doesn't align well with the problems we want to solve with Webiny.

At Webiny, we believe privacy is a rising need for many organizations. However, giving your data to a 3rd party is a big ask, and many enterprises don't want to do that. They value their data and their customers' data too much, and rightfully so!

Traditionally, those organizations were stuck with either using an open-source solution they had to scale, manage, and operate at very high costs themselves or using a commercial solution that is a black box sitting in their cloud.

With Webiny, we want to empower anyone who wishes to self-host their own CMS at a fraction of the cost of other solutions in the market.

Webiny achieves this in several ways:

1. **Serverless infrastructure**
   - It reduces your infrastructure cost by 60-80% compared to running on virtual machines.
   - It reduces the needed devops effort by 40-60%.
   - It reduces the risk of scaling too slowly on peak events.
   - It ensures a much higher grade of security best practices.
2. **Webiny Control Panel**
   - It additionally streamlines the management and reduces the operational overhead of running a self-hosted solution.
   - It provides you with a monitoring solution to track the infrastructure cost and performance.
   - It reduces the risk of rolling out patches and code changes.

We believe you should have complete control and ownership of your data and say how it gets stored, who can access it, and how it gets delivered.
