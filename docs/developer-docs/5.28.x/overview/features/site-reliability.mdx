---
id: aafeab4f
title: Site reliability
description: Site reliability engineering, and often overlooked aspect of self-hosted open-source solutions.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- How we think about site reliability

</Alert>

Site reliability, or site reliability engineering (SRE), is a set of best practices that define how to operate a system focusing on its reliability.

It's often an overlooked component when choosing a solution. With SaaS solutions, you don't worry about it as the SaaS vendor handles it for you. And if they do it correctly, you know they are doing it well.

However, SRE plays a huge role when choosing a self-hosted solution, as the SRE activities are up to you and your in-house team to implement. This can be very challenging and costly, and it's one main advantage you'll find SaaS solutions have over self-hosted solutions.
It's a common mistake we see when picking an open-source solution. Teams often overlook the risk of not having the best practices and experience managing these solutions, ensuring their reliability.

At Webiny, since we are a self-hosted solution, it's essential for us to properly equip teams using our CMS with the knowledge and tools to operate and manage their Webiny instance. We do this through our Control Panel product, and we believe we can bring the overall reliability on par with a SaaS solution.

<Alert type="success">

To learn more about Webiny Control Panel, [click here](/docs/{version}/overview/applications/control-panel).

</Alert>

Additionally, we provide training via the consultancy service for enterprise customers to transfer and equip the in-house team with the needed knowledge and confidence provided directly by the team that built Webiny.
