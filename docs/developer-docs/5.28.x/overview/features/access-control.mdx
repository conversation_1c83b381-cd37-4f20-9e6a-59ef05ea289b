---
id: aafeab47
title: Access control
description: Webiny security and access control overview.
---

import { <PERSON>ert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- Overview of Webiny's access control feature

</Alert>

Access control (ACL) inside Webiny CMS has been designed for security and extensability in mind.

Through the `Security` module inside the Admin app, you can create user groups with different levels of permissions. Users added into the groups inherit the permissions. Permissions define what operations they can perform in the system.

Besides users and groups, Webiny also supports API tokens and personal access tokens (PAT) for when you wish to allow programmatic access to Webiny from a 3rd party system.

This is the standard approach you see in most systems on the market, but at Webiny, we're going a few steps further.

Webiny provides you with a security framework. You can quickly implement security into your applications using this framework, including API and client-side UI logic. For example, with the framework, you can implement access control to your custom GraphQL resolvers that are built on top of Webiny. You can also show or hide components in the UI based on the same principles.

In addition to the security framework, we invested significant efforts in how we manage the `identity` inside Webiny. `identity` is what the system sees. It's how the permissions are retrieved. This is a key to having a flexible ACL layer that can efficiently work with 3rd party IdPs like OKTA, Auth0, Cognito, and Active Directory. But not just "work" with those IdPs, but that the organization structure your company has inside those systems can work with Webiny as well, without needing to change things on your end, and when it comes to an IdP, that's rarely a possibility.
