---
id: aafeab4b
title: Multiple environments
description: How Webiny handles multiple deployment environments.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- Overview of how Webiny manages deployment environments

</Alert>

Webiny comes with with a CLI utitlity that you use to deploy your Webiny instance to your AWS cloud. The same utility is used to manage the deployment environments.

Under deployment environments, we refer to both long-lived environments, such as dev, qa, staging, prod, and any other your organization might use, and short-lived environments, one for your PR previews, branches, and local development.

Depending on the environment type, you can use different infrastructure as code stacks. For example, you might want to have your Elasticsearch cluster deployed in multiple zones for a production environment. At the same time, you might need a much smaller deployment for the short-lived environments.

Webiny CLI manages all these configurations for you. It also ensures the deployments can be done manually and through different CI/CD pipelines.

Propagating a build artifact through multiple stages is also handled internally, so you can ensure your "golden image" is built only once and then deployed to an appropriate environment.
