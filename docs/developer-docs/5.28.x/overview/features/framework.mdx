---
id: aafeab49
title: Framework
description: Webiny Framework - Serverless application development framework. (Open-source, NodeJs, AWS, ReactJs).
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- A quick overview of Webiny Framework

</Alert>

Webiny Framework is at the core of Webiny CMS. It is the foundation of all Webiny applications.

Because Webiny CMS comes with a framework, it means you have a powerful platform upon which you can customize existing applications on both the API side and the client-side. You can also use the same framework to build new apps using the same principles.

The framework contains everything you need to architect, build and deploy serverless applications.

Use the framework to:

- Extend any part of Webiny CMS
- Build full-stack applications
- Build GraphQL APIs
- Build multi-tenant SaaS applications

The framework is written in Typescript and caters to full-stack needs with Node.Js and ReactJS on the frontend. It also embeds IaC utility via Pulumi.

To make the whole experience of using the framework easier, we have built a set of scaffold templates to get you started.

<!-- TODO: add alert that links to tutorials and scaffolds -->
