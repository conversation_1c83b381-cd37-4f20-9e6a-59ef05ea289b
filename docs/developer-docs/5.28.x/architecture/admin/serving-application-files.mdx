---
id: aafea988
title: Serving Application Files
description: Learn how are React application files served via the deployed cloud infrastructure.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how are React application files served via the deployed cloud infrastructure

</Alert>

## Diagram

![Webiny Cloud Infrastructure - Admin Area - Serving Application Files](./assets/serving-application-files/webiny_admin_page_visit.png)

## Description

The diagram shows how HTTP requests travel through the deployed cloud infrastructure and how files are returned back to the client. This happens repeatedly as the user opens and navigates through the **Admin Area** React application. Note that, when talking about the API HTTP requests that the **Admin Area** React application issues, those are still issued to the Amazon CloudFront distribution that's deployed as part of the [API project application](/docs/{version}/architecture/api/overview).

## Steps

1. The HTTP request first reaches the Amazon CloudFront <diagram-letter>A</diagram-letter>.
2. The request is forwarded to the Amazon S3 bucket <diagram-letter>B</diagram-letter>.
3. Amazon CloudFront <diagram-letter>A</diagram-letter> receives the file from Amazon S3 <diagram-letter>B</diagram-letter>, caches it accordingly, and returns it back to the client.

## FAQ

### Are there any Amazon CloudFront caching rules in place?

All of the files that are served from the `/static/*` path are cached for 30 days. The rest is cached for five minutes. If need be, this can be additionally adjusted [via code](https://github.com/webiny/webiny-js/blob/v5/packages/cwp-template-aws/template/apps/admin/pulumi/cloudfront.ts).

### What is the `static` folder anyways?

The Admin Area React application is actually a [create-react-app](https://create-react-app.dev/) application, which, upon making a production build, places all of the static files (JS, CSS, images, ...) into this folder.
