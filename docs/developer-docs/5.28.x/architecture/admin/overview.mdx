---
id: aafea98b
title: Overview
description: Learn about the necessary cloud infrastructure resources on which the Admin Area project application relies on.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- the necessary cloud infrastructure resources on which the **Admin Area** project application relies on

</Alert>

## Diagram

![Webiny Cloud Infrastructure - Admin Area - Overview](./assets/overview/webiny_admin_overview.png)

## Description

The diagram gives an overview of the complete cloud infrastructure that's needed to host the Admin Area application.

As we can see, it consists of two resources - Amazon CloudFront <diagram-letter>A</diagram-letter> and Amazon S3 bucket <diagram-letter>B</diagram-letter>.

Select the **Serving the Application** diagram in the above diagram selector to see how HTTP requests travel through this setup and how static application files are returned back to the client.
