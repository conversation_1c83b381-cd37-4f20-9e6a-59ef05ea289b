---
id: aafea98a
title: Introduction
description: Learn what does the Admin Area project application represent.
---

import { Al<PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- what does the **Admin Area** project application represent

</Alert>

## The Admin Area Project Application

The **Admin Area** project application represents, as the name itself suggests, your administration area, which is a simple React single-page-application (SPA).

With only two cloud infrastructure resources, the Amazon CloudFront and Amazon S3, hosting single page applications is simple, and most importantly, cost-effective.

<Alert type="success">

Dive deeper by taking a closer look at this project application [in our GitHub repository](https://github.com/webiny/webiny-js/tree/v5/packages/cwp-template-aws/template/api).

</Alert>
