---
id: aafea97f
title: Introduction
description: Introduction to the Cloud Infrastructure key topic, which explains everything related to the cloud infrastructure Webiny relies on.
---

import { Al<PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- what are the default project applications, included in a newly created Webiny project
- what is the most complex project application, in terms of deployed cloud infrastructure

</Alert>

## Overview

By default, a Webiny project contains three [project applications](/docs/{version}/core-development-concepts/project-organization/project-applications):

1. **API** (`./api`) - your project's GraphQL API
2. **Admin Area** (`./apps/admin`) - your administration area
3. **Website** (`./apps/website`) - your public website

In order for these applications to actually work, a set of cloud infrastructure resources needs to be deployed into the cloud (your AWS account).

If we wanted to compare these applications by the complexity of needed cloud infrastructure, we could say that the **API** is the most complex one. This is simply because it requires the biggest number of different cloud infrastructure resources in order for it to work. On the other hand, the **Admin Area** and **Website** project applications basically just represent React applications, and to host these, we only need a couple of different cloud infrastructure resources.

<Alert type="info">

Visit the [Deployments](/docs/{version}/infrastructure/basics/introduction) key topics section to learn more about how Webiny approaches cloud infrastructure deployment.

</Alert>

In the following sections, we examine which cloud infrastructure resources get deployed for each project application, how they are working together, different configurations, and other related information.
