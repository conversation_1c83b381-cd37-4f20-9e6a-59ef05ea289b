---
id: aafea984
title: Overview
description: What are the necessary cloud infrastructure resources on which the API project application relies on
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- what are the necessary cloud infrastructure resources on which the **API** project application relies on

</Alert>

## Diagram

![Webiny Cloud Infrastructure - API - Overview](./assets/overview/webiny_api_overview.png)

<Alert type="info">

For brevity, the diagram doesn't include network-level cloud infrastructure resources, like region, VPC, availability zones, and so on. Check out the [Default VPC](/docs/{version}/architecture/api/overview-vpc-default) and [Custom VPC](/docs/{version}/architecture/api/overview-vpc-custom) topics if you're interested in that aspect of the deployed cloud infrastructure.

</Alert>

## Description

The diagram gives an overview of the complete cloud infrastructure that is deployed by default Webiny applications, which together form the **API** project application.

1. Security
2. I18N
3. File Manager
4. Page Builder (with Prerendering Service)
5. Form Builder
6. Headless CMS

In the following text, we briefly cover how each of the default Webiny applications are utilizing the shown cloud infrastructure.

### 1. Security

Although the Security application does work with multiple identity providers, by default, it works with Amazon Cognito, hence its icon in the lower section of the diagram <diagram-letter>e</diagram-letter>. The identity checks are performed from the GraphQL Handler <diagram-letter>C</diagram-letter> and Headless CMS <diagram-letter>D</diagram-letter> Lambda functions.

With Amazon Cognito, the application also extends the GraphQL schema located on the GraphQL Handler <diagram-letter>C</diagram-letter> Lambda function.

For storing data, the application relies on Amazon DynamoDB <diagram-letter>F</diagram-letter>.

### 2. I18N

The I18N application doesn't bring any additional cloud infrastructure resources. It just expands the GraphQL schema with its own types and resolvers, so we can say only the GraphQL Handler <diagram-letter>C</diagram-letter> Lambda function <diagram-letter>C</diagram-letter> gets affected by it.

For storing data, the application relies on Amazon DynamoDB <diagram-letter>F</diagram-letter>.

### 3. File Manager

Besides expanding the GraphQL schema located on the GraphQL Handler Lambda function <diagram-letter>c</diagram-letter>, the File Manager application also brings a couple of dedicated Lambda functions. These perform various file-related tasks like uploading, downloading, and also image optimizations. It also brings a single Amazon S3 bucket, in which the files are ultimately stored <diagram-letter>H</diagram-letter>.

For storing data and performing search queries, the application relies on Amazon DynamoDB <diagram-letter>F</diagram-letter> and Amazon ElasticSearch Service <diagram-letter>G</diagram-letter>, respectively.

### 4. Page Builder (with Prerendering Service)

The Page Builder application doesn't bring any special cloud infrastructure resources, but it does utilize the Prerendering Service which is comprised of four Lambda functions and a CloudWatch event <diagram-letter>I</diagram-letter>. The prerendering HTTP requests are issued over to the public website React application that is hosted in the Website project application <diagram-letter>J</diagram-letter>.

For storing data and performing search queries, the application relies on Amazon DynamoDB <diagram-letter>F</diagram-letter> and Amazon ElasticSearch Service <diagram-letter>G</diagram-letter>, respectively.

### 5. Form Builder

The Form Builder application doesn't bring any additional cloud infrastructure resources. It just expands the GraphQL schema with its own types and resolvers, so we can say only the GraphQL Handler <diagram-letter>C</diagram-letter> Lambda function gets affected by it.

For storing data and performing search queries, the application relies on Amazon DynamoDB <diagram-letter>F</diagram-letter> and Amazon ElasticSearch Service <diagram-letter>G</diagram-letter>, respectively.

### 6. Headless CMS

Besides expanding the GraphQL schema located on the GraphQL Handler <diagram-letter>C</diagram-letter> Lambda function, the Headless CMS application also brings an extra Lambda function which is responsible for hosting the dynamic Headless CMS GraphQL API <diagram-letter>D</diagram-letter>. Note that on the diagram, it is shown "behind" the GraphQL Handler <diagram-letter>C</diagram-letter> Lambda function.

For storing data and performing search queries, the application relies on Amazon DynamoDB <diagram-letter>F</diagram-letter> and Amazon ElasticSearch Service <diagram-letter>G</diagram-letter>, respectively.

## FAQ

### Why is Webiny using AWS Lambda to host a GraphQL server and not AWS AppSync?

There are a couple of reason for that.

First, implementing a GraphQL server using AWS Lambda enables us and our users to make additional customizations to server's behavior.

The default Webiny applications rely on this fact as well. For example, the I18N application gives you the ability to check what is the current locale that the user is on. The Security application gives you the ability to perform custom authentication and authorization checks inside of your resolvers. Finally, it also makes it possible for the Headless CMS application to generate different GraphQL schemas, based on the current I18N locale.

Secondly, you can test your GraphQL server easily with Jest, as you would do with any other JavaScript piece of code.

Finally, this approach makes it much easier for Webiny to be hosted on not just one cloud infrastructure provider (AWS), but on many more, like Microsoft Azure or Google Cloud Platform.

### Which GraphQL implementation are you using?

We started with [Apollo GraphQL Server](https://www.apollographql.com/docs/apollo-server/), but for Webiny version 5, we migrated to [GraphQL tools](https://www.graphql-tools.com/), because it's more lightweight and both easier to implement and grasp.

### Which Amazon API Gateway is deployed as part of the API application?

By default, we deploy [HTTP APIs](https://docs.aws.amazon.com/apigateway/latest/developerguide/http-api.html). This can be changed on your behalf, if need be.

### How is the DynamoDB data structured?

Webiny is following the approach called [single-table design](https://www.alexdebrie.com/posts/dynamodb-single-table/), which advocates for storing all the application data in a single DynamoDB table. So all of the Webiny applications are storing data in a single table, with distinct primary and secondary keys in order to avoid clashing of data.

<Alert type="info">

This doesn't mean you have to do the same. Using [Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi), you can easily deploy separate DynamoDB tables and structure your application's data in any way you want.

</Alert>

### Is Amazon ElasticSearch Service deployed into multiple AZs?

Currently it is deployed into a single AZ, just for cost reasons. But yes, production workloads should use two or three AZs. Check out the [Amazon ElasticSearch Service's FAQ page](https://aws.amazon.com/elasticsearch-service/faqs/) for more information.

Deploying the Amazon ElasticSearch Service <diagram-letter>g</diagram-letter> into multiple AZs can be achieved by adjusting [the cloud infrastructure code](https://github.com/webiny/webiny-js/blob/v5/packages/cwp-template-aws/template/api/pulumi_no_vpc/elasticSearch.ts#L9).
