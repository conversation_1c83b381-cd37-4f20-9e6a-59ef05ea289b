---
id: aafea986
title: File Download
description: Learn about the necessary cloud infrastructure resources on which the API project application relies on to download files.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how file downloads are handled by the deployed cloud infrastructure and application code

</Alert>

## Diagram

![Webiny Cloud Infrastructure - API - File Download](./assets/file-download/webiny_api_fm_download.png)

<Alert type="info">

For brevity, the diagram doesn't include network-level cloud infrastructure resources, like region, VPC, availability zones, and so on. Check out the [Default VPC](/docs/{version}/architecture/api/overview-vpc-default) and [Custom VPC](/docs/{version}/architecture/api/overview-vpc-custom) topics if you're interested in that aspect of the deployed cloud infrastructure.

</Alert>

## Description

The diagram shows what happens every time a client tries to download a binary file. The process consists of three steps:

1. The client issues a GET `/download/{file-key}` HTTP request, which reaches the Amazon API Gateway <diagram-letter>B</diagram-letter>, which then invokes the File Manager's Download Lambda function <diagram-letter>C</diagram-letter>.
2. The Download Lambda function extracts the file key from the received HTTP request, and then fetches the file from the S3 bucket <diagram-letter>C</diagram-letter>.
3. As a base64 encoded string, the file is returned to the Amazon API Gateway, which transforms it to an actual binary, and sends it back to the Amazon CloudFront.
4. Amazon CloudFront receives the file, caches it, and sends it back to the client.

<Alert type="info">

Visit the [Working with binary media types for REST APIs](https://docs.aws.amazon.com/apigateway/latest/developerguide/api-gateway-payload-encodings.html) article to learn more about how Amazon API Gateway deals with binary files.

</Alert>
