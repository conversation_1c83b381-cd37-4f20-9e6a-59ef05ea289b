---
id: aafea987
title: File Upload
description: Learn about the necessary cloud infrastructure resources on which the API project application relies on to upload files.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how file uploads are handled by the deployed cloud infrastructure and application code

</Alert>

## Diagram

![Webiny Cloud Infrastructure - API - File Upload](./assets/file-upload/webiny_api_fm_upload.png)

<Alert type="info">

For brevity, the diagram doesn't include network-level cloud infrastructure resources, like region, VPC, availability zones, and so on. Check out the [Default VPC](/docs/{version}/architecture/api/overview-vpc-default) and [Custom VPC](/docs/{version}/architecture/api/overview-vpc-custom) topics if you're interested in that aspect of the deployed cloud infrastructure.

</Alert>

## Description

The diagram shows what happens every time a client tries to upload a binary file.

To provide file upload functionality, the File Manager application relies on a method called **pre-signed POST payload**. Once certain conditions are met, the method enables uploading files directly to an S3 bucket, which is significantly more efficient than having the file travel through multiple cloud infrastructure resources.

<Alert type="info" title="Learn more">

If you want to learn more, feel free to check out [a blog post](https://www.webiny.com/blog/upload-files-to-aws-s3-using-pre-signed-post-data-and-a-lambda-function-7a9fb06d56c1) we wrote on this exact subject and which explains the process in detail.

</Alert>

The process consists of three steps:

1. The client issues a GraphQL HTTP request which instructs the GraphQL Server <diagram-letter>C</diagram-letter> to generate the necessary pre-signed POST data.
2. Once the client receives the pre-signed POST data, in a new POST HTTP request, the data, and the actual file are uploaded to the S3 bucket <diagram-letter>G</diagram-letter>.
3. Finally, another GraphQL request is issued, which instructs the GraphQL Server <diagram-letter>C</diagram-letter> to store the file meta data. The data is stored both in Amazon DynamoDB <diagram-letter>E</diagram-letter> and Amazon ElasticSearch Service <diagram-letter>F</diagram-letter>.

## FAQ

### Who can upload files?

Only users with proper permissions can perform file uploads. Both authentication and authorization are performed in steps one and three, on the GraphQL Server <diagram-letter>c</diagram-letter>.

### Is there a way to define a maximum file upload size?

Yes, by going into the File Manager's general settings section, and manually entering the appropriate values.

![File Manager Settings](./assets/file-upload/fm-settings.png)
