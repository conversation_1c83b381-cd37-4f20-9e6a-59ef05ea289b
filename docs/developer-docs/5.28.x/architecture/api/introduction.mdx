---
id: aafea985
title: Introduction
description: Learn about the necessary cloud infrastructure resources on which the API project application relies on.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- what does the **API** project application represent
- gain awareness about the default VPC and custom VPC deployments

</Alert>

## The API Project Application

The **API** project application represents your project's HTTP API, whose main purpose is to serve the default **Admin Area** and **Website** applications, via a GraphQL interface.

But do note that the **API** application is much more than just a simple GraphQL interface. The default Webiny applications not only extend the application's GraphQL schema, but also bring additional processes and cloud infrastructure resources in order to achieve their goal. For example, in order to store and serve files, and optimize images, the File Manager application brings a couple of dedicated Lambda functions, and an S3 bucket. Another example is the Page Builder application, which also brings a couple of dedicated Lambda functions that deal with prerendering of pages.

Of course, the application can additionally grow in terms of code and cloud infrastructure on your behalf, if need be.

## Different Cloud Infrastructure Setups

Note that the cloud infrastructure, defined within the **API** project application, is defined as two different setups - **development** and **production**.

This is simply because, for development purposes, not all of the production cloud infrastructure resources are needed. For example, for improved security posture, the production setup will deploy all of your Lambda functions into a custom [Virtual Private Cloud (VPC)](https://aws.amazon.com/vpc/) with private subnets. It will also deploy your [Amazon Elastic Search Service](https://aws.amazon.com/elasticsearch-service/) into multiple availability zones (AZs), in order to provide high availability.

<Alert type="info">

Both setups can be found inside of the **API** project application's folder:

- development - `api/pulumi/dev`
- production - `api/pulumi/prod`

Take a look at the [code in our GitHub repository](https://github.com/webiny/webiny-js/tree/v5/packages/cwp-template-aws/template/api) to get a better sense of what's different on the code level.

</Alert>

The other two project applications, **Admin Area** and **Website**, do not have different setups for different environments, as it's simply not needed.
