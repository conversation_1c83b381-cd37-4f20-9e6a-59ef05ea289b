---
id: aafea983
title: Default VPC
description: Learn how the necessary cloud infrastructure resources are deployed within the default VPC.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how the necessary cloud infrastructure resources are deployed within the default VPC

</Alert>

## Diagram

![Webiny Cloud Infrastructure - API - Default VPC](./assets/default-vpc/webiny_api_overview_vpc_default.png)

## Description

As mentioned in the [introduction](/docs/{version}/architecture/api/introduction) section, the **API** project application's cloud infrastructure comes with two setups - development and production. The difference between the two is a bit different setup when it comes to networking and Amazon ElasticSearch Service. In the production setup, these are configured a bit differently, mainly for improving your project's security posture and availability.

In the development setup, your project is deployed into the default Virtual Private Cloud (VPC), which is automatically created for every AWS Account.

<Alert type="info">

Virtual Private Clouds (VPCs) is a topic that requires some general networking knowledge and knowledge on AWS-specific concepts like regions, availability zones, different network gateways, and so on. Be sure to [read about it](https://aws.amazon.com/vpc/) before going through this section.

You can also check this [Understanding the Default Virtual Private Cloud](https://www.rackspace.com/blog/aws-201-understanding-the-default-virtual-private-cloud) article. It clearly lays out a lot of useful information about AWS's default VPC setup.

</Alert>

In the diagram above, we can see that the default VPC setup (a setup controlled by AWS) consists of three subnets <diagram-letter>c</diagram-letter> <diagram-letter>d</diagram-letter> <diagram-letter>e</diagram-letter> that span across three availability zones (AZs). All of these subnets are public subnets, since they can both receive and send network traffic to the public internet. Note that the number of availability zones may vary, depending on the region you're deploying into (some regions only have two AZs).

We can also see that Lambda functions are located in all three subnets. This is because, by default, AWS runs Lambda functions in multiple AZs to ensure it is highly available in case of an AZ failure. The same cannot be said for the Amazon ElasticSearch Service <diagram-letter>h</diagram-letter>, which is only deployed in a single AZ <diagram-letter>C</diagram-letter>.

As mentioned, the default VPC setup can be a reasonable choice when it comes to development environments. But for production, since some of the cloud infrastructure resources may require that they are not exposed to the public internet, the [Custom VPC](/docs/{version}/architecture/api/overview-vpc-custom) may be a better solution.

## FAQ

### Is Amazon ElasticSearch Service deployed into multiple AZs?

In the `dev` setup, it is deployed into a single AZ, just for cost reasons. But yes, production workloads should use two or three AZs, hence the `prod` setup. Check out the [Amazon ElasticSearch Service's FAQ page](https://aws.amazon.com/elasticsearch-service/faqs/) for more information.
