---
id: aafea993
title: GraphQL Requests
description: Learn about the necessary cloud infrastructure resources on which the API project application relies on to perform graphql requests.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how GraphQL HTTP requests are handled by the deployed cloud infrastructure and application code

</Alert>

## Diagram

![Webiny Cloud Infrastructure - API - GraphQL Requests](./assets/graphql-requests/webiny_api_gql_request.png)

<Alert type="info" title="What you’ll learn">

For brevity, the diagram doesn't include network-level cloud infrastructure resources, like region, VPC, availability zones, and so on. Check out the [Default VPC](/docs/{version}/architecture/api/overview-vpc-default) and [Custom VPC](/docs/{version}/architecture/api/overview-vpc-custom) topics if you're interested in that aspect of the deployed cloud infrastructure.

</Alert>

## Description

The diagram shows how HTTP requests (GraphQL queries and mutations) travel through the deployed cloud infrastructure. Request are primarily issued by the **Admin Area**, or **Website** applications, but of course, can be issued by other clients as well.

The steps are the following:

1. The GraphQL HTTP request first reaches the Amazon CloudFront <diagram-letter>A</diagram-letter>.
2. The request is forwarded to the Amazon API Gateway <diagram-letter>B</diagram-letter>.
3. The Amazon API Gateway invokes the GraphQL Server Lambda function <diagram-letter>C</diagram-letter>.
4. Depending on the issued GraphQL operation, the Lambda function's code may issue one or more requests to other cloud infrastructure resources:
   1. Amazon Cognito <diagram-letter>D</diagram-letter> to perform identity authentication
   2. Amazon DynamoDB <diagram-letter>E</diagram-letter> or Amazon ElasticSearch Service <diagram-letter>F</diagram-letter> to perform database queries
5. Once the code execution has completed, an HTTP response is returned back to the Amazon API Gateway <diagram-letter>B</diagram-letter>.
6. The Amazon API Gateway <diagram-letter>B</diagram-letter> forwards the request to the Amazon CloudFront <diagram-letter>A</diagram-letter>, which again forwards it back to the client.
