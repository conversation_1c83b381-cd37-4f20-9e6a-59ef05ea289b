---
id: aafea995
title: Introduction
description: Learn what does the Website project application represent.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- what does the **Website** project application represent
- what are the common problems when serving a public website
- briefly, how Webiny approaches these problems

</Alert>

## The Website project application

The **Website** project application represents your public website.

In terms of the code the application holds, essentially, we're talking about a classic React application that renders pages created using the Page Builder application.

In terms of the necessary cloud infrastructure, things are not as simple as it's the case with the [Admin Area](/docs/{version}/architecture/admin/introduction) project application. Basically, public websites do not work well if they are served as a classic React single-page-application (SPA) that's relying on the client-side rendering approach.

This is because of the following two factors:

1. UX is not good because every time a users open a webpage, they are presented with a loading screen.
2. SEO suffers significantly. Search engines and web crawlers are still having problems interpreting the dynamically generated HTML, and some are not supporting this at all.

<Alert type="info">

Learn more about different rendering approaches in [this article](https://developers.google.com/web/updates/2019/02/rendering-on-the-web) published by Google.

</Alert>

Because of this, Webiny developed its own **Prerendering Service**, which prerenders your website pages. In other words, when a page is published, it captures the complete HTML code, and relevant data for it in advance. And, when an actual website visitors visits one of your pages, the page is just served as a simple static HTML file, with the page data directly embedded in it. This approach resolves both of the issues mentioned above.

<Alert type="success">

The explanation on prerendering of pages, presented above, is a very brief one. This is intentional, as this key topic is focused on the cloud infrastructure Webiny deploys and not on the prerendering itself. If you want to learn more about how the Page Builder application handles page rendering, visit the [Prerendering Pages](/docs/{version}/architecture/website/prerendering-pages) key topic.

</Alert>

So, the cloud infrastructure contains additional resources and it also depends on the **API** project application, which hosts the mentioned **Prerendering service** and which coordinates the complete prerendering process. All this, and more, is examined in the following topics.

<Alert type="info">

Dive deeper by taking a closer look at this project application [in our GitHub repository](https://github.com/webiny/webiny-js/tree/v5/packages/cwp-template-aws/template/apps/website).

</Alert>
