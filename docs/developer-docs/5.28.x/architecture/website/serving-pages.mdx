---
id: aafea994
title: Serving Pages
description: Learn how the prerendered pages are statically served to actual website visitors.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how the previously prerendered pages are statically served to actual website visitors

</Alert>

## Diagram

![Webiny Cloud Infrastructure - Website - Serving Pages](./assets/serving-pages/webiny_website_visit.png)

## Description

The diagram shows how pages, prerendered as part of the **Prerendering Pages** flow, are served to actual website visitors.

It's important to point out that, although the initial HTML and page metadata are always returned from the upper Amazon S3 bucket <diagram-letter>B</diagram-letter>, all of the static assets (CSS, JS, images, ...) are still returned from the lower one <diagram-letter>C</diagram-letter>, to which the React application was uploaded during the deployment of the **Website** application. These static files are always located on the `/static/*` path (for examples `/static/js/2.3ceb1741.chunk.js`), so defining appropriate redirect and caching rules on the Amazon CloudFront <diagram-letter>A</diagram-letter> is not hard to do.

<Alert type="info">

Check out this specific set of rules in the [infrastructure code](https://github.com/webiny/webiny-js/blob/v5/packages/cwp-template-aws/template/apps/website/pulumi/delivery.ts#L40) in our GitHub repository.

</Alert>

This way, the static assets don't have to be uploaded twice (to both Amazon S3 buckets <diagram-letter>B</diagram-letter> <diagram-letter>C</diagram-letter>), making the deployment process a bit faster and easier to maintain.

<Alert type="info">

Learn more about the **Prerendering Pages** flow by selecting it in the upper diagram selector.

</Alert>

## Steps

1. Users visit a particular URL which leads to your website, with a web browser of their choice.
2. The HTTP request, issued by the web browser, reaches the Amazon CloudFront <diagram-letter>A</diagram-letter>, which forwards it to the Amazon S3 bucket <diagram-letter>B</diagram-letter>. This is where the prerendered content was previously stored.
3. The page HTML, located in the S3 bucket <diagram-letter>B</diagram-letter>, is returned to the Amazon CloudFront <diagram-letter>A</diagram-letter>, which caches it first, and then returns it back to the user's browser. Note that if the page for given URL wasn't found, a default **not found** page is returned instead.
4. User's browser receives the HTML and starts to fetch the linked static assets (JS, CSS, images, ...). As mentioned in the description section, these assets are always located on the `/static/*` path, and the HTTP requests for these are always redirected to the lower Amazon S3 bucket <diagram-letter>C</diagram-letter>, on the Amazon CloudFront <diagram-letter>A</diagram-letter>.

## FAQ

### Are there any Amazon CloudFront caching rules in place?

By default, all of the files that are served via the `/static/*` path are cached for 30 days. The rest is cached for 30 seconds. If need be, this can be additionally adjusted [via code](https://github.com/webiny/webiny-js/blob/v5/packages/cwp-template-aws/template/apps/website/pulumi/delivery.ts#L40).

### What is the `static` folder anyways?

The Website React application is actually a [create-react-app](https://create-react-app.dev/) application, which, upon creating a production build, places all of the static files (JS, CSS, images, ...) into this folder.
