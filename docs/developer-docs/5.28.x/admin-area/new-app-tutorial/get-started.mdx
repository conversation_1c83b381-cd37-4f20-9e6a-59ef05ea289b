---
id: aafea954
title: Get Started
description: Learn how to use the existing Extend Admin Area scaffold in order to start extending your Admin Area application in no time.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU’LL LEARN">

- how to use the **Extend Admin Area** scaffold to get started in no time

</Alert>

<Alert type="info" title="Can I use this?">

In order to follow this tutorial, you must use Webiny version **5.9.0** or greater.

</Alert>

## Extend Admin Area Scaffold

The best way to start extending the Admin Area application would be to use the [Extend Admin Area](/docs/{version}/admin-area/new-app-tutorial/scaffolding) scaffold, which will automatically generate all of the fundamental application pieces for us. On the application code level, this includes the necessary plugins which create the following:

- in the Admin Area application: a new route, menu item, and a [CRUD view](/docs/{version}/admin-area/new-app-tutorial/scaffolding#new-webiny-admin-area-module)
- in the GraphQL API: supporting GraphQL query and mutation operations

So, in our terminal of choice, from our project root, let's run the following command:

```
yarn webiny scaffold
```

Running this command should give us the following list of available scaffolds:

![Scaffolds List](./assets/get-started/scaffolds-list.png)

Once we've selected the **Extend Admin Area** scaffold from the list, we'll get presented with the following three questions:

1. the path to our GraphQL API's plugins folder (`api/code/graphql/src/plugins` by default)
2. the path to our Admin Area application's plugins folder (`apps/admin/code/src/plugins` by default)
3. the name of the initial entity

For the first two, we can just use the default paths by simply pressing the **Enter** key. As the answer to the last question, we'll enter **CarManufacturer**.

Once we've answered all of the questions, after a quick confirmation step, scaffolding will start and eventually, we should end up with the following output in our terminal:

![Extend Admin Area - Full Output](./assets/get-started/scaffold-full-output.png)

## Using the Watch Command

In order to actually see the changes that the **Extend Admin Area** scaffold performed and continue developing, we will start two separate watch sessions. This can be achieved via the [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) command.

### GraphQL API

The following command will start a new watch session on our GraphQL API application code:

```
yarn webiny watch api/code/graphql --env dev
```

A couple of notes here.

#### Watch Path

Instead of `api/code/graphql`, note that we could've also passed `api` as the first argument. But, that would initialize a watch session on the whole **API** project application, which is most probably not what we want. We only want to watch the GraphQL API application code that's deployed as part of it, located within the `api/code/graphql` path.

#### Initial Deployment

Upon initializing a new watch session, all of the application code changes that were performed during the scaffolding process will automatically be deployed into the cloud. Meaning, all of the new GraphQL query and mutation operations should be immediately visible in the remote GraphQL schema.

<Alert type="success" title="Misplaced GraphQL API URL?">

Running the `yarn webiny info` command in your Webiny project folder will give you all of the relevant project URLs, including the URL of your GraphQL API.

</Alert>

### Admin Area Application

The following command will start a new watch session on our Admin Area application code:

```
yarn webiny watch apps/admin --env dev
```

#### No Initial Deployment

In contrast to the [GraphQL API](#graph-ql-api) development, where the application code needs to be redeployed into the cloud as we're making changes to it, Admin Area application can be developed locally. Meaning, once the watch command is run, no automatic deployments will be performed.

## Final Result

### Watch Commands

The following screenshot shows the two watch commands run side-by-side in two separate terminal sessions:

![Watching Our Application Code](./assets/get-started/watch-sessions.png)

### Admin Area Application

Once the two watch sessions have been started, in our Admin Area application, we should be able to see the new **Car Manufacturers** module and, via the generated [CRUD view](/docs/{version}/admin-area/new-app-tutorial/scaffolding#new-webiny-admin-area-module), perform essential CRUD operations.

![New Car Manufacturers Module](./assets/get-started/scaffolds-admin-area.png)

<Alert type="success">

By default, the Admin Area application should be locally accessible via [http://localhost:3001](http://localhost:3001).

</Alert>

## Further Development

Once we got the initial **Car Manufacturers** Admin Area application module working, we can proceed by modifying and extending it to our needs.

Usually, on the GraphQL API side, we'd want to start by extending the [`CarManufacturer` entity](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/api/code/graphql/src/plugins/scaffolds/carManufacturers/entities/CarManufacturer.ts) and our [GraphQL schema](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/api/code/graphql/src/plugins/scaffolds/carManufacturers/typeDefs.ts) with new attributes and fields, respectively. Once we're done, we'd then continue by propagating the same changes to the Admin Area application, by adding new form fields to the [`CarManufacturersForm`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/apps/admin/code/src/plugins/scaffolds/carManufacturers/views/CarManufacturersForm.tsx) React component and updating relevant [GraphQL queries and mutations](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/apps/admin/code/src/plugins/scaffolds/carManufacturers/views/hooks/graphql.ts).

To learn more, continue reading the following [Extending Entities](/docs/{version}/admin-area/new-app-tutorial/extending-entities) section.

## FAQ

### Which database is used in the generated GraphQL API application code?

The generated GraphQL API application code relies exclusively on [Amazon DynamoDB](https://aws.amazon.com/dynamodb/) for storing and retrieving data. Of course, you're certainly free to [bring your own](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api#storing-and-retrieving-data) databases and import different database clients, if need be.
