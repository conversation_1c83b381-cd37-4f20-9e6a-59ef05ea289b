---
id: aafea958
title: Admin App Scaffold
description: Learn how to use the Extend Admin Area scaffold.
---

import { Al<PERSON> } from "@/components/Alert";

<Alert type="info" title="Can I use this?">

The **Extend Admin Area** scaffold (previously **Admin Area Package**) received a major overhaul in [Webiny 5.9.0](/docs/release-notes/5.9.0/changelog). Note that this guide is not relevant for projects that are using Webiny 5.8.0 and lower.

</Alert>

<Alert type="success" title="What you'll learn">

- main features of the **Extend Admin Area** scaffold
- how to continue developing on top of the generated application code

</Alert>

## Overview

The **Extend Admin Area** scaffold creates a new module in the Webiny Admin Area React application and extends your GraphQL API with
supporting [CRUD](https://en.wikipedia.org/wiki/Create,_read,_update_and_delete) query and mutation operations.

Note that, when we say GraphQL HTTP API, by default, we're referring to the one deployed as part of the **API** project application, located within the [`api/code/graphql`](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cwp-template-aws/template/api/code/graphql) folder.

<Alert type="info">

Learn more about the **API** project application on the cloud infrastructure level in the [Cloud Infrastructure](/docs/{version}/architecture/api/introduction) key topics section.

</Alert>

## Features

### New Webiny Admin Area Module

As mentioned in the overview, the scaffold creates a new module in the Webiny Admin Area React application, which consists of the following:

1. a basic **CRUD view** which consists of a data list and a form, enabling the user to perform basic CRUD operations
2. a new route (URL) on which the CRUD view is being rendered
3. a new item in the main menu, which, when clicked, redirects the user to the new route (URL)

As an example, during the scaffold's wizard, if we were to enter **CarManufacturer** as the initial data model name, we'd end up with the following:

1. a CRUD view which lets us perform basic CRUD operations on car manufacturer entries
2. a new `/car-manufacturers` route
3. a new **Car Manufacturers** item in the main menu

![New Webiny Admin Area Module](./assets/scaffolding/scaffolds-admin-area.png)

<Alert type="info">

Note that initially all entries consist of two text fields: `title` and `description`. Naturally, based on your requirements, you can remove these fields or add new ones. More on this in the [Development](#development) section below.

</Alert>

### New CRUD Query and Mutation Operations

The **Extend Admin Area** scaffold also extends your GraphQL API with supporting query and mutation operations.

In order to do this, internally, the scaffold is simply utilizing the existing **Extend GraphQL API** scaffold. To learn more about its main features and how to continue developing on top of the application code it generates, please visit [its dedicated guide](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api).

### Multi-Locale Support Enabled

By default, Webiny Admin Area is a multi-locale system. This means entries like pages, page categories, forms, settings, and so on, are always created for a specific locale.

The generated application code follows the same idea and there aren't any additional steps that you need to take in order to enable multi-locale support. By simply changing the locale via the locales selector located in top-right corner of the screen, users get to manage a completely separate set of data, only available for the selected locale.

![Locales Selector](./assets/scaffolding/locales-selector.png)

<Alert type="info">

The locales selector is visible only when there are two or more locales to choose from. Locales are created via the **Languages** module.

</Alert>

### No Additional Cloud Infrastructure Resources

The scaffold does not add any additional cloud infrastructure resources. The generated application code is simply added to the existing Admin Area React application code ([`apps/admin/code`](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cwp-template-aws/template/apps/admin/code)).

Of course, if additional cloud infrastructure resources are needed, you're free amend the [existing cloud infrastructure code](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cwp-template-aws/template/apps/admin/pulumi), and reference the added and deployed resources in your application code.

<Alert type="success">

Note that the supporting GraphQL API has its own cloud infrastructure code, located in the [`api/pulumi`](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cwp-template-aws/template/api/pulumi) folder. So, if you're planning to add new cloud infrastructure resources that will be referenced within your GraphQL API application code, it makes more sense to introduce cloud infrastructure code changes there, and not in the [`apps/admin/pulumi`](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cwp-template-aws/template/apps/admin/pulumi), linked above.

</Alert>

## Development

### Usage

In order to use this scaffold, from your project root, simply run the `webiny scaffold` command:

```bash
yarn webiny scaffold
```

Then, from the list of all available scaffolds, select **Extend Admin Area** and follow the on-screen instructions.

### Essential Files

The following are the most essential files and folders that are generated in the scaffolding process.

<Alert type="info">

By default, all of the files are generated in a new folder, located at `apps/admin/code/src/plugins/scaffolds/{dataModelName}`, for example `apps/admin/code/src/plugins/scaffolds/carManufacturers`.

</Alert>

[`index.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-admin-app-module/template/index.ts)

This is the entry file, which exports the [`menus`](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-admin-app-module/template/index.ts#L1) and [`routes`](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-admin-app-module/template/index.ts#L2) plugins.

[`menus.tsx`](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-admin-app-module/template/menus.tsx)

Export a single [`MenuPlugin`](https://github.com/webiny/webiny-js/blob/9e490409f0367c596f9ee112ad57c265c74ff8d6/packages/api-page-builder/src/plugins/MenuPlugin.ts#L20) plugin that's responsible for adding the new main menu item.

[`routes.tsx`](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-admin-app-module/template/routes.tsx)

Exports a single [`RoutePlugin`](https://github.com/webiny/webiny-js/blob/9e490409f0367c596f9ee112ad57c265c74ff8d6/packages/app/src/plugins/RoutePlugin.tsx#L8) plugin that's responsible for creating a new route (URL), on which the CRUD view will be rendered. This is where the actual React component that form the CRUD view are referenced.

[`views/`](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cli-plugin-scaffold-admin-app-module/template/views)

Contains all of the React components and [hooks](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cli-plugin-scaffold-admin-app-module/template/views/hooks) that form the CRUD view. The main parent component is exported in the [`views/index.tsx`](https://github.com/webiny/webiny-js/blob/v5.10.0/packages/cli-plugin-scaffold-admin-app-module/template/views/index.tsx#L10) file.

### First Deploy

#### Admin Area React Application

Since the Admin Area React application can be developed locally, once you've completed the scaffold's wizard, there's no need to perform any additional deployments.

#### GraphQL API

Once you've completed the scaffold's wizard, in order to actually see the changes made to your GraphQL API, you need to deploy them. This can be done as usual, via the [`webiny deploy`](/docs/{version}/core-development-concepts/basics/project-deployment) command, or, even easier, if you're about to jump straight into coding, by running the [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) command. This command will not only deploy the changes, but also start a new watch session, which will automatically redeploy further application code changes, as you perform them. More on this below.

### Development Using the Watch Command

The most straightforward way to further develop on top of the generated code would be via the [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) command.

To get started, from your project root, you can run the following two commands.

#### Admin Area React Application

```
yarn webiny watch apps/admin --env {env}
```

This command initiates a new local development server and a watch session on the Admin Area React application. In other words, the React application is running locally, and all changes are immediately visible in user's browser as they make them.

#### GraphQL API

```
yarn webiny watch api/code/graphql --env {env}
```

This command initiates a watch session on the GraphQL API application code and ensures that every change we perform is automatically deployed into the cloud.

<Alert type="info">

Check out the [Extend GraphQL API - Development](#development) section for more information on how to continue developing the supporting GraphQL API.

</Alert>

## FAQ

### How does security (authentication and authorization) work?

Please note that, by default, the authentication and authorization logic isn't included in the generated code.

Luckily, with a couple of built-in utilities, this can be relatively easily added. Please check out the [existing tutorials](/docs/{version}/overview/features/security) to learn how to implement these on your own.

### Can Webiny's multi-tenancy feature be utilized with this scaffold?

Yes, but do note that since multi-tenancy is part of Webiny's enterprise offering, the relevant multi-tenancy code is not included in the generated application code.

For more information on multi-tenancy, we recommend you check out the [Multi-Tenancy](/docs/{version}/overview/features/multi-tenancy) key topic. Also, for any implementation-related questions, feel free to contact us directly via our community [Slack](https://www.webiny.com/slack).
