---
id: aafea951
title: Change Logo
description: Learn how to replace the default logo with your own.
---

import { Alert } from "@/components/Alert";

<Alert type="info" title="Can I Use This?">

In order to follow this guide, you must use Webiny version **5.21.0** or greater.

</Alert>

<Alert type="success" title="What you'll learn">

- how to replace the default Webiny logo with your own React component

</Alert>

## Overview

Changing the default logo is a very quick and straightforward process. The `@webiny/app-serverless-cms` packages exposes an `AddLogo` plugin specifically for this purpose.

<Alert type="success">

Your logo will be visible in two locations: in the top app bar, and in the navigation drawer.

</Alert>

## Replace the Logo

To replace the logo, go to `apps/admin/code/src/App.tsx` in your Webiny project, and apply the following code changes:

```tsx apps/admin/code/src/App.tsx
import React from "react";
import { Admin, Plugins, AddLogo } from "@webiny/app-serverless-cms";
import { Cognito } from "@webiny/app-admin-users-cognito";
import "./App.scss";

// Import your logo image
import logoPng from "./logo.png";

export const App = () => {
  return (
    <Admin>
      <Cognito />
      <AddLogo logo={<img src={logoPng} />} />
    </Admin>
  );
};
```

You can specify _any_ React element as a value of the `logo` prop. The system will simply make sure that it is rendered in the correct place.
It is _your_ responsibility to style your element accordingly (apply width, padding, color, etc.).

## Conditional Styling

Since there are two locations where your logo will be rendered, there's a neat way to tell _where exactly_ your component is currently being rendered.
For this, you'll have to create a React component which will render your logo.

```tsx
// Import the "useTags" hook
import { useTags } from "@webiny/app-serverless-cms";

// Import your logo image
import logoPng from "./logo.png";

const MyLogo = () => {
  // Fetch tags from context.
  const { location } = useTags();

  // "location" is a tag with a value of "navigation", if your logo is currently being rendered inside the navigation drawer.
  const style = location === "navigation" ? { paddingLeft: 5 } : {};

  return <img src={logoPng} style={style} />;
};

// Use your new component with the AddLogo plugin.
<AddLogo logo={<MyLogo />} />;
```

> Notice the use of the `useTags` hook. It's a utility which can come in really handy when you want to tag sections of your React app, so you components can render differently depending on their location in the component hierarchy. This topic will be covered in more details in a dedicated article.

With this, your logo will have different styles applied to it, depending on where it's being rendered.
