---
id: aafeab75
title: Environments
description: Learn what are environments and how they work in a Webiny project.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- what are environments
- how are environments handled in a Webiny project

</Alert>

<Alert type="warning" title="Just want to deploy?">

This article covers the general idea of environments and how they are embedded into a Webiny project. To learn how to deploy your project using [Webiny CLI](/docs/{version}/core-development-concepts/basics/webiny-cli), please visit the [Deploy your project](/docs/{version}/core-development-concepts/basics/project-deployment) guide.

</Alert>

## What are environments?

Your project can be deployed into multiple environments.

This means that you can have multiple "copies" of your project up and running at the same time, each having its own name and purpose.

Every project most certainly has an environment called **production**. This is the environment that represents "the real thing" - the system that's being used by real users. It's in your best interest to ensure this system is always online and the code that makes it into this environment is well tested. In order to achieve this, you deploy your project to a number of pre-production environments.

For example, you might have multiple **development** environments, to which developers can deploy their changes, ready to be tested by the rest of the team. Once the changes have been approved, an additional round of testing can be done in a **preview**, or more commonly called, **staging** environment. This is the environment that's the closest to the **production** environment in terms of the code, different configurations, cloud infrastructure, and so on.

<Alert type="info">

For best practices on how to organize your environments and other workflows, check out the [Workflows](/docs/{version}/core-development-concepts/ci-cd/workflows) section.

</Alert>

## Environments in a Webiny project

Every project application can be deployed into several different environments. For example, we might have three applications in our project, and for each, we might want to have three different environments: **dev**, **staging**, and **production**.

![Environments.](./assets/environments/environments.png)

Note that Webiny does **not** sync environments across your project applications. Meaning, if you want to deploy your entire project into a specific environment, you need to deploy every application into it. So, in the case of the above project, you would need to deploy all three applications.

Fortunately, Webiny makes it easy to work with different environments, using the [Webiny CLI](/docs/{version}/core-development-concepts/basics/webiny-cli) and [Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi). Be sure to check out the [Deploy your project](/docs/{version}/core-development-concepts/basics/project-deployment) guide to learn how to deploy a Webiny project.

<Alert type="danger" title="Watch out for extra costs">

Deploying multiple project environments into the same AWS account may incur additional cost. Check out the [Cloud Infrastructure](/docs/{version}/architecture/introduction) section for more information.

</Alert>

## FAQ

### How many different environments can a project have?

The answer is: as many as needed. There is no limit on the count of different environments you can deploy.

### Depending on the environment, can a different cloud infrastructure be deployed?

This can be done. Since the cloud infrastructure is defined using code, nothing prevents you from using a couple of `if` statements to determine which resources need to be deployed.

### Is it reasonable to have different cloud infrastructure deployed based on the environment?

In certain cases, this might be reasonable. For example, you can reduce development costs by not deploying custom VPCs (and related cloud infrastructure resources) for development environments. You only deploy the full set of cloud infrastructure resources into **staging** and **production** environments.

<Alert type="info">

Depending on the environment, the **API** project application is deployed as two different sets of cloud infrastructure resources - development and production. Visit the [API Overview - Default VPC](/docs/architecture/deployment-modes/development) key topic to learn more.

</Alert>
