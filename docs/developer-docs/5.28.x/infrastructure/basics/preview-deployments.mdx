---
id: aafea97c
title: Preview Deployments
description: Learn how to preview cloud infrastructure deployments, before actually executing them.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to preview cloud infrastructure deployment for your project application

</Alert>

## Introduction

During the project development, there is a chance that the deployed cloud infrastructure needs to change. New resources might be added, existing resources configured differently, and some may even need to be destroyed.

When doing more significant changes, it might be helpful to preview the deployment process that you're about to execute. More specifically, you would certainly like to know if the deployment you're about to execute destroys a mission-critical resource, e.g. a database or a file storage resource.

## The Preview Flag

In order to avoid performing unintended cloud infrastructure changes, you can use the preview flag upon executing the [deploy](/docs/{version}/core-development-concepts/basics/project-deployment#the-deploy-command) command. Consider the following examples:

```bash
yarn webiny deploy api --env dev --preview
yarn webiny deploy apps/admin --env dev --preview
yarn webiny deploy apps/website --env dev --preview
```

Executing these commands won't perform any changes to your cloud infrastructure resources. They would just list all of the changes that would be applied, for example:

![Previewing Deploy Command](./assets/preview-deployments/preview-terminal.png)

## Debugging

If you run into an error while running a deployment preview, to get additional information and logs about it, you can append the `--debug` argument to the `webiny deploy` command. For example:

```bash
yarn webiny deploy api --env dev --preview --debug
```

This can significantly help in debugging underlying deployment ([Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi)) errors, since without it, in some cases the returned error report doesn't contain enough useful information. We've also seen cases in which the report would actually be misleading and even incorrect, making the debugging process much harder for the user.
