---
id: aafeab7a
title: Use AWS Profiles
description: How to deploy your Webiny project using different AWS profiles.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to deploy your Webiny project using different AWS profiles

</Alert>

## AWS Profiles

AWS profiles enable you to store multiple AWS credentials on your system, and then, when the time comes, to use specific credentials in order to perform a command against the AWS cloud.

<Alert type="info">

Read more about profiles and how to set them up in the official [AWS documentation article](https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-profiles.html).

</Alert>

## How To Use

While executing deployment-related commands via the [Webiny CLI](/docs/{version}/core-development-concepts/basics/webiny-cli), for example [`deploy`](/docs/{version}/core-development-concepts/basics/project-deployment) or [`destroy`](/docs/{version}/infrastructure/basics/destroy-cloud-infrastructure), if not specified, Webiny will always use the `default` AWS profile.

A different profile can be specified via the `AWS_PROFILE` environment variable, which you can set in a couple of ways.

### Using `.env` File

The easiest way to do it would be using the `.env` file, located in your project root, for example:

```bash .env
AWS_PROFILE=my-profile
```

The advantage of this approach is that you don't need to set the `AWS_PROFILE` via your terminal, but it always stays in the `.env` file, which is automatically loaded when executing mentioned deployment-related commands.

### Using Terminal

Apart from setting it via the shown `.env` file, the `AWS_PROFILE` environment variable can also be set using a terminal of your choice, and a command that's available on your operating system.

For example, on Linux or MacOS, you can use the [`export`](https://www.geeksforgeeks.org/export-command-in-linux-with-examples/) command:

```
export AWS_PROFILE=my-profile
```

On Windows, you can utilize the [`set`](https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/set_1) and [`setx`](https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/setx) commands, for example:

```
setx AWS_PROFILE my-profile
```

Once the `AWS_PROFILE` has been properly set via one of the shown methods, you can proceed with the deployment of your Webiny project.

## FAQ

### Is there a way to check if my profile was set properly?

If you have the [AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-welcome.html) installed on your system, in your terminal, you can run the following command:

```
aws sts get-caller-identity
```

This should give an output similar to the following:

```
{
    "Account": "x",
    "UserId": "y",
    "Arn": "arn:aws:iam::x:user/z"
}
```
