---
id: aafeab79
title: Configure AWS Credentials
description: How to configure your AWS credentials for programmatic access
---

import { Alert } from "@/components/Alert";
import { ExternalLink } from "@/components/ExternalLink";
import YouTube from "@/components/YouTube";

<Alert type="success" title="What you’ll learn">

- how to configure your AWS credentials for programmatic access
- how to deploy Webiny in a more controlled and secure manner using our **Deploy Webiny Project** AWS CloudFormation template

</Alert>

This article guides you through configuring your AWS credentials for programmatic access. At the end of the tutorial, you will have your API keys and region configured to successfully deploy Webiny to your AWS cloud.

If you prefer the video version, check out our YouTube video below:

<YouTube id="qmtDRmplMG4" />

---

## Step 1: Create Your AWS Account

<Alert type="info" title="Already have an account?">

In that case skip to [step 2](#step-2-create-a-user-for-programmatic-access) to create your programmatic user.

</Alert>

To create your AWS account and set up your IAM credentials we should first navigate to the [AWS Console page](https://aws.amazon.com/console/):

![aws-sign-in](./assets/configure-aws-credentials/aws-sign-in.png)

Next click on `Create a new AWS account`:

![aws-login](./assets/configure-aws-credentials/aws-login.png)

Now input your credentials and create your account:

![aws-register](./assets/configure-aws-credentials/aws-register.png)

---

## Step 2: Create a User for Programmatic Access

Once you're registered, login and head to the AWS Management Console and select **IAM** under **Security, Identity & Compliance**:

![aws-iam-selection](./assets/configure-aws-credentials/aws-iam-selection.png)

Click on **IAM** and select **Users** under **Access Management**:

![aws-iam-users](./assets/configure-aws-credentials/aws-iam-users.png)

Click on **Create user** to create the account with the IAM credentials:

![aws-create-user](./assets/configure-aws-credentials/aws-create-user.png)

Here you enter a multi-step process, and the first step is to create a **User name** for the credentials.

<Alert type="warning" title="Programmatic access">

Make sure to select the **Programmatic access** checkbox before moving to the next step.

</Alert>

![aws-user-details](./assets/configure-aws-credentials/aws-user-details.png)

In the next step, you define the access level for the new user. Select **Attach existing Policies** from the three options available. Then, choose the **AdministratorAccess** policy by checking the checkbox next to it. When you're ready, click the **Next** button.

<Alert type="warning" title="Need a more secure deployment option?">

In case your organization cannot use the `AdministratorAccess` policy or you need to have a more strictly defined set of permissions,
please see the ["Deploy Webiny Project" AWS CloudFormation Template](#deploy-webiny-project-aws-cloud-formation-template) section.

</Alert>

![aws-user-permissions](./assets/configure-aws-credentials/aws-user-permissions.png)

Once all the steps are complete, you'll reach the **Review and create** page, which should look similar to image below. If everything is correct, click the **Create user** button.  

You can also add tags to new users. If you don't want to add any tags to your new user, you can simply click the **Create user** button.

![aws-user-review](./assets/configure-aws-credentials/aws-user-review.png)

Once the user is created, you will receive a success message: "User created successfully." Now, we will proceed to create the **Access key** for this new user.

![aws-new-user-created-successfully](./assets/configure-aws-credentials/aws-new-user-created-successfully.png)

Click on the newly created user from the Users home page. This will display the user details page. Next, click on the **Create access key** link.

![access-key-creation](./assets/configure-aws-credentials/access-key-step-1.png)

Select the **Command Line Interface (CLI)** option, and check the `I understand the above recommendation and want to proceed to create an access key` confirmation box at the end. Then, click the **Next** button.

![access-key-use-case](./assets/configure-aws-credentials/access-key-step-2.png)

The next step is optional, where you can set the description tag. Click the **Create access key** button.

![set-description-tag](./assets/configure-aws-credentials/access-key-step-3.png)

On successful creation of the Access key, you will see a message: **Access key created**.  
This is the only time that the secret access key can be viewed or downloaded. You cannot recover it later. However, you can create a new access key any time. Copy this *Access key* and **Secret access key**.

![access-key-created-successfully](./assets/configure-aws-credentials/access-key-step-4.png)

<Alert type="warning" title="Save the credentials">

Once you navigate off this screen, you will not be able to see the credentials any more. If you lose them, you will need to delete the user and create a new one.

</Alert>

### "Deploy Webiny Project" AWS CloudFormation Template

<Alert type="info">

Please note that an IAM user with programmatic access is a prerequisite for this step.
If you don't have one, please refer to the [previous step](/docs/{version}/infrastructure/aws/configure-aws-credentials#step-2-create-a-user-for-programmatic-access).

</Alert>

In order to deploy your Webiny project in a more controlled and secure manner, you can utilize our **Deploy Webiny Project** AWS CloudFormation template. Via three IAM user groups, the template deploys necessary ([least-privilege](https://en.wikipedia.org/wiki/Principle_of_least_privilege)) set of permissions into your AWS account, which also automatically get linked with a preexisting Amazon IAM user of your choosing (the user you plan to use upon running deployments).

To get started, simply click [here](https://console.aws.amazon.com/cloudformation/home?#/stacks/create/review?templateURL=https://webiny-public.s3.us-east-2.amazonaws.com/cloudformation/DEPLOY_WEBINY_PROJECT_CF_TEMPLATE.yaml&stackName=DeployWebinyProject).

<Alert type={"info"}>

Before deploying the AWS CloudFormation template, you might want to inspect it. You can do that by
visiting our <ExternalLink href={"https://github.com/webiny/webiny-js/blob/next/docs/DEPLOY_WEBINY_PROJECT_CF_TEMPLATE.yaml"}>
GitHub repository

</ExternalLink> or <ExternalLink href={"https://webiny-public.s3.us-east-2.amazonaws.com/cloudformation/DEPLOY_WEBINY_PROJECT_CF_TEMPLATE.yaml"}>downloading the template from our S3 bucket</ExternalLink>.
  
</Alert>

[//]: # "Commented out as the provided info and screenshots are outdated."
[//]: # "![aws-cloudformation-create-stack](./assets/configure-aws-credentials/create-stack.gif)"
[//]: #
[//]: # "The resource creation will take a few minutes. After completion, you will see the completion logs:"
[//]: #
[//]: # "![aws-cloudformation-step-5](./assets/configure-aws-credentials/cloudformation-step-5.png)"
[//]: #
[//]: # "Afterwards, you can check out the newly created resources in the [IAM service panel](https://console.aws.amazon.com/iamv2/home?#/users)."
[//]: # "Also, if you head over to the previously created user (`webiny-cli` in our case), you will see the attached policy and group:"
[//]: #
[//]: # "![aws-cloudformation-step-6](./assets/configure-aws-credentials/cloudformation-step-6.png)"

## Step 3: Configure the Programmatic Access

Now that you have the **Access key ID** and **Secret access key** it's time to store them on your development machine.

### Unix setup

If you're on UNIX, create a folder named `.aws` inside your user folder, that's `~/.aws`. Inside that folder place a file called `credentials`. So the full path is `~/.aws/credentials`.

### Windows setup

On Windows machines, navigate to your user folder. That's `C:\Users\<USER>\` (replace `USERNAME` with your actual username). Inside create a new folder named `.aws`, and inside the `.aws` folder create a file named `credentials`. The full path should be like this: `C:\Users\<USER>\.aws\credentials`.

---

Now that we have our `credentials` file, edit the file and populate it like so:

```plain
[default]
aws_access_key_id = PASTE_ACCESS_KEY_ID_HERE
aws_secret_access_key = PASTE_SECRET_ACCESS_KEY_HERE
```

The word `default` inside the square brackets is your profile name. If you don't explicitly configure a profile name, the `default` profile is used by AWS CLI and SDK.

<!-- ## Step 4: Configure the default region

The last thing to configure is your default region. It's important to configure the region that is closest to your geographical location for optimal network latency. You can find which region is the most suitable for you using, for example, https://www.cloudping.info/.

To see the region codes, visit https://docs.aws.amazon.com/general/latest/gr/rande.html#regional-endpoints.

If you don't want to bother with regions, use `us-east-1`.

---

### Unix setup

Inside your `.aws` folder, the one we created in the previous step, create a file called `config`. So the full path is `~/.aws/config`.

### Windows setup

Inside your `.aws` folder, the one we created in the previous step, create a file called `config`. The full path should be like this: `C:\Users\<USER>\.aws\config`.

---

Now that we have our `config` file, edit the file and populate it like so:

```plain
[default]
region = YOUR_AWS_REGION
```

This means that for your `default` profile, that particular region is used. -->

And that's it! You now have your AWS credentials, your default region, and you're ready to deploy Webiny.

<Alert type="info" title="Using a Different AWS Profile Name?">

If you would like to use a different name for your AWS profile, you can do that too. Check out the [Use AWS Profiles](/docs/{version}/infrastructure/aws/use-aws-profiles) how-to guide for more information.

</Alert>

## FAQ

### Is there a way to check if my profile was set properly?

If you have the [AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-welcome.html) installed on your system, you can run the following command:

```
aws sts get-caller-identity
```

This should give an output similar to the following:

```
{
    "Account": "x",
    "UserId": "y",
    "Arn": "arn:aws:iam::x:user/z"
}
```
