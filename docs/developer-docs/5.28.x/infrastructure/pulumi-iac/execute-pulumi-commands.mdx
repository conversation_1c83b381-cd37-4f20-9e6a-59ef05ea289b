---
id: aafeab78
title: Execute Pulumi Commands
description: Learn how to execute Pulumi commands via Webiny CLI.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- why you might want to execute Pulumi commands
- how to execute Pulumi commands via Webiny CLI

</Alert>

## Introduction

Out of the box, [Webiny CLI](/docs/{version}/core-development-concepts/basics/webiny-cli) provides all of the essential commands that you might need in order to [preview](/docs/{version}/infrastructure/basics/preview-deployments), [deploy](/docs/{version}/core-development-concepts/basics/project-deployment), and [destroy](/docs/{version}/infrastructure/basics/destroy-cloud-infrastructure) your project applications' cloud infrastructure resources.

<Alert type="info">

To deploy necessary cloud infrastructure, by default, Webiny relies on Pulumi, a modern infrastructure as code framework. Find out more in the following [IaC with Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi) key topic.

</Alert>

And while in most cases these commands are enough to get the job done, still, there is a chance that you might need to use other specific Pulumi commands, via the [Pulumi CLI](https://www.pulumi.com/docs/reference/cli/).

For example, in case of the [pending operations](/docs/{version}/core-development-concepts/basics/project-deployment#the-current-deployment-has-x-resource-s-with-pending-operations) issue, you will most certainly want to execute the [`pulumi stack export`](https://www.pulumi.com/docs/reference/cli/pulumi_stack_export/) and [`pulumi stack import`](https://www.pulumi.com/docs/reference/cli/pulumi_import/) commands, in order to repair your cloud infrastructure [state files](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi#state-files).

Unless you've installed the Pulumi CLI manually, trying to execute these in your terminal will result with an error, saying that the `pulumi` command was not found. Basically, this happens because Webiny doesn't install the Pulumi CLI globally on your system. It's set up locally and separately for every Webiny project, by downloading necessary binaries into a temporary folder, located in your Webiny project.

Because of this, Webiny CLI provides the `pulumi` command, which enables you to run any Pulumi command you might need. And not only that. Using Pulumi CLI through Webiny CLI will free you from setting some of the necessary environment variables and configuration params, because Webiny CLI handles these internally. For example, by specifying the environment via the `--env` argument, the Webiny CLI will automatically select the right [Pulumi stack](https://www.pulumi.com/docs/intro/concepts/stack/) for you. No need for extra commands that would otherwise be needed, if we were using the standalone Pulumi CLI.

<Alert type="info">

Pulumi's [programming model](https://www.pulumi.com/docs/intro/concepts/programming-model/) consists of three key concepts: Pulumi [project](https://www.pulumi.com/docs/intro/concepts/project/), [program](https://www.pulumi.com/docs/intro/concepts/programming-model/#programs), and [stack](https://www.pulumi.com/docs/intro/concepts/stack/).

</Alert>

## The `pulumi` Command

Using the `pulumi` command is as simple as follows:

```bash
# Run the "stack export" command within the "api" project application,
# for the "dev" environment ("dev" Pulumi stack)
yarn webiny pulumi api --env dev -- stack export

# Run the "config set foo bar --secret" command within the "apps/admin" project
# application, for the "dev" environment ("dev" Pulumi stack)
yarn webiny pulumi apps/admin --env dev -- config set foo bar --secret

# Run the "refresh" command within the "apps/website" project application,
# for the "prod" environment ("prod" Pulumi stack)
yarn webiny pulumi apps/website --env prod -- refresh
```

As we can see, upon executing the `pulumi` command, we must provide three things:

1. the project application folder (for example `apps/website`)
2. the environment, via the `--env` argument
3. the actual Pulumi command

Note that the `--`, used in shown examples, is not an error. The actual Pulumi command you want to execute must come after it, otherwise you will end up with unexpected results.

## FAQ

### What is the package Webiny is using to execute Pulumi commands?

We are using our own [Pulumi SDK (@webiny/pulumi-sdk)](https://github.com/webiny/webiny-js/tree/next/packages/pulumi-sdk), which is, essentially, a small wrapper over the original Pulumi CLI, that allows us to use it programmatically, via JavaScript code.

### Where is the Pulumi CLI downloaded?

Pulumi CLI is downloaded into the following folder in your Webiny project: `node_modules/@webiny/pulumi-sdk/pulumi`.

### Which should I use, Webiny CLI or Pulumi CLI?

Although both can be used to get the job done, we recommend you always rely on the Webiny CLI. This is because of the reasons mentioned in this guide:

- no need to install the Pulumi CLI manually
- no need to set extra environment variables and configuration params
- anything that can be achieved with the Pulumi CLI can also be achieved with Webiny CLI

## Troubleshooting

### I'm receiving the `--yes must be passed in to proceed when running in non-interactive mode` error

This error may sometimes pop up because [Webiny's Pulumi SDK](https://github.com/webiny/webiny-js/tree/next/packages/pulumi-sdk) package runs all Pulumi commands in a non-interactive mode. Because of this, sometimes you will need to pass the `--yes` flag in order to confirm the execution of the Pulumi command you're about to execute.
