---
title: New Pin Modal Dialog
description: Learn how to create a simple modal dialog over which users can create new pins.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU’LL LEARN">

- how to create a simple modal dialog for creating new pins (with [Ant Design](https://ant.design/docs/react/introduce) React library)
- how to execute GraphQL mutations using already configured [Apollo Client](https://www.apollographql.com/docs/react/v2)

</Alert>

<Alert type="info" title="Can I use this?">

In order to follow this tutorial, you must use Webiny version **5.18.0** or greater.

</Alert>

<Alert type="info">
The code that we cover in this section can also be found in our GitHub [examples repository](https://github.com/webiny/webiny-examples/tree/master/create-custom-application/04-react-application/03-new-pin-modal-dialog). Also, if you'd like to see the complete and final code of the application we're building, check out the [`full-example`](https://github.com/webiny/webiny-examples/tree/master/create-custom-application/full-example) folder.

</Alert>

## Creating the New Pin Modal Dialog

It's time to create the **New Pin** modal dialog, which the users will open by clicking on the **&#10753;** icon, located in the top right corner of the screen.

![Open New Pin Modal Dialog](./assets/new-pin-modal-dialog/plus-header-button.png)

For starters, let's create the [`NewPinModal`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/03-new-pin-modal-dialog/pinterest-clone/app/code/src/components/NewPinModal.tsx#L26) React component in the [`pinterest-clone/app/code/src/components/NewPinModal.tsx`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/03-new-pin-modal-dialog/pinterest-clone/app/code/src/components/NewPinModal.tsx) file:

```tsx pinterest-clone/app/code/src/components/NewPinModal.tsx
import React, { useCallback } from "react";
import { Modal } from "antd";
import { Form, Input } from "antd";
import { useMutation } from "@apollo/react-hooks";
import { message } from "antd";
import gql from "graphql-tag";

// The mutation which we'll issue on form submissions.
const CREATE_PIN = gql`
  mutation CreatePin($data: PinCreateInput!) {
    pins {
      createPin(data: $data) {
        id
        title
        description
      }
    }
  }
`;

type Props = {
  visible: boolean;
  onClose: Function;
};

const NewPinModal = (props: Props) => {
  // A reference to the form rendered below.
  const [form] = Form.useForm();

  // A simple mutation for creating new pins.
  const [createPin] = useMutation(CREATE_PIN);

  // Once the form is submitted and all field validation is passing, this callback will get executed.
  const onFinish = useCallback(async ({ title, description }) => {
    await createPin({
      variables: {
        data: { title, description }
      },
      refetchQueries: ["ListPins"]
    });
    message.success(`New pin ${title} created successfully!`);
    form.resetFields();
    props.onClose();
  }, []);

  // Submits the form and only triggers the `onFinish` callback if all fields are valid.
  const onModalOk = useCallback(() => form.submit(), []);

  // Reset the form and close the modal dialog.
  const onModalCancel = useCallback(() => {
    form.resetFields();
    props.onClose();
  }, []);

  return (
    <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} form={form} onFinish={onFinish}>
      <Modal
        title="New Pin"
        width={700}
        visible={props.visible}
        onOk={onModalOk}
        onCancel={onModalCancel}
      >
        <Form.Item name={["title"]} label="Title" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item name={["description"]} label="Description">
          <Input.TextArea />
        </Form.Item>
      </Modal>
    </Form>
  );
};

export default NewPinModal;
```

As we can see, in order to create the modal dialog, we're completely relying on the [Ant Design](https://ant.design/docs/react/introduce) React library. To create the modal dialog, we're using the [`Modal`](https://ant.design/components/modal/) component, to create the form, we're using the [`Form`](https://ant.design/components/form/) component, and we're using the [`Input`](https://ant.design/components/input/) and [`Input.TextArea`](https://ant.design/components/input/#components-input-demo-textarea) components to render the basic form fields.

<Alert type="info">

For more information on Ant Design React library and all of the components in offers, make sure to check out its [documentation](https://ant.design/docs/react/introduce).

</Alert>

Do note that the form in the modal dialog doesn't include the cover image field yet. We'll add this field later, when we'll be covering the file upload functionality.

Finally, when it comes to interacting with our GraphQL API, in order to issue the [`CreatePin`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/03-new-pin-modal-dialog/pinterest-clone/app/code/src/components/NewPinModal.tsx#L9) GraphQL mutation, we're using [Apollo Client's](https://www.apollographql.com/docs/react/v2) [`useMutation`](https://www.apollographql.com/docs/react/v2/data/mutations/#executing-a-mutation) React hook.

<Alert type="info">

For the purposes of this tutorial, we've included all of the React component's code in a single file. But, you can reorganize the code into multiple files, if you prefer that.

</Alert>

## Add the New Pin Button to Header

Now that we have the **New Pin** modal dialog ready to go, let's create the mentioned **&#10753;** icon, which will enable users to actually open it.

So, let's update the [`Layout`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/03-new-pin-modal-dialog/pinterest-clone/app/code/src/components/Layout.tsx#L12) component with the following lines of code:

```diff-js pinterest-clone/app/code/src/components/Layout.tsx
+ import React, { useState } from "react";
+ import { Divider, Button } from "antd";
+ import { PlusOutlined } from "@ant-design/icons";
import { Link } from "@webiny/react-router";
import logo from "~/images/logo.png";
+ import NewPinModal from "./NewPinModal";

/**
 * The default layout component which you can use on any page.
 * Feel free to customize it or create additional layout components.
 */
const Layout = (props: { className: string }) => {
+    const [visible, setVisible] = useState(false);

    return (
        <div className="layout">
            {/* We're using the `nav` tag for rendering the header. */}
            <nav>
                <div>
                    <Link to={"/"}>
                        <img src={logo} className="logo" alt={"Pinterest Clone"} />
                    </Link>
                </div>
+                <div>
+                    <Button
+                       onClick={() => setVisible(true)}
+                       type="primary"
+                       size={"large"}
+                       shape="circle"
+                       icon={<PlusOutlined />}
+                   />
+                </div>
            </nav>
            <Divider style={{ margin: 0 }} />

            {/* The pages are rendered within the `main` tag. */}
            <main className={props.className}>{props.children}</main>
+           <NewPinModal visible={visible} onClose={() => setVisible(false)} />
        </div>
    );
};

export default Layout;
```

## Final Result

With all of the code updates in place, we should be able to see the **&#10753;** icon in the header and open the **New Pin** dialog by clicking on it. And, of course, we should be able to create new pins by simply filling the form and pressing the **OK** button. Feel free to create as much pins as you like!

![New Pin Modal Dialog](./assets/new-pin-modal-dialog/new-pin-modal-dialog.png)

<Alert type="info">

Once the form is submitted, the [`CreatePin`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/03-new-pin-modal-dialog/pinterest-clone/app/code/src/components/NewPinModal.tsx#L9) GraphQL mutation will be issued. If you're using a browser like [Google Chrome](https://www.google.com/chrome/index.html), you can see that via the Developer Tools's [Network](https://developer.chrome.com/docs/devtools/network/) tab.

</Alert>

Note that, of course, since we didn't create the homepage yet, nothing will actually be shown on it after we've submitted the form. This is something we'll be covering in the next section.
