---
title: Homepage
description: Learn how to create a simple homepage that lists the pins created by different users.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU’LL LEARN">

- how to create a simple homepage that lists the pins created by different users
- how to execute GraphQL queries using already configured [Apollo Client](https://www.apollographql.com/docs/react/v2)
- quick introduction to the [`@webiny/react-router`](https://github.com/webiny/webiny-js/tree/next/packages/react-router) library

</Alert>

<Alert type="info" title="Can I use this?">

In order to follow this tutorial, you must use Webiny version **5.18.0** or greater.

</Alert>

<Alert type="info">

The code that we cover in this section can also be found in our GitHub [examples repository](https://github.com/webiny/webiny-examples/tree/master/create-custom-application/04-react-application/04-homepage). Also, if you'd like to see the complete and final code of the application we're building, check out the [`full-example`](https://github.com/webiny/webiny-examples/tree/master/create-custom-application/full-example) folder.

</Alert>

## Overview

Now that we have our **New Pin** modal dialog working as expected, we're can continue by creating the homepage. As seen in previous sections, the homepage lists all pins that were created by different users.

Like in the previous section, fetching the list of created pins can be easily done via the [Apollo Client](https://www.apollographql.com/docs/react/v2/) and its [`useQuery`](https://www.apollographql.com/docs/react/v2/data/queries/#executing-a-query) React hook. A bit trickier part is rendering the pins in a mosaic layout, which is how the original [Pinterest](https://www.pinterest.com/) website does it.

![Pinterest - The Mosaic Layout](./assets/homepage/pinterest-mosaic-layout.png)

Luckily, we can achieve this pretty easily with a small React library called [`react-columned`](https://www.npmjs.com/package/react-columned), which we'll need to add to our React application. Once we have that, we can start working on our homepage by adjusting the existing [`Home`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/04-homepage/pinterest-clone/app/code/src/plugins/routes/home.tsx#L25) React component.

<Alert type="info">

We already had a quick mention of the [`Home`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/02-layout/pinterest-clone/app/code/src/plugins/routes/home.tsx#L7) React component in the [Optional Steps](/docs/{version}/custom-app-tutorial/react-application/layout#optional-steps) section, in the [Layout](/docs/{version}/custom-app-tutorial/react-application/layout) section of this tutorial.

</Alert>

## Creating the Homepage

For starters, let's add the mentioned [`react-columned`](https://www.npmjs.com/package/react-columned) library with the following command:

```bash
yarn workspace pinterest-clone-app add react-columned
```

Once we have that, we can start working on our homepage. The following is the final [`Home`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/04-homepage/pinterest-clone/app/code/src/plugins/routes/home.tsx#L25) React component that's responsible for rendering it, located in the [`pinterest-clone/app/code/src/plugins/routes/home.tsx`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/04-homepage/pinterest-clone/app/code/src/plugins/routes/home.tsx) file:

```tsx pinterest-clone/app/code/src/plugins/routes/home.tsx
import React from "react";
import { RoutePlugin } from "@webiny/app/plugins/RoutePlugin";
import { Link, Route } from "@webiny/react-router";
import { Empty } from "antd";
import { useQuery } from "@apollo/react-hooks";
import gql from "graphql-tag";
import Columned from "react-columned";
import Layout from "~/components/Layout";
import blankImage from "~/images/blankImage.png";

const LIST_PINS = gql`
  query ListPins($sort: PinsListSort, $limit: Int, $after: String, $before: String) {
    pins {
      listPins(sort: $sort, limit: $limit, after: $after, before: $before) {
        data {
          id
          title
        }
      }
    }
  }
`;

// The home page.
const Home = () => {
  const listPinsQuery = useQuery(LIST_PINS, { variables: { limit: 100 } });
  const { data = [] } = listPinsQuery?.data?.pins?.listPins || {};

  return (
    <Layout className={"home"}>
      {data.length > 0 ? (
        /* If we have pins to show, use the `Columned` component to render them in a mosaic layout. */
        <Columned>
          {data.map(item => (
            /* Every pin should link to its details page. */
            <Link key={item.id} to={"/pins/" + item.id}>
              {/* If the pin contains an image, we show it. Otherwise, we show a placeholder image. */}
              <img title={item.title} alt={item.title} src={item.coverImage || blankImage} />
            </Link>
          ))}
        </Columned>
      ) : (
        /* If there are no pins to show, render "Nothing to show." message. */
        <Empty description={"Nothing to show."} />
      )}
    </Layout>
  );
};

// We register routes via the `RoutePlugin` plugin.
export default new RoutePlugin({
  route: <Route path="/" exact component={Home} />
});
```

<Alert type="info">

In order for the above code to work, make sure to copy and paste the [`blankImage.png`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/04-homepage/pinterest-clone/app/code/src/images/blankImage.png) file into the [`pinterest-clone/app/code/src/images`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/04-homepage/pinterest-clone/app/code/src/images) folder.

</Alert>

<Alert type="success">

Notice how we've used the tilde (`~`) character to target the parent [`images`](https://github.com/webiny/webiny-examples/tree/master/create-custom-application/04-react-application/04-homepage/pinterest-clone/app/code/src/images) and [`components`](https://github.com/webiny/webiny-examples/tree/master/create-custom-application/04-react-application/04-homepage/pinterest-clone/app/code/src/components) folders. Using that instead of something like `../../../` can make our import statements easier to read and maintain.

</Alert>

As we can see, the file contains the [`Home`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/04-homepage/pinterest-clone/app/code/src/plugins/routes/home.tsx#L25) React component, in which we're simply issuing the `ListPins` GraphQL query, and then, based on the received data, rendering each pin as an image and link to the **Pin Details** page (more on this soon). And, in order to achieve the mentioned mosaic layout, the pins are wrapped with the `Columned` component, imported from the added [`react-columned`](https://www.npmjs.com/package/react-columned) library.

Furthermore, note that, by default, the React application that's generated during the [Full Stack Application](/docs/{version}/core-development-concepts/scaffolding/full-stack-application) scaffolding process uses [`@webiny/react-router`](https://github.com/webiny/webiny-js/tree/next/packages/react-router) library, which is a plugins-based React application router. In other words, all of the application routes are defined via [`RoutePlugin`](https://github.com/webiny/webiny-js/blob/v5.18.0/packages/app/src/plugins/RoutePlugin.tsx#L8) plugins, which is why we're exporting that instead of the actual [`Home`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/04-homepage/pinterest-clone/app/code/src/plugins/routes/home.tsx#L25) React component:

```tsx pinterest-clone/app/code/src/plugins/routes/home.tsx
// We register routes via the `RoutePlugin` plugin.
export default new RoutePlugin({
  route: <Route path="/" exact component={Home} />
});
```

All of these plugins are then registered in the [`pinterest-clone/app/code/src/plugins/index.ts`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/04-homepage/pinterest-clone/app/code/src/plugins/index.ts) plugins entrypoint file. For example, in case of the **Homepage**, we have the following:

```diff-js pinterest-clone/app/code/src/plugins/index.ts
import { plugins } from "@webiny/plugins";
import apolloLinkPlugins from "./apollo";
+ import home from "./routes/home";
import notFound from "./routes/notFound";

// Imports and registers all defined plugins.
plugins.register([
    // Various Apollo client plugins.
    apolloLinkPlugins,

    // Application routes.
+   home,
    notFound
]);
```

<Alert type="info">

At the moment, this file already includes all of the necessary imports. No further changes need to be made.

</Alert>

<Alert type="info">

The [`@webiny/react-router`](https://www.npmjs.com/package/@webiny/react-router) library is a thin wrapper around the popular [React Router](https://www.npmjs.com/package/react-router) and its [`react-router-dom`](https://www.npmjs.com/package/react-router-dom) library, which just adds a couple of minor features to it. For example, via the [`ReactRouterOnLinkPlugin`](https://github.com/webiny/webiny-js/blob/next/packages/react-router/src/types.ts#L4) plugin, it gives developers the ability to execute a piece of code for every link that's rendered on the page.

</Alert>

## Additional Page Styles

One last thing before we move on. Let's replace the code in the existing [`home.scss`](https://github.com/webiny/webiny-examples/blob/master/create-custom-application/04-react-application/04-homepage/pinterest-clone/app/code/src/styles/home.scss) file with the following:

```scss
// Home page styles.
.home {
  img {
    vertical-align: middle;
    border-style: none;
    width: 100%;
    border-radius: 20px;
    padding: 5px;
  }
}
```

Not super important, but making this change this will make the pins listed on our homepage look a little bit nicer.

## Final Result

With this component in place, our homepage should look like the following:

![Homepage - List of Pins](./assets/homepage/homepage.png)

At the moment, we could probably say that our homepage looks a bit bland and maybe boring. But we don't need to worry about that for now. The whole page will start looking much more lively as soon as we bring the missing cover image field and file upload functionality into the mix.

For now, our goal was to have our homepage show a list of pins created by different users, and we've certainly achieved that. This means we're ready for the final step, and that's creating the **Pin Details** page.
