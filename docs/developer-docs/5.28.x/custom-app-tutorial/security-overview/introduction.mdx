---
title: Introduction
description: Get familiar to the fundamental security-related terms like authentication and authorization and how we'll implement these in our custom application.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU’LL LEARN">

- why, at the moment, our application can be considered as not secure
- get introduced to the fundamental security-related terms: authentication and authorization
- the plan we intend to follow in the next couple of sections

</Alert>

<Alert type="info" title="Can I use this?">

In order to follow this tutorial, you must use Webiny version **5.18.0** or greater.

</Alert>

<Alert type="info">

The code that we cover in this section can also be found in our GitHub [examples repository](https://github.com/webiny/webiny-examples/tree/master/create-custom-application/05-security/01-introduction). Also, if you'd like to see the complete and final code of the application we're building, check out the [`full-example`](https://github.com/webiny/webiny-examples/tree/master/create-custom-application/full-example) folder.

</Alert>

## Overview

At the moment, we could certainly say our application is not secure:

- everybody can create pins, even anonymous (not signed in) users
- when a pin is created, we don't know who did it
- by directly accessing the GraphQL API via a simple GraphQL client, we can even issue mutations and change the information on pins created by other users (and even delete them)

This is certainly not what we'd expect of a modern application nor from any kind of application at all.

So, in the next couple of sections, we'll continue working on our **Pinterest Clone** application by creating a layer of security around it. The layer will introduce the following two fundamental security concepts into our application.

The first one is **authentication**, which, in short, is a process in which we confirm users are who they say they are. We determine their **identity**. Think of sign up and sign in **authentication flows**, these are both ways of **authenticating** users within our application.

On the other hand, we have **authorization**, which, in secure environments, always follows authentication. Essentially, this is a process in which we determine whether a successfully authenticated user has the permission to access a particular resource or not. For example, the created pin can be updated or deleted only by the user who initially created it. Furthermore, we definitely do not want to allow pin creation by **anonymous (not signed in)** users.

<Alert type="info">

Note that, when talking about **anonymous** users, we're referring to users whose identity is unknown. In other words, users who simply did not sign in to our application.

</Alert>

In order to implement all of these concepts, we'll need to make changes to our application and cloud infrastructure code - both on the GraphQL API and React application side.

Before we start with any kind of coding, let's quickly outline the plan.

## The Plan

We're going to divide this section of the tutorial into four parts. Note that while going through these, we'll be shifting back and forth between our GraphQL API and React application, and also between our application and cloud infrastructure code. Basically, this is because of the natural order in which all of the different pieces are connected together.

### [Getting Started](/docs/{version}/custom-app-tutorial/security-overview/get-started)

To get started, we'll quickly outline a couple of base concepts we'll be using in the sections that follow:

- give an overview of the authentication flow we'll be integrating into our application - the Amazon Cognito's Hosted UI
- because we'll not only be working with our application, but also with our cloud infrastructure code, we'll dedicate a couple of sentences to explain how it's organized, explain what cloud infrastructure stack output is, and more

### [Cloud Infrastructure](/docs/{version}/custom-app-tutorial/adding-user-pools/adding-user-pool-and-user-pool-domain)

This is where we'll start working with our cloud infrastructure code, which will enable us to deploy additional Amazon Cognito-related cloud infrastructure resources. We'll be doing this partly on the GraphQL API and partly on the React application side.

### [React Application](/docs/{version}/custom-app-tutorial/securing-react-application/initial-setup)

Once all of the needed cloud infrastructure resources have been deployed, we can start integrating the mentioned Amazon Cognito's Hosted UI into our React application.

### [GraphQL API](/docs/{version}/custom-app-tutorial/securing-graphql-api/initial-setup)

Finally, once our users are able to sign up and sign in to our application, we'll complete our application's security layer by implementing authentication and authorization checks on the GraphQL API side, within our resolver functions.
