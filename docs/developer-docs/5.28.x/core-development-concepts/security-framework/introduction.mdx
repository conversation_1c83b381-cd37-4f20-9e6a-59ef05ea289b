---
id: aafeab71
title: Introduction
description: Learn how security works in Webiny.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- the key terms used in the context of security
- what is Webiny Security Framework

</Alert>

<Alert type="info">

If you just want to jump straight into the code, take a look at the [Securing Your Application](/docs/{version}/admin-area/new-app-tutorial/security) tutorials section.

</Alert>

Before we can talk about Webiny Security Framework, we need to explain a few key terms that are constantly used when talking about security in general. You're most likely already familiar with some of them, but let's quickly go over these terms, in case you're new to the world of security.

## Authentication

Authentication is a process in which the application attempts to verify the credentials provided by the client (e.g., a browser, a mobile app, or another application), and retrieves identity information using those verified credentials.

Credentials can be a combination of a username and a password, a [JWT token](https://en.wikipedia.org/wiki/JSON_Web_Token), etc. Every time a request is made to your API by a client, that request will most likely contain a set of credentials (usually using the [Authorization HTTP request header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Authorization)) that will allow you to identify the client.

Once the identity is established, your application will usually keep that information available for the rest of the system to use, for the duration of that particular request.

## Identity

An identity is a _verified_ piece of information that tells us _who_ is accessing your application. As we already established in the previous section, identity is determined in the authentication process. However, not every interaction with your application requires identity information. Thus, it's also acceptable for a request to be anonymous.

As an example of anonymous requests, let's pretend your API is exposing a publicly accessible endpoint to fetch published pages, or weather information. That public endpoint doesn't need to know anything about the client in order to return the data. Requests to these public endpoints won't need to go through authentication process and, thus, don't need to have any credentials.

## Authorization

Authorization is a process of determining if the current identity is allowed to perform the requested action. During request processing, your application can execute authorization many times. Different parts of the application will use different rules to determine if the requested action is allowed. These rules are called _permissions_.

## Permission

A permission is a set of rules which describe what the identity can or cannot do. In case of Webiny, it's a simple object that contains a _name_ and additional optional properties that are relevant to the business logic of your application.

For example, let's pretend Joe wants to update a recipe for muffins, originally created by Jane. Since we already know his name, it means he has valid credentials. Now we need to see if he's actually allowed to update recipes, especially the ones that were not originally written by him.

The application will first load all permissions that are assigned to Joe and then check if he can update recipes. In the context of Webiny, such a permission could look like this:

```json Recipe permission
{
  "name": "recipe", // unique name for a permission
  "rwd": "rw", // read/write/delete actions
  "own": true // can only perform actions on own recipes
}
```

This means that Joe can perform _read_ and _write_ operations, but only on recipes he has created. Which, in our scenario, means that the application will reject his attempt to update a recipe that belongs to Jane.

---

Armed with all this information, we can now describe the Webiny Security Framework.

## Webiny Security Framework

Webiny Security Framework is a thin application layer that allows you to handle _authentication_ and _authorization_ using plugins. It can be used in APIs (be it GraphQL API, REST API, or a custom Lambda handler) as well as React applications. Its main purpose is to provide a simple and flexible system to work with permissions.

Webiny applications, as well as your custom ones, request permissions from the security framework when they need to check if the current _identity_ is _authorized_ (in other words, _has permission_) to execute certain operations.

---

In the following articles we dive deeper into how the framework works in the context of Webiny APIs and React applications.
