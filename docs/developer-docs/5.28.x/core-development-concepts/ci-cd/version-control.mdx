---
id: aafeab6f
title: Version Control
description: Learn about the most commonly used branching strategy that you can adopt for your project.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- how to organize your Git repository

</Alert>

<Alert type="info">

Get your CI/CD set up in no time with the built-in [CI/CD scaffold](/docs/{version}/core-development-concepts/ci-cd/setup).

</Alert>

## Overview

In today's world, project development is always backed by a [version control system (VCS)](https://en.wikipedia.org/wiki/Version_control).

In this guide, we cover the approaches we recommend to be used with [GitHub](https://en.wikipedia.org/wiki/GitHub) as the VCS of choice, as it is, from our experience, the most used system out there.

<Alert type="info">

[GitHub](https://en.wikipedia.org/wiki/GitHub) is a provider of Internet hosting for software development and version control using [Git](https://www.git-scm.com/book/en/v2/Getting-Started-What-is-Git%3F). It offers the distributed version control and source code management functionality of Git, plus its own features.

</Alert>

<Alert type="success">

Want us to cover an integration with a different of VCS? [Let us know](https://www.webiny.com/slack)!

</Alert>

## GitHub Flow

When working with GitHub, there are a couple of different workflows that you can adopt within your organisation, but the one that's used the most is the [GitHub flow](https://guides.github.com/introduction/flow/):

> GitHub flow is a lightweight, branch-based workflow that supports teams and projects where deployments are made regularly.

Essentially, developers first create a branch from one of the long-lived development base branches, in which they perform necessary code changes. Once ready, a [pull request (PR)](https://docs.github.com/en/github/collaborating-with-issues-and-pull-requests/about-pull-requests) is created, which initiates the review process made by other team members and in almost all cases triggers different application tests. If all code changes are in order, the pull request is accepted, or in other words, the changes are merged back into the base branch.

![Environments.](./assets/version-control/github_flow.png)

## Organizing Branches

Besides adopting the GitHub flow explained above, another element to consider is the organization of your project's Git branches. Similarly to how we've defined [shared and isolated environments](/docs/{version}/core-development-concepts/ci-cd/environments) we can deploy into, your repository should also consist of shared and isolated branches that, in a way, mirror these environments.

### Shared branches

For your project, you might want to have a couple of **shared** and long-lived branches that represent your shared environments, like `dev`, `staging`, and `prod` (`production`). As with the environments, no strict naming rules exist, but these names are definitely something most developers will recognize. We recommend having these three as the minimum and, of course, if more are needed, you're free to do so.

These branches should be made [protected](https://docs.github.com/en/github/administering-a-repository/about-protected-branches) and direct code pushes into these should be disabled. Not only direct pushes break the GitHub flow and merging strategies we're about to explain in the next section, in almost all cases, this triggers different deployment processes defined in your CI/CD workflows.

Finally, one of these branches will always be a starting point for every fix or a new feature that needs to be implemented. Usually, this is the `dev` branch which should be marked as the [default branch](https://docs.github.com/en/github/administering-a-repository/changing-the-default-branch) in your repository.

<Alert type="info">

Visit the [Workflows](/docs/{version}/core-development-concepts/ci-cd/workflows) key topic to learn how code changes are propagated from one shared branch to another, and how they are ultimately deployed to real users.

</Alert>

### Isolated (development) branches

With the shared and long-lived branches, depending on the project size, you always have a number of **isolated** or **development**, short-lived branches, that are created by developers and that, eventually, contain new fixes and features.

These branches are considered as short-lived because they exist for a shorter amount of time. As soon as the performed changes are merged into one of the long-lived branches, these branches can usually be immediately deleted.

As mentioned, the `dev` branch is usually the default branch, from which developers branch from, and to which the changes are ultimately merged back.

The following diagram shows both types of branches:

![Environments.](./assets/version-control/branches.png)

## FAQ

### Can we adopt a different branching strategy?

Yes, you can. The branching strategy presented in this guide is something that works in most cases. But, you're free to define your own, if need be.

### Can I use a different VCS?

Yes, you can. If you manage to define some best practices around it, we'd be glad to [hear about it](https://www.webiny.com/slack/) and add it to our documentation.
