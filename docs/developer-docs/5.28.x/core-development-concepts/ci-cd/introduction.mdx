---
id: aafeab6d
title: Introduction
description: Introduction to CI/CD.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- what is CI/CD
- get an overview of different CI/CD phases

</Alert>

<Alert type="info">

Get your CI/CD set up in no time with the built-in [CI/CD scaffold](/docs/{version}/core-development-concepts/ci-cd/setup).

</Alert>

## Overview

The process of application development is much more than just writing application code. It entails several different subprocesses, which are equally, if not more, important. For example:

- testing
- deployment into multiple environments
- organizing code in multiple Version Control System (VCS) branches
- choosing the right cloud infrastructure resources for the job
- storing and managing cloud infrastructure state files
- security
- monitoring and observability <!-- (https://thenewstack.io/monitoring-vs-observability-whats-the-difference/) -->
- team organization

In the following sections, we cover some of these subprocesses and provide best practices around how to structure and implement them, within your CI/CD workflow.

For starters, we give a quick introduction on what CI/CD is and what are its different phases.

## What is CI/CD?

**Continuous Integration (CI)** is a software application development practice where developers integrate code into a shared repository frequently, preferably several times a day. Each integration can then be verified by an automated build and automated tests. While automated testing is not strictly part of CI, it is typically implied.

**Continuous deployment (CD)** is a software application development practice where code changes are automatically prepared for a release to production.

Note that the **CD** abbreviation can also stand for **continuous delivery**, which also entails continuous deployment of your project, only in a [more manual fashion](https://en.wikipedia.org/wiki/Continuous_delivery):

> Continuous delivery is the ability to deliver software that can be deployed at any time through manual releases; this is in contrast to continuous deployment which uses automated deployments.

<Alert type="info">

In the rest of this key topics section, when mentioning the **CD** abbreviation, we're explicitly referring to the **continuous deployment**.

</Alert>

## Different Phases

In general, the CI/CD workflow, sometimes also referred to as a release cycle, consists of a couple of phases:

![CI/CD Phases](./assets/introduction/cicd_infinity.png)

It all starts with the **planing** phase, in which we define the work that needs to be done, and delegate it to different members of the team. Teams use different approaches in order to organize the team and track its progress, for example [Scrum](https://en.wikipedia.org/wiki/Scrum_%28software_development%29) or [Kanban](https://en.wikipedia.org/wiki/Kanban).

Once the work has been delegated, every developer goes through the **code**, **build**, and **test** phases. Different tools, processes and services can be used here, starting from services for code storage and [version control](https://en.wikipedia.org/wiki/Version_control) like [Git](https://www.git-scm.com/book/en/v2/Getting-Started-What-is-Git%3F) and [GitHub](https://en.wikipedia.org/wiki/GitHub), different testing frameworks like [Jest](https://jestjs.io/), [Mocha](https://mochajs.org/) or [Cypress](https://www.cypress.io/), build tools like [Babel](https://babeljs.io/), [Webpack](https://webpack.js.org/), and so on.

<Alert type="info">

Most of the mentioned tools are specific to the JavaScript / Node.js ecosystem, and come set up by default in every Webiny project. A full list of used technologies can be found in the [Tools and Libraries](/docs/{version}/core-development-concepts/basics/tools-and-libraries) topic.

</Alert>

Once the tests are passing, we reach the **release** and **deploy** phases, in which new changes are deployed to actual users. Again, a plethora of different deployment tools can be used in this space, like [Pulumi](https://www.pulumi.com/), [AWS CloudFormation](https://aws.amazon.com/cloudformation/), [Serverless Framework](https://www.serverless.com/), and so on. With the tools, there are also different deployment strategies that can be utilized, like blue/green and canary deployments, A/B testing, and so on.

<Alert type="info">

By default, Webiny projects are deployed with Pulumi - a modern infrastructure as code (IaC) solution. Check out the [Iac with Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi) topic to learn more.

</Alert>

Last but certainly not the least, we reach the **operate** and **monitor** phases in which we are, essentially, ensuring that the system that was deployed is operating as expected. Different speed and resource metrics, error reports and alerts are what provide the information on how stable our system is.

In general, monitoring serverless applications has shown to be a bit harder than monitoring "traditional" applications, mainly because of their distributed nature. They are almost always constructed out of several services that talk to each other and trying to find the service that is the root cause of a new issue can be hard. Fortunately, there are tools and services that assist us in this regard as well, such as [AWS CloudWatch](https://aws.amazon.com/cloudwatch/) and [AWS X-ray](https://aws.amazon.com/xray/), [Lumigo](https://lumigo.io/), [Epsagon](https://epsagon.com/product/), and others.

And this completes the CI/CD workflow. Once we've passed through all of the steps, the whole workflow starts from the beginning, and it is repeating infinitely as part of our project development process. Note that the explained phases shouldn't be considered as a hard rule - certain phases can be implemented in different ways and adjusted to your needs. <!-- Be sure to check the [CI/CD How-To Guide](/docs/{version}/core-development-concepts/ci-cd/introduction) for more practical information on how to set up a CI/CD workflow for your Webiny project. -->

## FAQ

### Can I bring additional tools and services?

Yes, you can. All of the tools that come with every Webiny project are the tools we've determined to work good, but some can still be replaced, if need be.
