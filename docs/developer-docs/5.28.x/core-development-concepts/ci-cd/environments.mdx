---
id: aafeab6e
title: Managing Environments
description: Learn how to organize different deployment environments for your project
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- how to organize different environments for your project

</Alert>

<Alert type="info">

Get your CI/CD set up in no time with the built-in [CI/CD scaffold](/docs/{version}/core-development-concepts/ci-cd/setup).

</Alert>

## Introduction

Deploying your project into multiple environments enables us to preview and test new features and bug fixes, before actually rolling them out to real users. This helps us in making our project as stable as possible, and, ultimately, providing the best possible user experience.

<Alert type="info">

To learn more about environments in general, check out the [Environments](/docs/{version}/infrastructure/basics/environments) key topic.

</Alert>

<Alert type="success">

Check out the [Deploy Your Project](/docs/{version}/core-development-concepts/basics/project-deployment) guide to learn how to deploy your project into multiple environments.

</Alert>

## Multiple Environments and Extra Costs

When we talk about multiple environment deployments, it's important to note that we're talking about deploying multiple copies of our project, both in terms of the application code and, more importantly, needed cloud infrastructure resources.

Serverless technologies and services have many advantages, one of them being the **pay-per-use** pricing model. This means that, if we, for example, had five environments deployed into our AWS account, and none of them were used (or very rarely), no additional cost would be incurred.

This **pay-per-use** pricing model, paired with Webiny's built-in deployment workflows and tools, makes it much more affordable and easier to introduce multiple environments for your project.

<Alert type="warning" title="Non-Serverless Resources">

Take into consideration that sometimes, projects do require cloud infrastructure resources that aren't serverless or do not follow the pay-per-use pricing model. In that case, having multiple environments could in fact incur additional cost.

In Webiny's case and its default setup, one of the resources of that kind is the [Amazon Elastic Search](https://aws.amazon.com/elasticsearch-service/pricing/).

</Alert>

<Alert type="info">

Read more about the cloud infrastructure resources that get deployed into your AWS account in our [Cloud Infrastructure](/docs/{version}/architecture/introduction) key topics section.

</Alert>

## Shared and Isolated Environments

There are two types of environments into which we usually deploy to: **shared** and **isolated**.

**Shared environments** are environments that are shared among:

- the team - for development, integration, and testing needs
- real users - a unique environment, most commonly known as the **production** environment

In terms of naming these environments, a strict rule does not exist, but some of the typical environment names for these environments are usually `dev`, `staging`, and `prod` (`production`). These are always deployed from a CI/CD environment.

On the other hand, **isolated environments** are environments that are deployed by developers, for their own development and testing needs. These are usually deployed and destroyed from a developer's machine.

<Alert type="info">

Within an organization, it's common practice for developers to have their own cloud (AWS) account, which they use to deploy their isolated environments. This way we ensure that they don't impact one of the (sensitive) shared environments and enable them to perform experiments more freely.

Read more about how to organize cloud accounts in the [Cloud (AWS) Accounts](/docs/{version}/core-development-concepts/ci-cd/environments#cloud-aws-accounts) section.

</Alert>

### Long-lived and Short-lived Environments

Shared environments are long-lived environments, meaning, once they're deployed, they do not get destroyed that often, if ever. In contrast, isolated environments are short-lived - they are frequently deployed and destroyed.

<Alert type="warning" title="Caution">

Be sure to check for unused deployed cloud infrastructure resources from time to time. We want to avoid unnecessary cost from mentioned non-serverless resources, that do not follow the pay-per-use pricing model.

</Alert>

## Organization

The following diagram shows the most common organization of shared and isolated environments in a single project:

![Webiny Cloud Infrastructure - Environments](./assets/environments/environments.png)

<Alert type="info">

This structure of environments is also mirrored by branches in your project's Git repository. More on this in the [Version Control](/docs/{version}/core-development-concepts/ci-cd/version-control) topic that follows.

</Alert>

### Shared Environments

Although there can be more shared environments, most commonly, we end up with the following:

#### `dev`

New fixes and features are first deployed into this environment, might be unstable from time to time.

#### `staging`

For testing new release candidates in a production-like environment. Meaning, here we deploy the same application code and cloud infrastructure resources that we will ultimately deploy into the production environment, used by real users.

#### `prod` (`production`)

A system used by real users. Must be in a state that is considered as stable, as much of the time as possible.

### Isolated Environments

With shared environments, developers deploy their own environments, where, as mentioned, they develop and test bug fixes and new features. Usually, one environment per developer is enough. If the environment is not needed anymore, it's recommended it's destroyed.

## Cloud (AWS) Accounts

In terms of cloud (AWS) accounts, it has been shown that a good practice to adopt is having a separate account for every shared environment and developer in the organization. For example, if our organization consisted of five developers, with the three `dev`, `staging`, and `prod` shared environments, that would be eight cloud accounts in total.

<Alert type="info">

The shared environments AWS accounts will mostly be used from your CI/CD pipeline, which we cover in one of the following sections.

</Alert>

<Alert type="success">

For easier management of AWS accounts, we recommend checking out [AWS Organizations](https://aws.amazon.com/organizations/), which is a feature that helps you centrally manage and govern your environment as you grow and scale your AWS resources.

Furthermore, the [Organizing Your AWS Environment Using Multiple Accounts](https://docs.aws.amazon.com/whitepapers/latest/organizing-your-aws-environment/organizing-your-aws-environment.html) article is also something that provides useful information on the multiple AWS accounts for different environments approach.

</Alert>

## FAQ

### Can we have more shared environments?

Yes. The `dev`, `staging`, and `prod` is the minimum we recommend, but you can have more, if need be.

### Are the names of environments fixed in any way?

Environment names are not fixed or hard-coded in any way. We use `dev`, `staging`, and `prod` as these names are often familiar to developers. Feel free to name these differently if your organization follows different conventions.

### Except my application code, should the `staging` and `prod` cloud infrastructure also be the same?

With serverless application development, the deployed cloud infrastructure can play a vital role in our application. It's no longer just about the application code. So, with that in mind, in order to reduce the chance of introducing regressions that our users could experience, we recommend that the `prod` and `staging` environments are same on both application code and cloud infrastructure level.

To enable this, please open your [`packages/cwp-template-aws/template/api/pulumi/index.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/api/pulumi/index.ts#L7) file, and uncomment the `staging` string in the `PROD_STACK_ENVIRONMENTS` array.

<Alert type="info">

Learn more about differences between the development and production cloud infrastructure differences in the [Cloud Infrastructure](/docs/{version}/architecture/introduction#different-cloud-infrastructure-setups) key topic.

</Alert>
