---
id: aafeab69
title: Introduction to Scaffolding
description: Learn how to set up a CI/CD pipeline for your Webiny project.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- what is scaffolding
- what are the scaffolds you can use today

</Alert>

## Scaffolding

In general, scaffolding is a process which, based on the passed arguments, performs one (or more) of the following actions:

- generates repetitive (also known as "boilerplate") code, which we don't want to keep writing every time from scratch
- generates more complete setups, like a new [project application](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages), a standalone GraphQL API, and so on
- performs operations with different services, e.g. sets up a CI/CD process with a CI/CD provider of your choice
- and more

By enabling developers to skip all of the manual and often tedious work, the goal of scaffolding is to speed up the development process and, ultimately, make developers' lives easier.

## Available Scaffolds

Out of the box, Webiny offers a couple of scaffolding tools, or in short, scaffolds, which you can utilize via the [Webiny CLI's](/docs/{version}/core-development-concepts/basics/webiny-cli) built-in `webiny scaffold` command.

The following is a list of currently available scaffolds:

- [Extend Admin Area](/docs/{version}/core-development-concepts/scaffolding/extend-admin-area)
- [Extend GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api)
- [Set up CI/CD](/docs/{version}/core-development-concepts/ci-cd/setup)
