---
id: aafea966
title: Full Stack Application
description: Learn how to use the Full Stack Application scaffold.
---

import { Al<PERSON> } from "@/components/Alert";

<Alert type="info" title="Can I use this?">

This feature is available since **v5.13.0**.

</Alert>

<Alert type="success" title="What you'll learn">

- main features of the **Full Stack Application** scaffold
- how to continue developing on top of the generated application code

</Alert>

## Overview

The **Full Stack Application** scaffold creates a new full stack application which consists of a simple React application on the frontend, and the supporting GraphQL HTTP API on the backend, powered by AWS Lambda and Amazon DynamoDB.

<Alert type="info">

In order to create a new full stack application, behind the scenes, the scaffold is relying on the existing [GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api) and [React Application](/docs/{version}/core-development-concepts/scaffolding/full-stack-application) scaffolds.

</Alert>

## Features

### Frontend and Backend Applications

The Full Stack Application scaffold creates everything that you might need in order to start creating a new full stack application. It creates two [project applications](/docs/{version}/core-development-concepts/project-organization/project-applications), one that holds your frontend, and the other one that holds your backend application.

The frontend application is powered by React and is created with the [React Application](/docs/{version}/core-development-concepts/scaffolding/full-stack-application) scaffold. On the other hand, the backend application is powered by a GraphQL API and is created with the [GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api) scaffold.

<Alert type="info">

For more information on specific features that these two scaffolds include, we recommend you check out the dedicated [React Application](/docs/{version}/core-development-concepts/scaffolding/full-stack-application) and [GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api) guides.

</Alert>

### Clear Organization

The full stack application is created within a single folder, which contains both frontend and backend project applications. More on the organization of files and folders can be found in the [Development](#development) section below.

### Linked Automatically

The scaffold automatically ensures that the React application pulls the URL over which the deployed GraphQL API is accessible. In other words, once you start the application locally, you can immediately start using the included [Apollo GraphQL (v2)](https://www.apollographql.com/docs/react/v2) client to issue GraphQL queries and mutations.

### GraphQL API Interaction Example

During the scaffold's setup wizard, users get to choose whether they want to also include a simple GraphQL API interaction example code. In other words, the scaffold will create an additional page in your React application and extend the GraphQL API with a basic set of CRUD queries and mutations (using the [Extend GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api) scaffold).

To check out the example, start your React application locally and open it in your browser. Then, in the top left corner of the screen, click on the **A Simple GraphQL API Example** link:

![GraphQL API Interaction Example](./assets/full-stack-application/gql-api-example.png)

<Alert type="info">

Check out the React Application scaffold's [Development](/docs/{version}/core-development-concepts/scaffolding/full-stack-application#development-using-the-watch-command) section to learn how to start the React application locally.

</Alert>

## Development

### Usage

In order to use this scaffold, from your project root, simply run the `webiny scaffold` command:

```bash
yarn webiny scaffold
```

Then, from the list of all available scaffolds, select **Full Stack Application** and follow the on-screen instructions.

### Essential Files and Folders

The following is brief overview of the essential files and folders that are created during the scaffolding process. For more information, we encourage you to check out the dedicated [React Application](/docs/{version}/core-development-concepts/scaffolding/full-stack-application#essential-files-and-folders) and [GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api#essential-files-and-folders) guides.

```bash New Full Stack Application Folder (Some Files Removed for Brevity)
├── api                                 # GraphQL API's project application folder.
│   ├── code
│   │   └── graphql                     # GraphQL API application code folder.
│   │       └── src
│   │           └── plugins
│   │               └── scaffolds
│   │                   └── todos       #  GraphQL API interaction example code.
│   └── pulumi
└── app                                 # React application's project application folder.
    ├── code
    │   ├── public
    │   ├── src                         # React application code folder.
    │   │   ├── components
    │   │   ├── images
    │   │   ├── plugins
    │   │   │   └── routes
    │   │   │       ├── graphqlApiExample       # GraphQL API interaction example code.
    │   │   │       └── graphqlApiExample.tsx   # GraphQL API interaction example code.
    │   │   └── styles
    └── pulumi
```

#### GraphQL API

##### [`api`](https://github.com/webiny/webiny-js/tree/v5.13.0/packages/cli-plugin-scaffold-graphql-api/template)

The project application folder that holds the newly created React application.

##### [`api/code/graphql/src/`](https://github.com/webiny/webiny-js/tree/v5.13.0/packages/cli-plugin-scaffold-graphql-api/template/code/graphql/src)

Contains the GraphQL API application code. This is where you'll be defining the GraphQL schema, resolver functions, business logic, authentication / authorization logic, writing tests, and potentially more.

##### [`api/code/graphql/src/plugins`](https://github.com/webiny/webiny-js/tree/v5.13.0/packages/cli-plugin-scaffold-graphql-api/template/code/graphql/src/plugins)

This is where you'll be creating plugins, either manually or via other scaffolding utilities, for example via the [Extend GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api) scaffold.

<Alert type="success">

Do have in mind that every plugin (or a collection of plugins) you create also needs be imported and registered in the [`api/code/graphql/src/index.ts`](https://github.com/webiny/webiny-js/blob/v5.13.0/packages/cli-plugin-scaffold-graphql-api/template/code/graphql/src/index.ts#L11) entrypoint file.

</Alert>

##### [`api/pulumi`](https://github.com/webiny/webiny-js/tree/v5.13.0/packages/cli-plugin-scaffold-graphql-api/template/pulumi)

This is the folder that contains all of the GraphQL API's cloud infrastructure code. In it, we have the [`dev`](https://github.com/webiny/webiny-js/tree/v5.13.0/packages/cli-plugin-scaffold-graphql-api/template/pulumi/dev) and [`prod`](https://github.com/webiny/webiny-js/tree/v5.13.0/packages/cli-plugin-scaffold-graphql-api/template/pulumi/prod) folders, which represent the cloud infrastructure resources that will be deployed into development and production environments, respectively.

#### React Application

#### [`app`](https://github.com/webiny/webiny-js/tree/v5.13.0/packages/cli-plugin-scaffold-react-app/template)

The project application folder that holds the newly created React application.

##### [`app/code/src/`](https://github.com/webiny/webiny-js/blob/v5.13.0/packages/cli-plugin-scaffold-react-app/template/code/src)

Contains the React application code. This is where you'll be creating your application's pages, shared React components, defining routes, styles, and more.

##### [`app/code/src/plugins`](https://github.com/webiny/webiny-js/blob/v5.13.0/packages/cli-plugin-scaffold-react-app/template/code/src/plugins)

Contains plugins, like [`RoutePlugin`](https://github.com/webiny/webiny-js/blob/v5.13.0/packages/app/src/plugins/RoutePlugin.tsx#L8) plugins that define application routes or [`ApolloLinkPlugin`](https://github.com/webiny/webiny-js/blob/v5.13.0/packages/app/src/plugins/ApolloLinkPlugin.ts#L8) plugins that define Apollo links.

##### [`app/pulumi/`](https://github.com/webiny/webiny-js/tree/v5.13.0/packages/cli-plugin-scaffold-react-app/template/pulumi)

This is the folder that contains all of the React application's cloud infrastructure code.

### Deployment

Once you've completed the scaffold's wizard and the files have been created, in order to actually access the GraphQL API, you need to deploy it. This can be done as usual via the [`webiny deploy`](/docs/{version}/core-development-concepts/basics/project-deployment) command, or, even easier, if you're about to jump straight into coding, by running the [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) command. This command will not only deploy the changes, but also start a new watch session, which will automatically redeploy further application code changes, as you perform them (more on this below).

In order to fully deploy your full stack application, you need to run the following two commands:

```bash Deploying GraphQL API
yarn webiny deploy {full-stack-app-path/api} --env {env}
```

```bash Deploying React Application
yarn webiny deploy {full-stack-app-path/app} --env {env}
```

Note that, for the first deploy, the order matters. Because of the fact that the React application is relying on the already deploy GraphQL API, the GraphQL API should always be deployed first.

<Alert type="info">

During the scaffold's setup wizard, you will be asked if you want to immediately deploy the created GraphQL API. If you decide not to do it, you can deploy it later, as usual, with the [`webiny deploy`](/docs/{version}/core-development-concepts/basics/project-deployment) command.

</Alert>

### Development Using the Watch Command

The most straightforward way to continue developing on top of the created code would be via the [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) command.

In order to get started, from your project root, via two separate terminal sessions, simply run the following commands:

```bash Watching GraphQL API Code
yarn webiny watch {full-stack-app-path/api} --env {env}
```

```bash Watching React Application Code
yarn webiny watch {full-stack-app-path/app} --env {env}
```

<Alert type="info">

If you're just working on one side of your application, for example on your GraphQL API, you don't necessarily need to run both [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) commands.

</Alert>

### Extending the GraphQL API

The new GraphQL API can be extended in two ways. Be sure to check the [Extending GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api#extending-the-graphql-api) section in the [GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api) scaffold's guide.

### Extending the React application

On the React application side, you can start by creating new pages, in the similar fashion it's already done with the existing example pages. Be sure to check the [Creating First Pages](/docs/{version}/core-development-concepts/scaffolding/full-stack-application#creating-first-pages) section in the [GraphQL API](/docs/{version}/core-development-concepts/scaffolding/full-stack-application) scaffold's guide.

## FAQ

### How does security (authentication and authorization) work?

Please note that, by default, the authentication and authorization logic isn't included in the created code. In other words, all of the created GraphQL query and mutation operations can be performed by anonymous (not logged-in) users and all pages can be accessed by anybody. In most cases, this is not the desired behaviour.

Luckily, with a couple of built-in utilities, this can be relatively easily added. Please check out the [existing tutorials](/docs/{version}/admin-area/new-app-tutorial/security) to learn how to implement these on your own.

### Do I need to deploy the created GraphQL API in order develop my React application?

Technically, no, but you probably won't get too far. In almost all cases, your React application will need to interact with an already deployed GraphQL API, in order for it to work.
