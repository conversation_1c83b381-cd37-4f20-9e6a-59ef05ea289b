---
id: aafea969
title: How to Create a Package in a Webiny Project
description: Learn how to share code by creating a package in a Webiny project
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What You’ll Learn">

- how to create a new package in a Webiny project
- how to organize files in a Webiny project

</Alert>

## Overview

We often need to share code between multiple packages in our project.

<Alert type="info">

Every Webiny project consists of packages and project applications, you can learn more about them in our [key topics](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages) section.

</Alert>

Every Webiny project is organized as a [monorepo](/docs/{version}/core-development-concepts/project-organization/monorepo-organization).
In this tutorial, you will learn how to organize and share code between multiple packages in a Webiny project.
Let's get started.

## What We'll Build

In this tutorial, we'll be building a package that contains a simple React component and see how we can share it between multiple packages.
The same principles apply to Node.js packages you would use in your API.

Here is the file structure of the package we're about to build:

```
// Some files are omitted for the sake of brevity.

├── api
├── apps
│   ├── admin
│   ├── theme
│   └── website
├── package.json
├── packages
|   |   // This is our new package
│   └── gretting
│       ├── src
│       │   └── index.tsx
│       ├── .babelrc.js
│       ├── README.md
│       ├── package.json
│       ├── tsconfig.build.json
│       └── tsconfig.json
└── yarn.lock
```

## Prerequisites

### A Webiny Project

This tutorial assumes you have already created a new Webiny project to work on.
We recommend reading our [install Webiny](/docs/{version}/get-started/install-webiny) tutorial which shows you how to do it.

## Create a Package

In this step, we create a new React package.

<Alert type="info">

The Yarn workspaces aim to make working with monorepos easy.
Learn more about [workspaces](https://yarnpkg.com/features/workspaces) here.

</Alert>

### The Workspaces List

Before we continue, let's quickly cover the workspaces list, located in the `package.json` file in the project root.

The content of the package looks as shown below:

```json
(...)
"workspaces": {
    "packages": [
        "packages/*",
        "apps/admin/code",
        "apps/website/code",
        "apps/theme",
        "api/code/fileManager/*",
        "api/code/graphql",
        "api/code/getTime",
        "api/code/headlessCMS",
        "api/code/pageBuilder/*",
        "api/code/prerenderingService/render",
        "api/code/prerenderingService/flush",
        "api/code/prerenderingService/queue/*"
    ]
},
(...)
```

As you can see from the example above, you can define exact workspace paths, or provide a wildcard to mark each subfolder as a workspace.

In this tutorial, we use the latter.

### Initialize the Package

Enough with the theory, let’s dive in and initialize the package.

First, create a folder called `packages` inside the root of your project where we add our custom package.

<Alert type="info">

Packages are just regular NPM packages, or in other words, folders with their own package.json

</Alert>

Let's create a folder called `greeting` inside `packages`.
Now that we've created our new folder, let's initialize a new package in it.
For that, we need to create a `package.json` file inside that folder.

You can add it manually or use the following command inside the newly created folder:

```
yarn init
```

Once we execute the above command, we will be presented with a couple of questions as shown below:

![yarn init](./assets/create-a-package-in-webiny-project/yarn-init.png)

<Alert type="info">

You can also run `yarn init -y` to use sensible defaults.

</Alert>

Depending on your input, the generated `package.json` file's content may look similar to the following:

```json
{
  "name": "@examples/greeting",
  "version": "1.0.0",
  "description": "",
  "main": "index.js",
  "license": "MIT"
}
```

<Alert type="info">

The `name` property defined in the package's `package.json` will be used to later import it.

</Alert>

### Create the Package Content

Now that we've initialized a new package, let's start by adding a couple of files.

```json
├── packages
|   |   // This is our new package.
│   └── greeting
│       │ // All the source code for the React component will be in this folder.
│       ├── src
│       │   └── index.tsx
│       ├── // A configuration file for babel. More on that later.
│       ├── .babelrc.js
│       ├── // A text file about the package.
│       ├── README.md
│       ├── // This file holds various metadata relevant to the package.
│       ├── package.json
│       ├── // A configuration file of the TypeScript compiler (tsc) used when package is being built.
│       ├── tsconfig.build.json
│       └── // A configuration file for Typescript compiler used by your IDE.
│       └── tsconfig.json
└── yarn.lock

```

### Source Code

First, we write the source code for our example React component.
For that, create a `src` folder inside `packages/greeting` and then add the `index.tsx` file inside it with the following code:

```tsx packages/greeting/src/index.tsx
import React from "react";

const WelcomeMessage = () => {
  return <h1>Welcome to Webiny</h1>;
};

export default WelcomeMessage;
```

<Alert type="info">

Here we're creating a very simple React component. But, you can write whatever logic you need for your project.

</Alert>

Now that we have our desired code in place. We can move to the next step which is adding the required configuration files.

To build our package, we need to add the following configuration files:

- [`package.json`](#packagejson)
- [`.babelrc.js`](#babelrcjs)
- [`tsconfig.build.json`](#tsconfigbuildjson)
- [`tsconfig.json`](#tsconfigjson)

Let's create them one by one.

<Alert type="info">

You can check out the full list of [tools and libraries](/docs/{version}/core-development-concepts/basics/tools-and-libraries) included in every Webiny.

</Alert>

### `package.json`

Let's start with the `package.json` file.

First, we need to add the following `devDependencies` and `dependencies` as shown below:

```json packages/greeting/package.json
{
  (...)
  "dependencies": {
    "react": "^16.14.0",
    "react-dom": "^16.14.0"
  },
   "devDependencies": {
    "@babel/cli": "^7.5.5",
    "@babel/core": "^7.5.5",
    "@babel/preset-env": "^7.5.5",
    "@babel/preset-react": "^7.0.0",
    "@babel/preset-typescript": "^7.8.3",
    "@svgr/webpack": "^4.3.2",
    "babel-plugin-named-asset-import": "^1.0.0-next.3e165448",
    "rimraf": "^3.0.2",
    "typescript": "^4.1.3"
  },
  (...)
}
```

<Alert type="info">

You can find out the full example code used in this tutorial in our [repo](https://github.com/webiny/webiny-examples/tree/master/create-package-in-monorepo).

</Alert>

Let's quickly discuss all of them:

#### `dependencies`

This field defines other packages (dependencies) we will use in the code.

In our case we need the following:

- [`react`](https://www.npmjs.com/package/react): React is a JavaScript library for creating user interfaces.
- [`react-dom`](https://www.npmjs.com/package/react-dom): Serves as the entry point to the DOM and server renderers for React.

#### `devDependencies`

This value is used to specify the packages that are only needed for local development and testing.

In our case we need the following:

- [`@babel/cli`](https://www.npmjs.com/package/@babel/cli): Babel command line.
- [`@babel/core`](https://www.npmjs.com/package/@babel/core): Babel compiler core.
- [`@babel/preset-env`](https://www.npmjs.com/package/@babel/preset-env): Babel preset for each environment.
- [`@babel/preset-react`](https://www.npmjs.com/package/@babel/preset-react): Babel preset for all React plugins.
- [`@babel/preset-typescript`](https://www.npmjs.com/package/@babel/preset-typescript): Babel preset for TypeScript.
- [`@svgr/webpack`](https://www.npmjs.com/package/@svgr/webpack): [Webpack](https://webpack.js.org/) loader for SVGR.
- [`babel-plugin-named-asset-import`](https://www.npmjs.com/package/babel-plugin-named-asset-import): Babel plugin for import named exports from non JS/CSS assets.
- [`rimraf`](https://www.npmjs.com/package/rimraf): A deep deletion module for Node.js (like `rm -rf`).
- [`typescript`](https://www.npmjs.com/package/typescript): [TypeScript](https://www.typescriptlang.org/) is a language for application-scale JavaScript.

After that, we add the following scripts inside the `package.json` file:

#### `scripts`

The ["scripts"](https://docs.npmjs.com/cli/v7/using-npm/scripts) property of your package.json file supports a number of built-in scripts
and their preset life cycle events as well as arbitrary scripts.

In our case we need the following:

- [`build`](https://github.com/webiny/webiny-examples/blob/master/create-package-in-monorepo/packages/iFrameElement/package.json#L37):
  it removes the content of the `dist` folder and compiles the source code via `babel` and runs the `postbuild` command.
- [`watch`](https://github.com/webiny/webiny-examples/blob/master/create-package-in-monorepo/packages/iFrameElement/package.json#L38):
  it runs the `babel` compiler in `watch` mode, which means the latest changes will compile automatically as source file content changes.
- [`postbuild`](https://github.com/webiny/webiny-examples/blob/master/create-package-in-monorepo/packages/iFrameElement/package.json#L39):
  as the name suggests, it runs after the completion of the `build` command.
  We use it to copy the meta files like `package.json`, `README.md` into the `dist` folder and compile typescript code.

After adding `script` the `package.json` file look as shown below:

```json packages/greeting/package.json
{
  (...)
  "dependencies": {
    "react": "^16.14.0",
    "react-dom": "^16.14.0"
  },
   "devDependencies": {
    "@babel/cli": "^7.5.5",
    "@babel/core": "^7.5.5",
    "@babel/preset-env": "^7.5.5",
    "@babel/preset-react": "^7.0.0",
    "@babel/preset-typescript": "^7.8.3",
    "@svgr/webpack": "^4.3.2",
    "babel-plugin-named-asset-import": "^1.0.0-next.3e165448",
    "rimraf": "^3.0.2",
    "typescript": "^4.1.3"
  },
   "scripts": {
    "build": "rimraf ./dist '*.tsbuildinfo' && babel src -d dist --source-maps --copy-files --extensions \".ts,.tsx\" && yarn postbuild",
    "watch": "babel src -d dist --source-maps --copy-files --extensions \".ts,.tsx\" --watch",
    "postbuild": "cp package.json README.md dist/ && tsc -p tsconfig.build.json"
  }
  (...)
}
```

#### `publishConfig`

And finally, we add `publishConfig` which is a set of configuration values, usually used for package publishing purposes.
But, in our case, this is what enables us to import our newly created package from other packages in different project applications.
Webiny uses this to link your package in node_modules with the appropriate target folder, which will be `dist` once the package is built.

<Alert type="info">

The proper linking of packages is established via the built-in `link-workspaces` command, defined in your root `package.json` file.

<!-- Learn more about linking workspaces in [xyz](#)... -->

</Alert>

After adding `publishConfig` the package.json file look as shown below:

```json packages/greeting/package.json
{
 (...)
 "dependencies": {
   "react": "^16.14.0",
   "react-dom": "^16.14.0"
 },
  "devDependencies": {
   "@babel/cli": "^7.5.5",
   "@babel/core": "^7.5.5",
   "@babel/preset-env": "^7.5.5",
   "@babel/preset-react": "^7.0.0",
   "@babel/preset-typescript": "^7.8.3",
   "@svgr/webpack": "^4.3.2",
   "babel-plugin-named-asset-import": "^1.0.0-next.3e165448",
   "rimraf": "^3.0.2",
   "typescript": "^4.1.3"
 },
   "publishConfig": {
   "access": "public",
   "directory": "dist"
 },
  "scripts": {
   "build": "rimraf ./dist '*.tsbuildinfo' && babel src -d dist --source-maps --copy-files --extensions \".ts,.tsx\" && yarn postbuild",
   "watch": "babel src -d dist --source-maps --copy-files --extensions \".ts,.tsx\" --watch",
   "postbuild": "cp package.json LICENSE README.md dist/ && tsc -p tsconfig.build.json"
 }
}
```

<Alert type="info">

Learn more about [`publishConfig`](https://docs.npmjs.com/cli/v7/configuring-npm/package-json#publishconfig).

</Alert>

### `.babelrc.js`

Now let's take a look at the `.babelrc.js` file which is a configuration file for a tool called [babel](https://babeljs.io/).

<Alert type="info">

Babel is a JavaScript compiler. We need it because:

- we're writing the `React` code in `JSX` syntax which needs to be converted to `JS`
- we're also using the latest JavaScript features and syntax which are not supported in all browsers, and therefore, need to be converted

</Alert>

In the `.babelrc.js` we just export the `.babel.react` configuration file which is defined in the project root.

```js packages/greeting/.babelrc.js
module.exports = require("../../.babel.react")({ path: __dirname });
```

<Alert type="info">

Every Webiny project comes with a `.babel.react.js` and `.babel.node.js`.
You don't need to know all the configurations.
But, if you're interested feel free to check the full configuration [file](https://github.com/webiny/webiny-examples/blob/master/create-package-in-monorepo/.babel.react.js).

</Alert>

### `tsconfig.build.json`

Every Webiny project prioritizes [TypeScript](https://www.typescriptlang.org/).

And it needs to be compiled and the `tsconfig.build.json` file corresponds to the configuration file of the TypeScript compiler (tsc) used when package is being built.

<Alert type="info">

Webiny uses TypeScript (v4). Only in a few cases, like for example configuration files, you will encounter pure JavaScript.

</Alert>

Let's take a look at the content of this file:

```json packages/greeting/tsconfig.build.json
{
  "extends": "../../tsconfig.build.json",
  "include": ["./src"],
  "exclude": ["node_modules"],
  "compilerOptions": {
    "rootDir": "./src",
    "outDir": "./dist",
    "declarationDir": "./dist"
  }
}
```

<Alert type="info">

The `tsconfig.build.json` file specifies the root files and the compiler options required to compile the project.
Please check out the official docs to [learn more](https://www.typescriptlang.org/docs/handbook/tsconfig-json.html#:~:text=a%20tsconfig.json-,Overview,required%20to%20compile%20the%20project.) about it.

</Alert>

### `tsconfig.json`

This is a configuration file for Typescript compiler used by your IDE.

Let's take a look at the content of this file:

```json packages/greeting/tsconfig.json
{
  "extends": "../../tsconfig"
}
```

Like the previous file, we're just using the configuration defined in the
[project root](https://github.com/webiny/webiny-examples/blob/master/create-package-in-monorepo/tsconfig.json) here.

### `README.md` (optional)

A README is a text file that introduces and explains a project.
It contains information that is commonly required to understand what the project is about.

## Preparing the Package for Usage

Now that we've created our package and added the required configuration files, it is time to use it.
To do that we need to take the following steps:

- `install` the package
- `build` the package

### Install the Package

Now that we have all the files in place, it is time to install and link the package.
Run the following command from the root of your project:

```
yarn install
```

Running this command will do two things:

1. install the package dependencies.
2. link the package.

<Alert type="info">

The `link` step is performed by the [`link-workspaces`](https://github.com/webiny/webiny-examples/blob/master/create-package-in-monorepo/scripts/linkWorkspaces.js) script
which runs via the [`postinstall`](https://github.com/webiny/webiny-examples/blob/master/create-package-in-monorepo/package.json#L64) hook.

</Alert>

### Build the Package

To use the package we need to `build` it first.

**"And how do we do that?"** you may ask, remember we added the `build` command under the `scripts` key inside the `package.json` file of the package.
Now it's time we use it.

We can simply `cd` into the package folder which is `packages/greeting` and run:

```
yarn build
```

And it will work just fine. But, as your project grows and you add more packages, it becomes a chore to run the same command across multiple packages.

Webiny CLI provides the `workspaces run` (or `ws run` for short) command that enables you to run a single command across multiple workspaces at once.
The common use case where this might be needed is local development, where you want to watch for code changes on multiple packages, and rebuild them accordingly.

For example, to establish a watch session across multiple packages, located in a specific folder, you can run the following command:

```
yarn webiny ws run watch --folder packages
```

On the other hand, if you wanted to build all of the packages, again, located in a specific folder, you can run:

```
yarn webiny ws run build --folder packages
```

<Alert type="info">

The `ws run` command executes the command in question for every workspace present in the folder.
In our case, `packages/greeting`.

</Alert>

### Using the Package in Apps

After completing all these steps you can now simply import and use it as a regular npm package.
You can import and use this newly created package in any application or any other package inside the same Webiny project.

```ts
import WelcomeMessage from "@examples/greeting";
```

## Conclusion

Congratulations!

You've successfully created a new package in a Webiny project.
Monorepo organization makes it possible to structure different logical pieces of your project as multiple packages.

You can also check out a similar code example in our [repo](https://github.com/webiny/webiny-examples/tree/master/create-package-in-monorepo).
If you have further questions, feel free to [ask](https://www.webiny.com/slack) for additional help.
