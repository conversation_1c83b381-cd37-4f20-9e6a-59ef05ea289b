---
id: aafeab6a
title: Adding Custom CLI Commands
description: Learn how to add custom commands to the Webiny CLI.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- how to create a new Webiny CLI command
- how to override existing commands

</Alert>

## Introduction

[Webiny CLI](/docs/{version}/core-development-concepts/basics/webiny-cli) is pluggable by default, which means you can create new commands, or even override existing ones, simply by adding a new plugin, with its type property set to `cli-command`.

This can be useful if you want to create one or more custom commands for your project, which the developers can use.

## Adding the Command

For this tutorial, we're going to create a new plugin directly in the `webiny.project.js` file, located in your project root folder.

The content of this file might look something like the following:

```js webiny.project.js
module.exports = {
  template: "@webiny/cwp-template-aws@5.0.0",
  name: "my-project",
  cli: {
    plugins: [
      // Webiny CLI commands required from different packages.
      require("@webiny/cli-plugin-workspaces")(),
      require("@webiny/cli-plugin-deploy-pulumi")(),
      require("@webiny/api-page-builder/cli")(),
      require("@webiny/cwp-template-aws/cli")()
    ]
  }
};
```

Notice the plugins required in the `cli.plugins` array. This is where we are going to add our own, for example:

```js webiny.project.js
module.exports = {
  template: "@webiny/cwp-template-aws@5.0.0",
  name: "my-project",
  cli: {
    plugins: [
      // Webiny CLI commands required from different packages.
      require("@webiny/cli-plugin-workspaces")(),
      require("@webiny/cli-plugin-deploy-pulumi")(),
      require("@webiny/api-page-builder/cli")(),
      require("@webiny/cwp-template-aws/cli")(),
      // We added the new CLI command plugin here.
      {
        type: "cli-command",
        name: "cli-command-drink",
        // Here we create the command, using "yargs" library.
        create({ yargs, context }) {
          yargs.example("$0 drink water ");
          yargs.command(
            "drink <what>",
            `Prints a message with the drink you've specified.`,
            yargs => {
              yargs.positional("what", {
                describe: `What do you want to drink?`,
                type: "string",
                default: "nothing"
              });
            },
            // This is the function that'll be called when the command is executed.
            async args => {
              if (args.what === "coffee") {
                context.error("Please, no more coffee.");
                process.exit(1);
              }
              context.log("I want to drink:", args.what);
            }
          );
        }
      }
    ]
  }
};
```

To test it out, in your terminal of choice, you can run the following commands:

```
// Prints "webiny error: Please, no more coffee.'" and exits with exit code 1.
yarn webiny drink coffee

// Prints "webiny log: I want to drink: water" and exits with exit code 0.
yarn webiny drink water
```

<Alert type="info" title="Moving the Code To a Separate File">

Inserting new plugins directly into the `webiny.project.js` works, but overtime, may become crowded. Feel free to move this code into a separate file, for example `scripts/drinkCommand.js`, and use the `webiny.project.js` just to call the necessary `require` statements.

</Alert>

<Alert type="info">

To define commands, we use the `yargs` library, which is provided as one of the named arguments of the `create` function. Be sure to check out the [official Yargs documentation](http://yargs.js.org/) for more information.

</Alert>

## Overriding Existing Commands

We override existing commands by overriding already registered plugins, which is done by registering a new plugin with the `name` property equal to the `name` property of the plugin we want to override.

In the above shown code, we have the following import in the `cli.plugins` array:

```
require("@webiny/cli-plugin-deploy-pulumi")()
```

This function call registers the `cli-command-deployment` plugin, which sets up all of the necessary commands for deploying your project, using the [default deployment solution - Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi). If we wanted to override it, we'd simply register a plugin with the same `cli-command-deployment` name again, and write our own commands in the `create` function we've seen above.

<Alert type="info">

If you can access the plugin you're overriding, you should also remove it. So, if we wanted to override the `cli-command-deployment` plugin, we should also remove the `require("@webiny/cli-plugin-deploy-pulumi")()` statement.

</Alert>
