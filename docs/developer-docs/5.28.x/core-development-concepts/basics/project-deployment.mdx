---
id: aafea95d
title: Project Deployment
description: Learn how to deploy your Webiny project and its project applications, using the Webiny CLI.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to deploy your project into multiple environments
- how to deploy a single project application into multiple environments

</Alert>

## Introduction

Webiny makes it easy to deploy your project, using the [Webiny CLI](/docs/{version}/core-development-concepts/basics/webiny-cli) and [Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi) as the default infrastructure as code and deployment solution. You can also easily deploy your project into multiple [environments](/docs/{version}/infrastructure/basics/environments), which is covered in this guide as well.

## Before We Begin

### AWS Credentials

In order to deploy your Webiny project, you need to have AWS Credentials properly configured on your system. Furthermore, you can also deploy your project using a specific AWS profile.

To learn more about configuring your AWS credentials, please check out the [Configure AWS Credentials](/docs/{version}/infrastructure/aws/configure-aws-credentials) guide. If you're looking for information on how to use different AWS profiles, feel free to skip to the [Use AWS Profiles](/docs/{version}/infrastructure/aws/use-aws-profiles) guide.

### Tagged Resources

Note that, in order to more easily filter out the cloud infrastructure resources deployed by your Webiny project, most of the resources are tagged with the following tags:

- `WbyProjectName` - the name of your project (declared in your `webiny.project.js` file)
- `WbyEnvironment` - the environment of which the resource is part of

## The `deploy` Command

The `deploy` command can be used to completely deploy your project, or just a specific project application.

<Alert type="info">

As explained in the [introduction to deployment](/docs/{version}/infrastructure/basics/introduction) key topic, doing a complete project deployment entails deploying all of its [project applications](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages).

</Alert>

### Deploying the Whole Project

To completely deploy your project, simply run the following command in your terminal of choice:

```bash
yarn webiny deploy
```

This command deploys all of the three applications that are, by default, included in every Webiny project. It deploys them in the following order:

1. **API** (`./api`) - your project's GraphQL API
2. **Admin Area** (`./apps/admin`) - your administration area
3. **Website** (`./apps/website`) - your public website

<Alert type="info">

Note that the first deployment can take up to 20 minutes! So even though it might look like nothing is happening in the terminal, please be patient and let the process finish. If something went wrong, an error will be shown, and the process will exit with an appropriate error code.

</Alert>

### Environments

In general, when deploying projects or project applications, an [environment](/docs/{version}/infrastructure/basics/environments) must be specified. The above shown `deploy` command deploys your project into the `dev` environment by default, but you can also specify a different environment with the `--env` argument. For example, to deploy your project into the `prod` environment, you can run the following command:

```bash
yarn webiny deploy --env prod
```

### Deploying a Project Application

To deploy a specific project application, located within your Webiny project, simply append the path to the folder in which the project application resides, for example:

```bash
yarn webiny deploy api --env dev
yarn webiny deploy apps/admin --env dev
yarn webiny deploy apps/website --env dev
```

Note that when deploying a specific project application, the environment (`--env`) argument is not optional, it must always be set.

## List AWS Resources

To list all cloud infrastructure resources deployed as part of a particular project application, you can run the following command:

```bash
yarn webiny pulumi {folder} --env {env} -- stack --show-ids
```

For example, to list all cloud infrastructure resources deployed as part of the **API** project
application, deployed into the `dev` environment, you can run the following command:

```bash
yarn webiny pulumi api --env dev -- stack --show-ids
```

Running the above command should produce the following output:

![Previewing Stack command](./assets/project-deployment/list-aws-resources.png)

Similarly, you can use the following commands to list all cloud infrastructure resources
deployed as part of the **Admin Area** and **Website** project applications, into the `dev` environment:

```bash
yarn webiny pulumi apps/admin --env dev -- stack --show-ids
yarn webiny pulumi apps/website --env dev -- stack --show-ids
```

<Alert type="info">

For more information on the `webiny pulumi` command, please visit the [Execute Pulumi Commands](/docs/{version}/infrastructure/pulumi-iac/execute-pulumi-commands) guide.

</Alert>

<Alert type="success">

By default, every Webiny project consists of three project applications: **API** (`api`), **Admin Area** (`apps/admin`), and **Website** (`apps/website`).
Read more about project organization in the [Project Organization](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages) key topics section.

</Alert>

## Debugging

If you run into an error while running the `webiny deploy` command, to get additional information and logs about it, you can append the `--debug` argument. For example:

```bash
yarn webiny deploy api --env dev --debug
```

This can significantly help in debugging underlying deployment ([Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi)) errors, since without it, in some cases the returned error report doesn't contain enough useful information. We've also seen cases in which the report would actually be misleading and even incorrect, making the debugging process much harder for the user.

## Building the Application Code

It's worth mentioning that the deployment commands shown above also build your application code. They perform this task by just running the following `workspaces run` command:

```bash
yarn webiny workspaces run build --folder {STACK_FOLDER}
```

The command first finds all packages that are located in the project application folder. Then, for every found package, it runs the `build` command that's specified in the `webiny.config.ts` - a configuration file that every package possesses. If the `build` command is defined, it gets executed, otherwise, the process just continues with the next package.

## Troubleshooting

### Deploying my project takes a long time to finish

We're aware of this fact, and this is mainly because of the [Amazon ElasticSearch Service](https://aws.amazon.com/elasticsearch-service/). While other cloud infrastructure resources get deployed reasonably fast, this service can take anywhere from 15 to 30 minutes to become ready.

Unfortunately, this is a well-known issue for quite some time, and until the present, there haven't been any positive improvements.

The AWS Access Key Id needs a subscription for the service#
In some cases, upon deploying a new Webiny project, it's possible for users to experience the following error being thrown in their terminal:

### SubscriptionRequiredException: The AWS Access Key Id needs a subscription for the service

In some cases, upon deploying a new Webiny project, it's possible for users to experience the following error being thrown in their terminal:

```bash
SubscriptionRequiredException: The AWS Access Key Id needs a subscription for the service
```

The following are some of the reasons why you might be receiving this error:

1. you didn't verify your phone number
2. you didn't verify your payment method
3. you didn't select a support subscription (free or other)
4. if you created a brand new AWS account, it wasn't yet processed on the AWS side

The account signup and verification process can be completed via https://portal.aws.amazon.com/billing/signup?type=resubscribe#/resubscribed.

### The current deployment has x resource(s) with pending operations:

When deploying your project applications, you might have received an error similar to the following:

> error: the current deployment has 1 resource(s) with pending operations:
>
> \* urn:pulumi:dev::website::aws:s3/bucket:Bucket$aws:s3/bucketObject:BucketObject::go.js, interrupted while creating
>
> These resources are in an unknown state because the Pulumi CLI was interrupted while
> waiting for changes to these resources to complete. You should confirm whether or not the
> operations listed completed successfully by checking the state of the appropriate provider.
> For example, if you are using AWS, you can confirm using the AWS Console.
>
> Once you have confirmed the status of the interrupted operations, you can repair your stack
> using 'pulumi stack export' to export your stack to a file. For each operation that succeeded,
> remove that operation from the "pending_operations" section of the file. Once this is complete,
> use 'pulumi stack import' to import the repaired stack.

This error can occur if one of the previous deployments of your [project application](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages) has been [interrupted](/docs/{version}/core-development-concepts/basics/project-deployment#one-of-the-previous-deployments-has-been-interrupted), or another deployment is [currently in progress](/docs/{version}/core-development-concepts/basics/project-deployment#another-deployment-currently-in-progress).

#### One of the Previous Deployments Has Been Interrupted

In case of the former, you'll need to open your Pulumi state files, and find all of the resources whose deployment status is in `pending_operations` state. If we're talking about local development environment, those files are located in your `.pulumi` files and can be manually edited. Otherwise, you'll need to use the mentioned [`pulumi stack export`](https://www.pulumi.com/docs/reference/cli/pulumi_stack_export/) and [`pulumi stack import`](https://www.pulumi.com/docs/reference/cli/pulumi_import/) commands.

If you didn't install the Pulumi CLI manually by yourself, which is usually the case for most Webiny users, you can execute the above commands via the [`pulumi`](/docs/{version}/infrastructure/pulumi-iac/execute-pulumi-commands) command, for example:

```bash
# Export state files for "apps/website" project application ("dev" environment).
yarn webiny pulumi apps/website --env dev -- stack export

# Export state files for the "api" project application ("prod" environment).
yarn webiny pulumi api --env prod -- stack export
```

<Alert type="info">

Learn more about cloud infrastructure state files in the [Cloud Infrastructure State Files](/docs/{version}/core-development-concepts/ci-cd/cloud-infrastructure-state-files) development workflows guide.

</Alert>

#### Another Deployment Currently in Progress

In case of the latter, in which an active deployment currently exists, then you'll just need to wait for the first one to finish, and redeploy your project application. Although not that often, this is more possible to happen in your CI/CD workflows.
