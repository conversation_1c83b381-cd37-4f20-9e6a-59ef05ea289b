---
id: aafeab66
title: Tools and Libraries
description: Learn about different tools and libraries that are included in every Webiny project.
---

import { Al<PERSON> } from "@/components/Alert";
import logos from "./assets/tools-libraries/logos.png";

<Alert type="success" title="What you’ll learn">

- what tools and libraries are included in every Webiny project

</Alert>

<Image src={logos} shadow={false} canEnlarge={false}/>

Webiny is an open-source project, and as such, it is also relying on other open-source software and tools, to bring the best possible experience and increase development productivity.

In this key topic, we briefly cover all of the different tools that are included and automatically configured for you, in every Webiny project.

<Alert type="info">

Note that a Webiny project is a full-stack project, and that some of the tools mentioned might be more relevant only for backend development, and less for frontend (and vice-versa).

</Alert>

## Toolchain

### Languages and Runtimes

In terms of languages, Webiny prioritizes [TypeScript (v4)](https://www.typescriptlang.org/). Only in a few cases, like for example configuration files, you will encounter pure JavaScript.

In terms of runtimes, for backend development, [Node.js](https://nodejs.org/en/) is used. Version 14 is the minimum required version.

### Package Manager

From version 5, every Webiny project is initialized using [yarn 2](https://yarnpkg.com/), with the [workspaces](https://yarnpkg.com/features/workspaces) feature enabled. This is necessary because Webiny projects are using the [monorepo](https://www.atlassian.com/git/tutorials/monorepos) organization.

### Static Code Analysis

Except TypeScript mentioned above, every Webiny project comes with the following static code analysis tools:

- [Prettier](https://prettier.io/) - makes sure the code is consistently formatted across the whole project
- [ESLint](https://eslint.org/) - warns about bad coding practices, redundant code, and more

### Testing

For unit and integration testing purposes, [Jest](https://jestjs.io/) is configured, also with TypeScript support.

For end-to-end (E2E) testing, we provide an easy way to get started with [Cypress](https://www.cypress.io/) - an E2E Testing Framework.

### Building Code

For all code building needs, every Webiny project comes with the gold standard tools:

- [Babel](https://babeljs.io/) - transpiles next-gen code into backwards compatible version of JavaScript
- [Webpack](https://webpack.js.org/) - performs code bundling and optimization

Both tools are used in frontend and backend development.

### Deployment

For everything deployment-related, by default, Webiny projects come with [Pulumi](https://www.pulumi.com/) - a modern infrastructure as code (IaC) solution. Essentially, necessary cloud infrastructure is defined via TypeScript code, and the tool itself performs deployments, change detection, handles multiple [environments](/docs/{version}/infrastructure/basics/environments), and more.

<Alert type="info">

You don't have to install Pulumi by yourself. Webiny automatically downloads necessary binaries for you, and exposes common commands via the [Webiny CLI](/docs/{version}/core-development-concepts/basics/webiny-cli).

To learn more about Pulumi, visit our [IaC with Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi) key topic.

</Alert>

## Backend Development

For backend development or in most cases, development of HTTP APIs, Webiny almost exclusively relies on the [GraphQL](https://graphql.org/) standard, and provides an easy way to create new schemas or expand existing ones, via plugins. Note that, behind the scenes, the [GraphQL Tools](https://www.graphql-tools.com/) library is what is being used to make it happen.

As far as databases are concerned, you are allowed to bring your own database clients and other related tools. But, by default, we do provide our own [database client](https://github.com/webiny/webiny-js/tree/next/packages/db) which you can use as well.

Along with the primary database of default Webiny applications, for example Page Builder and Headless CMS, they also rely on [Elasticsearch](https://www.elastic.co/) - a distributed search and analytics engine. The [official client](https://www.elastic.co/guide/en/elasticsearch/client/javascript-api/current/index.html) is what we use for interacting with it, and we also provide a convenient way to include it in your code, using the [`@webiny/api-elasticsearch`](https://github.com/webiny/webiny-js/tree/next/packages/api-elasticsearch) package.

<Alert type="info">

Read more about the cloud infrastructure resources that get deployed into your AWS account in our [Cloud Infrastructure](/docs/{version}/architecture/introduction) key topics section.

</Alert>

When it comes to authentication and authorization, no particular open source libraries are being used. Webiny provides a couple of its own packages that deal with it.

For communicating with AWS services, you are, naturally, free to use the [AWS SDK](https://aws.amazon.com/sdk-for-javascript/).

Finally, it's worth mentioning that [chrome-aws-lambda](https://www.npmjs.com/package/chrome-aws-lambda) library is used within the Page Builder application, for prerendering pages, and [sharp](https://www.npmjs.com/package/sharp) for transforming images, which is part of the File Manager application.

## Frontend Development

For frontend development, first and foremost, Webiny relies on [React](https://reactjs.org/) - a library for building user interfaces.

On top of it, there are many other libraries that are used, but the most significant might be:

- [Apollo GraphQL Client (v2)](https://www.apollographql.com/docs/react/v2) - for everything GraphQL-related
- [React Material Web Components (RMWC)](https://github.com/jamesmfriedman/rmwc) - used in Admin Area React application, wrapped with our [`@webiny/ui`](https://github.com/webiny/webiny-js/tree/next/packages/ui) package
- [Emotion](https://emotion.sh/docs/introduction) - used for additional CSS styling

For everything else, you're free to bring your own libraries and tools.

## FAQ

### Can I bring my own tools and libraries?

Yes. The mentioned tools and libraries create a solid foundation, but feel free to expand it in any way needed.

### Can I use another UI library, for example [Vue.js](https://vuejs.org/)?

The Admin Area application, and other Webiny applications like Page Builder or Form Builder, are built with React, so when using or expanding these, you will have to use it too.

But if you're building a brand new app from scratch, and, for example, you just want to interact with the Webiny GraphQL API, you can certainly use another library.

### Can I replace the [RMWC](https://github.com/jamesmfriedman/rmwc) that's used in the Admin Area React application?

You can, but it has been shown several times that it's not that easy. We don't recommend doing it because, essentially, you'd be recreating everything from scratch. If you still want to try, [send us a message](https://www.webiny.com/slack/), we might be able to help.
