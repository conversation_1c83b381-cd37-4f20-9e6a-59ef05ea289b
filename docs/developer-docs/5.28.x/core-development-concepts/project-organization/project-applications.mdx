---
id: aafea964
title: Project Applications
description: Learn what are project applications and how they're structured.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU’LL LEARN">

- what are project applications and how they're structured

</Alert>

## The basics

Webiny uses the term **project application** (or just **application**) in order to depict a specific logical segment of your project.

Project applications are higher-level organizational units formed from one or more packages that, as the name itself suggests, form applications. Every application, essentially, consists of two pieces:

1. Your application code, which includes one or more Node.js packages. These packages can be anything from a simple GraphQL API or a single Lambda function to a complete React application.
2. Cloud infrastructure that hosts your code or which is being utilized by it, which is also described and deployed using code. For that matter, by default, Webiny relies on a solution called [Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi), which is a modern infrastructure as code solution.

<Alert type="info">

Learn more about the fundamental organizational units - project applications and packages, by visiting the [Project Applications and Packages](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages) page.

</Alert>

Moving on, every application has its own folder. For example, in a newly created Webiny project, by default, you end up with three applications.
The **api** (`./api`), which represents your project's (GraphQL) API, **admin** (`./apps/admin`), and finally, the **website** (`./apps/website`), which is your public website.

These applications are shown in the following directory tree:

```
.
├── api
│   ├── pulumi
│   ├── Pulumi.yaml
│   ├── Pulumi.dev.yaml
│   ├── code
│   └── index.ts
├── apps
│   ├── admin
│   │   ├── pulumi
│   │   ├── Pulumi.yaml
│   │   ├── Pulumi.dev.yaml
│   │   ├── code
│   │   └── index.ts
│   └── website
│       ├── pulumi
│       ├── Pulumi.dev.yaml
│       ├── Pulumi.yaml
│       ├── code
│       └── index.ts
└── (...)
```

## A closer look

If we take a look at the directory tree above, we can notice a consistent files and folders structure.

As mentioned, every application consists of your code and cloud infrastructure (also described via code).

The `code` folder is where all of the application's code is located. In the case of the `api` (`./api/code`), it contains several packages that form the GraphQL API. In case of `admin` (`./apps/admin/code`) and `website` (`./apps/website/code`), it contains React applications.

On the other hand, the `pulumi` folder and the `Pulumi.yaml`, `Pulumi.dev.yaml`, and `index.ts` files contain Pulumi code that define your cloud infrastructure. Again, in the case of the `api` application, these define several cloud infrastructure resources like Lambda functions, an API Gateway, a DynamoDB table, and so on. The `admin` and `website` applications contain fewer cloud resources, as they serve a bit simple (but still important) purpose.

<Alert type="info">

To learn more about Pulumi and relevant files mentioned above, check the [Infrastructure as code with Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi) key topic.

</Alert>

<Alert type="info">

To learn more about the necessary cloud infrastructure resources Webiny is relying on, check out the [Cloud Infrastructure](/docs/{version}/architecture/introduction) page.

</Alert>

## FAQ

### How many applications can a single Webiny project have?

Every Webiny project can have any number of applications. This depends on the project requirements that are in front of you.

### Can applications work together?

Yes, and in most cases, [they are working together](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages#package-and-project-application-dependencies).
