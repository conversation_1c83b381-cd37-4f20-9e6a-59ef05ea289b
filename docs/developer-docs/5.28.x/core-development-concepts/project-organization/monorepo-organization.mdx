---
id: aafea961
title: Monorepo Organization
description: Learn what are monorepos and what are the benefits this project organization brings.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU’LL LEARN">

- what are monorepos and what are the benefits of using them
- where can a list of all workspaces be found

</Alert>

## What are Monorepos?

Every Webiny project is organized as a [monorepo](https://www.atlassian.com/git/tutorials/monorepos). This essentially means your project is organized as a collection of multiple NPM packages, or, in the context of monorepos, multiple workspaces.

Behind the scenes, it is the [yarn 2](https://yarnpkg.com/) package manager that enables this organization, via its [workspaces feature](https://yarnpkg.com/features/workspaces):

> The Yarn workspaces aim to make working with monorepos easy, solving one of the main use cases for yarn link in a more declarative way. In short, they allow multiple of your projects to live together in the same repository AND to cross-reference each others - any modification to one's source code being instantly applied to the others.

## Workspaces vs. Packages

Terms **workspace** and **package**, essentially, refer to the same thing - an NPM package or in other words, a folder with a `package.json` file and some code. Depending on the context, we use one of the two.

When talking about the monorepo organization and yarn 2 and its workspaces feature, then the term **workspace** makes more sense to use. On the other hand, when talking about project applications and the actual code, the term **package** is used. We always say "import this package", never "import this workspace".

<Alert type="info">

For more information on packages and their role in a Webiny project, take a step back and check the [Applications and Packages](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages) topic.

</Alert>

## Benefits of the Monorepo Organization

Monorepo organization benefits us in a couple of ways.

### Multiple Packages

Monorepo organization makes it possible to structure different logical pieces of your project as multiple packages. For example, you might have a package that contains the business logic that deals with customer data, then the other one that deals with customer orders, and so on. You can even create shared or utility packages, or in other words, packages that contain one or more utility functions, which are then imported from multiple other packages.

An abstract example of packages and shared packages, located in different project applications, can be found in the [Project Applications and Packages](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages) topic:

![Project Organization](./assets/project-applications-and-packages/project-organization.png)

### Optimally Storing Dependencies

The yarn 2 workspaces feature makes sure that the same dependencies required by multiple packages are stored in a most efficient way. For example, when specifying the React library as a dependency in two or more packages, the library is actually downloaded only once. On larger projects, this significantly reduces the overall size on disk of all of the different dependencies that your packages may require.

## List of Workspaces

In every Webiny project, the list of all workspaces can be found in the `package.json` file, located in your project root, for example:

```
(...)
"workspaces": {
    "packages": [
        "packages/*",
        "apps/admin/code",
        "apps/website/code",
        "apps/theme",
        "api/code/fileManager/*",
        "api/code/graphql",
        "api/code/getTime",
        "api/code/headlessCMS",
        "api/code/pageBuilder/*",
        "api/code/prerenderingService/render",
        "api/code/prerenderingService/flush",
        "api/code/prerenderingService/queue/*"
    ]
},
(...)
```

In addition to the above shown list, you're also free to add more workspaces, if need be. You will be updating this list whenever you're about to add a new package to your project.

<Alert type="info">

For convenience's sake, different scaffolding commands, that create new packages, update the workspaces list automatically for you.

</Alert>

## FAQ

### Is Monorepo organization set up by default in every Webiny project?

Yes, every Webiny project is organized as a monorepo.

### Can we use a different package manager, like yarn classic or NPM?\*\*

At the moment, Webiny projects are set up to work with [yarn 2](https://yarnpkg.com/), exclusively.
