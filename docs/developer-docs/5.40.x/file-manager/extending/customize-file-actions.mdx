---
id: cf32d9df
title: Customize File Actions
description: Learn how to customize file actions in File Manager, either globally or for specific file types.
---

import fmFilesActions from "./assets/fm-files-actions.png";
import fmFilesActions2 from "./assets/fm-files-actions-2.png";
import fmFilesGridActionPlay from "./assets/fm-files-grid-action-play.png";
import fmFilesGridDetailsActionPlay from "./assets/fm-files-details-action-play.png";

<CanIUseThis since={"5.40.0"} />

<WhatYouWillLearn>

- how to customize file actions globally (for all types of files)
- how to customize file actions for specific types of files

</WhatYouWillLearn>

## Overview

Files in File Manager can have different actions associated with them. These actions are displayed in the file browser (when hovering over a file) and file details panel. 

<Image src={fmFilesActions} title="File Actions In File Browser" />

<Image src={fmFilesActions2} title="File Actions In File Details Panel" />

On top of the built-in actions, it's also possible to add custom actions, which can be displayed for all types of files (global actions) or for specific file types. 

In this guide, we show how to add custom actions for both scenarios.

## Getting Started

<ExtensionsGettingStarted type={"admin"} name={"customFileActions"} dependencies={"@webiny/app-file-manager,@material-design-icons/svg"} />

## Global File Actions

In the following snippet of code, we show how we can introduce a file action that will be displayed for all types of files.

```tsx extensions/customFileActions/src/index.tsx
import React from "react";
import { FileManagerViewConfig, useFile } from "@webiny/app-file-manager";
import { ReactComponent as PlayIcon } from "@material-design-icons/svg/round/play_arrow.svg";

// To make the code more readable, we immediately destructure child components.
const { Browser, FileDetails } = FileManagerViewConfig;

const GridItemAction = () => {
    return (
        <Browser.Grid.Item.Action.IconButton
            icon={<PlayIcon />}
            onAction={() => {
                alert("In the future, this will be useful.");
            }}
        />
    );
};

const FileDetailsAction = () => {
    return (
        <FileDetails.Action.IconButton
            label={"Play Video"}
            icon={<PlayIcon />}
            onAction={() => {
                alert("In the future, this will be useful.");
            }}
        />
    );
};

export const Extension = () => {
    return (
        <>
            <FileManagerViewConfig>
                <Browser.Grid.Item.Action name={"playVideo"} element={<GridItemAction />} />
                <FileDetails.Action name={"playVideo"} element={<FileDetailsAction />} />
            </FileManagerViewConfig>
        </>
    );
};
```

As we can see, we created two components, `GridItemAction` and `FileDetailsAction`, which represent the action in the file browser (grid view) and file details panel, respectively. Once created, we register these components as actions with the `FileManagerViewConfig` component and its `Browser.Grid.Item.Action` and `FileDetails.Action` components.

With this code in place, the **Play Video** action will be displayed for all types of files, both in the file browser (grid view) and file details panel.

<Image src={fmFilesGridActionPlay} title="File Browser - Play Video Action" />

<Image src={fmFilesGridDetailsActionPlay} title="File Details Panel - Play Video Action" />

## File Type Specific File Actions

Besides global actions, we can also introduce actions for specific file types. 

To do that, in the `GridItemAction` and `FileDetailsAction` components, we use the `useFile` hook to get the current file and check its `type` property. If the file type is not `video/quicktime`, we return `null` and the action is not displayed.

```diff-tsx extensions/customFileActions/src/index.tsx
import React from "react";
+ import { FileManagerViewConfig, useFile } from "@webiny/app-file-manager";
import { ReactComponent as PlayIcon } from "@material-design-icons/svg/round/play_arrow.svg";

// To make the code more readable, we immediately destructure child components.
const { Browser, FileDetails } = FileManagerViewConfig;

const GridItemAction = () => {
+    const { file } = useFile();
+    if (file.type !== "video/quicktime") {
+        return null;
+    }

    return (
        <Browser.Grid.Item.Action.IconButton
            icon={<PlayIcon />}
            onAction={() => {
                alert("In the future, this will be useful.");
            }}
        />
    );
};

const FileDetailsAction = () => {
+   const { file } = useFile();
+   if (file.type !== "video/quicktime") {
+       return null;
+   }

    return (
        <FileDetails.Action.IconButton
            label={"Play Video"}
            icon={<PlayIcon />}
            onAction={() => {
                alert("In the future, this will be useful.");
            }}
        />
    );
};

export const Extension = () => {
    return (
        <>
            <FileManagerViewConfig>
                <Browser.Grid.Item.Action name={"playVideo"} element={<GridItemAction />} />
                <FileDetails.Action name={"playVideo"} element={<FileDetailsAction />} />
            </FileManagerViewConfig>
        </>
    );
};
```
