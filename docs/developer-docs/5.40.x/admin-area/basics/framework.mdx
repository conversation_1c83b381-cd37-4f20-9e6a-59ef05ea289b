---
id: aafeab63
title: The Framework
description: Learn what makes the Admin app tick, and how plugins work.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="info" title="Can I Use This?">

In order to follow this guide, you must use Webiny version **5.21.0** or greater.

</Alert>

<Alert type="success" title="What you'll learn">

- the moving parts of the Admin app framework
- what is a plugin
- how the Admin app is rendered

</Alert>

## Overview

Admin app is powered by a simple React framework, which allows you to add new [React Context providers](https://reactjs.org/docs/context.html#contextprovider), compose existing UI components, register routes, and do all that using plain [React components](https://reactjs.org/docs/react-component.html), and widely used concepts, like [Higher Order Components](https://reactjs.org/docs/higher-order-components.html) (hereinafter: HOCs), and [hooks](https://reactjs.org/docs/hooks-intro.html).

In the following sections, we cover 5 fundamental components which make everything work. These 5 components make up the low-level API, and in the vast majority of cases, these are the only components you'll need to use to develop new plugins (besides your own custom components and views).

The following diagram shows all 5 components in action:

![Admin App Framework In Action](./assets/framework/AdminAppFramework.png)

As you can see, what you mount, is not exactly what gets rendered. The `Admin` component (the framework) takes care of rendering things _where_ and _when_ they need to be rendered, and also does various compositions and optimizations (caching) in the process.

A good example of that are the React Context providers (represented by the `Provider` component in the diagram). Traditionally, when using Higher Order Components (HOCs), we end up with a deep hierarchy of nested components, which is hard to read, and even harder to maintain. In these cases, we usually resort to HOC composition, to flatten out the hierarchy, and produce a single component which is constructed of the individual HOCs wrapped around the previous one. We use the same technique to construct React Context providers.

## The `Plugins` Component

To explain why we need this component, let's quickly describe what a _plugin_ is.

A plugin is a React component that can do things like adding a route, adding a menu, adding a file type renderer, etc. Plugins can also be composed into larger plugin components, to perform more complex tasks.

More often than not, a plugin component needs to access one or more React Contexts provided by various apps (Page Builder, File Manager, etc.), to do its job. To be able to do that, plugin components need to be rendered as children of all those React Context providers (if unsure, revisit the diagram in the [Overview](#overview) section).

The purpose of the `Plugins` component is to make sure that its children are rendered at the right place within the app component hierarchy. It helps you not to think about _where_ you mount your plugin components, so you're more flexible with how you build your custom plugins.

<Alert type="success">

You can mount as many Plugin elements as you need, anywhere in the app components hierarchy. The only important thing is that you _do_ mount it when you want to add one or more plugins to the app.

</Alert>

Here's the simplest, most straightforward example of using the `Plugins` component:

```tsx Using <Plugins/> to Add Routes and Menus
import React from "react";
import { Admin, Plugins, AddMenu, AddRoute, Layout } from "@webiny/app-serverless-cms";

// Mount everything as children of the <Admin> element.
export const App = () => {
  return (
    <Admin>
      <Plugins>
        <AddMenu id={"myApp"} label={"My App"}>
          <AddMenu id={"myApp.records"} label={"Records"} path={"/my-app/records"} />
        </AddMenu>
        <AddRoute path={"/my-app/records"}>
          <Layout title={"My App - Records"}>{/* Your UI goes here. */}</Layout>
        </AddRoute>
      </Plugins>
    </Admin>
  );
};
```

The `AddRoute` component is described in the dedicated [Custom Routes](/docs/admin-area/extending/custom-routes) article.

The `AddMenu` component is described in the dedicated [Customize Navigation](/docs/admin-area/extending/customize-navigation) article.

Let's see a more advanced example, and demonstrate a real-life example of menus, routes, and permissions:

```tsx Extension Composition
import React from "react";
import {
  Admin,
  Plugins,
  AddMenu,
  AddRoute,
  Layout,
  HasPermission
} from "@webiny/app-serverless-cms";

export const MyExtension = () => {
  return (
    <Plugins>
      {/* Only mount the top-level menu element if the user has the required permission. */}
      <HasPermission name={"myApp"}>
        <AddMenu id={"myApp"} label={"My App"}>
          {/* Only mount sub-menu element and the corresponding route, if the user has the required permission. */}
          <HasPermission name={"myApp.records"}>
            <AddMenu id={"myApp.records"} label={"Records"} path={"/my-app/records"} />
            <AddRoute path={"/my-app/records"}>
              <Layout title={"My App - Records"}>{/* Your UI goes here. */}</Layout>
            </AddRoute>
          </HasPermission>
        </AddMenu>
      </HasPermission>
    </Plugins>
  );
};

export const App = () => {
  return (
    <Admin>
      <MyExtension />
    </Admin>
  );
};
```

<Alert type={"info"} title={"Extensions"}>

  We highly recommend using our [Extensions](/docs/core-development-concepts/basics/extensions) to organize your code in a scalable and portable manner.

</Alert>

## Naming Conventions

There are many components exported from the `@webiny/app-serverless-cms` package. These naming conventions will help you find your way around, and quickly filter out components in your IDE.

- components that _add_ things to the system start with `Add` prefix (e.g., `AddMenu`)
- presentation components end with `Renderer` (e.g., `MenuItemRenderer`)
- hooks, by React convention, start with `use` (e.g., `useSecurity`)

Almost always, renderer components go with a parent component that provides a React Context to the renderer. For example, a `UserMenuItem` has a corresponding `UserMenuItemRenderer` component. The renderer component doesn't take any props; instead, it uses hooks to fetch the relevant data from React Context, provided by the `UserMenuItem` component. This approach drastically reduces prop drilling, and makes it easier to decouple logic from presentation.

If you want to change the appearance (the renderer) of a certain component, always look for the `Renderer` part of it, and decorate _that_ component.

## Conclusion

These few simple components is what makes up the core of the Admin app. This provides strong and simple foundations to build upon. It's easily composable, and gives the ability to lazy-load plugins, apply plugins conditionally, compose component renderers, and also compose these atomic components into larger extensions.
