---
id: aafeaa17
title: Webiny CLI
description: Learn what Webiny CLI is and how it can help you in your project development.
---

import { Alert } from "@/components/Alert";
import webinyInfoCommand from "./assets/webiny-info-command.png";
import webinyAboutCommand from "./assets/webiny-about-command.png";

<Alert type="success" title="What you'll learn">

  - what is the Webiny CLI
  - what are the commonly used commands

</Alert>

## Overview

Webiny CLI (command line interface) is the central tool that you'll be using throughout the complete project development process, in your terminal of choice.

Out of the box, it offers a couple of commands that will let you perform essential tasks like deployments, development and production code builds, different scaffolding, and so on.

Additionally, the Webiny CLI is pluggable, meaning you can easily create your own custom commands, or even replace the existing ones. Please visit the [Adding Custom Commands](/docs/{version}/core-development-concepts/extending-and-customizing/adding-custom-cli-commands) guide for more information.

<Alert type="info">

  For a full list of commands, in your terminal of choice, make sure to run `yarn webiny --help`.

</Alert>

## Commands

In the following sections, we cover the most commonly used commands that the Webiny CLI provides.

### Deployments

Here are the most commonly used deployments-related commands.

#### `yarn webiny deploy --env ENV`

Builds all of the [project applications](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages) and deploys cloud infrastructure resources defined within them, into the specified environment.

By default, the command will deploy project applications into the `dev` environment. But, you can specify a different one using the `--env` argument.

#### `yarn webiny deploy APP --env ENV`

Builds a project application located inside of the specified folder and deploys cloud infrastructure resources defined within it, into the specified environment.

The `--env` argument is required.

#### `yarn webiny destroy APP --env ENV`

Destroys cloud infrastructure resources that were previously deployed as part of the specified project application, into a specified environment.

The `--env` argument is required.

<Alert type="info">

  For more hands-on information on the above listed commands, please visit the [Deploy Your Project](/docs/{version}/core-development-concepts/basics/project-deployment) and [Destroy Cloud Infrastructure](/docs/{version}/infrastructure/basics/destroy-cloud-infrastructure) guides.

</Alert>

#### `yarn webiny pulumi APP --env ENV -- PULUMI_COMMAND`

Provides a way to execute Pulumi specific commands directly via the Pulumi CLI.

<Alert type="info">

  For more information, please visit the [Execute Pulumi Commands](/docs/{version}/infrastructure/pulumi-iac/execute-pulumi-commands) guide.

</Alert>

#### `yarn webiny output APP --env ENV`

Returns Pulumi stack output for the specified project application and environment.

### Development

Here are the most commonly used development-related commands.

#### `yarn webiny watch APP --env ENV` 

Watches specified project application for application and cloud infrastructure code changes, and performs rebuilds and redeploys, accordingly.

<Alert type="info">

  For more information, please visit the [Use Watch Command](/docs/{version}/core-development-concepts/basics/watch-command) guide.

</Alert>

#### `yarn webiny build APP --env ENV`

Builds a project application located inside of the specified folder.

#### `yarn webiny info --env ENV`

Returns useful project information, like AWS region and useful URLs.

<Image src={webinyInfoCommand} title="Command output" />

#### `yarn webiny about`

Prints out information helpful for debugging purposes.

<Image src={webinyAboutCommand} title="Command output" />

#### `yarn webiny ws run CMD --folder FOLDER --scope SCOPE`

Runs provided command, for example the `watch` command, across multiple workspaces (packages) in your project. Workspaces are defined via either the `--folder` or `--scope` argument.

### Other

Here are some other commonly used commands.

#### `yarn webiny disable-telemetry`

Completely disables collection of anonymous usage information.

<Alert type="warning" title="Telemetry">

  By default, Webiny collects anonymous usage information, which is exclusively used for improving the product and understanding usage patterns. Please take a look at our [Telemetry](https://www.webiny.com/telemetry/) page for more information on this subject.

</Alert>

## FAQ

### Do I need to install Webiny CLI manually?

No, Webiny CLI comes set up automatically with every new Webiny project. Note that it's not installed as a global tool, but on a per-project basis. So, in theory, you could have two projects with different versions of the CLI.

### Do I need to invoke the Webiny CLI with `yarn`?

For the highest chance that everything will work as expected, we recommend you do so. But do note that if you're using some of the alternative terminals, like for example [Zsh](https://ohmyz.sh/), you might even get away without it.
