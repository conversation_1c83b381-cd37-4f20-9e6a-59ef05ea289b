---
id: 761e90f6
title: Extensions
description: Learn what extensions are and how they can be used to extend Webiny's functionality.
---

import { WhatY<PERSON><PERSON>illLearn } from "@/components/WhatYouWillLearn";
import webinyScaffoldCommand from "./assets/webiny-scaffold-command.png";

<WhatYouWillLearn>

  - what extensions are and how they can be used to extend Webiny's functionality.

</WhatYouWillLearn>

## Overview

One of the most powerful features of Webiny is the ability to extend the default functionality. This is done by creating and registering **extensions**. Extensions are a way to add new functionality to the system, or to modify the existing one.

There are a couple of types of extensions in Webiny, but the most common ones are:

- **Admin** - used to extend the Webiny's Admin app
- **API** - used to extend Webiny's backend functionality (for example its GraphQL API)
- **Cloud Infrastructure** - used to extend the cloud infrastructure Webiny deploys

For example, via an Admin extension, we can change the [layout of the dashboard](/docs/admin-area/extending/custom-dashboard) or [change the logo](/docs/admin-area/extending/change-logo) that's shown in the header and the main menu. On the other hand, via an API extension, we can introduce new GraphQL queries or mutations, [content models](/docs/headless-cms/extending/content-models-via-code), or [hook into lifecycle events](/docs/headless-cms/references/lifecycle-events).

## Getting Started

In general, the easiest way to start working on a new extension is by using the **New Extension** scaffold, which can be accessed via the `webiny scaffold` command:

```bash
yarn webiny scaffold
```

Once run and the **New Extension** scaffold is selected, users are prompted to provide the basic information about the extension, like extension type and name.

<Image src={webinyScaffoldCommand} alt="Webiny Scaffold Command" />

Ultimately, the scaffold creates the base code for the new extension in the `extensions` folder, which is the place where all extensions are stored.

<Alert type="info">

   There are a couple of cases where the extension code is placed outside of the `extensions` folder. For example, when [modifying cloud infrastructure](/docs/infrastructure/basics/modify-cloud-infrastructure), the code is placed in different `webiny.application.ts` files, located in the `apps` folder.

</Alert>

## FAQ

### What is the difference between an extension and a plugin?

In Webiny, extensions are used to extend the system's functionality, and an extensions can be made up of one or more plugins. A plugin is a small piece of code that adds a specific functionality to the system. For example, a plugin can be used to add a new GraphQL query or mutation, or to modify the existing one.
