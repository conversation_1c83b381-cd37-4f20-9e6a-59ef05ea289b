---
id: 3de9b4ff
title: Production
description: Learn how the necessary cloud infrastructure resources are deployed within the custom VPC.
---

import { Alert } from "@/components/Alert";
import prodMode from "./assets/webiny_api_overview_vpc_custom.png";

<Alert type="success" title="What you’ll learn">

- how the necessary cloud infrastructure resources are deployed within a custom VPC

</Alert>

## Diagram

<Image
  src={prodMode}
  title="Webiny Cloud Infrastructure - Modes - Production Mode"
  shadow={false}
/>

## Description

Unlike in the development setup, where your project is deployed into the default VPC, in the production setup, your project is deployed into a custom Virtual Private Cloud (VPC), which we cover in this section.

Note that the VPC setup presented here is a good foundation, but is not an ultimate solution. There is a chance that the setup might need additional cloud infrastructure resources or different configurations on your or your organization's behalf.

<Alert type="info">

Virtual Private Clouds (VPCs) is a topic that requires some general networking knowledge and knowledge on AWS-specific concepts like regions, availability zones, different network gateways, and so on. Be sure to [read about it](https://aws.amazon.com/vpc/) before going through this section.

</Alert>

### Key Differences

When compared to the development mode, essentially, resources still work and communicate with each other in the same way, except this time, there are a couple of additional network-level resources and rules in place. This helps in improving your project's availability and overall security posture.

#### Public and private subnets

The most prominent change, when compared to the development mode, is the inclusion of a VPC that consists of three subnets - one public <diagram-letter>C</diagram-letter> and two private <diagram-letter>D</diagram-letter> <diagram-letter>e</diagram-letter>, deployed across multiple availability zones (AZs).

<br />
<br />

With this network structure, you are given the opportunity to place mission-critical cloud infrastructure resources into the private subnets <diagram-letter>D</diagram-letter> <diagram-letter>E</diagram-letter>, which makes these resources more secure, because they are not directly exposed to the public internet. This is especially important when talking about hosting databases, for example the Amazon OpenSearch Service <diagram-letter>H</diagram-letter>.

<Alert type="info">

With the Amazon OpenSearch Service <diagram-letter>H</diagram-letter> placed inside of a private subnet, note that you can't connect to it directly from your machine. [Deploying a jump-box (bastion host)](https://aws.amazon.com/blogs/security/how-to-record-ssh-sessions-established-through-a-bastion-host/) in a public subnet can resolve this problem.

</Alert>

#### Multiple Availability Zones

As mentioned, the public and private subnets are deployed across multiple availability zones (AZs). This helps in making your application more highly available, fault tolerant and scalable. For example, if in a single region, one of the AZs goes offline, all of the network traffic is essentially routed to other AZs that are online. This means your application still works.

Note that the number of distinct AZs depends on the region you're deploying to as some only have 2 AZs.

<Alert type="danger">

Have in mind that hosting your application in multiple availability zones may incur additional cost, since some of the cloud infrastructure resources need to be deployed multiple times. For example, this is true for Amazon OpenSearch Service <diagram-letter>h</diagram-letter>.

</Alert>

#### External internet-facing cloud infrastructure resources

The only way resources located in the private subnets <diagram-letter>D</diagram-letter> <diagram-letter>e</diagram-letter> can talk to the public internet is via the public subnet <diagram-letter>C</diagram-letter>, which includes a NAT gateway <diagram-letter>F</diagram-letter>. The NAT (network address translation) gateway is the middleman that forwards all internet-routable network traffic, received from private subnets, to the Internet Gateway <diagram-letter>g</diagram-letter>.

This makes it possible for AWS Lambda functions that are located in private subnets <diagram-letter>D</diagram-letter> <diagram-letter>E</diagram-letter> to talk to AWS resources that operate in an internet facing environment, like Amazon DynamoDB <diagram-letter>i</diagram-letter>, Amazon S3 <diagram-letter>j</diagram-letter>, and Amazon Cognito <diagram-letter>k</diagram-letter>.

<Alert type="warning">

Note that when private subnet resources are communicating with the ones operating in an internet facing environment <diagram-letter>i</diagram-letter> <diagram-letter>j</diagram-letter> <diagram-letter>k</diagram-letter>, sending and receiving data is still performed across the public internet <diagram-letter>l</diagram-letter>.

If this presents a problem, you may want to integrate a different solution, like for example VPC endpoints for DynamoDB. Check out the [Using Amazon VPC Endpoints to Access DynamoDB](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/vpc-endpoints-dynamodb.html) article to learn more.

</Alert>

## FAQ

### Is Amazon OpenSearch Service <diagram-letter>h</diagram-letter> deployed into multiple AZs?

For the production setup, that is true. For development setup, it is deployed into a single AZ, just for cost reasons.

Check out the [Amazon OpenSearch Service's FAQ page](https://aws.amazon.com/opensearch-service/faqs/) for more information.

### How is API Gateway communicating with AWS Lambda functions if it's outside of the VPC?

To our knowledge, there is no official evidence on how this actually works. But, since no additional configuration was needed in order to establish the API Gateway <diagram-letter>B</diagram-letter> - Lambda Functions connection, it means that this is automatically handled for you by AWS's internal structure and mechanisms. This [Stack Overflow](https://stackoverflow.com/questions/60678826/aws-route-from-public-api-gateway-to-in-vpc-lambda) question briefly discusses this, but again, no concrete evidence and answers are provided.
