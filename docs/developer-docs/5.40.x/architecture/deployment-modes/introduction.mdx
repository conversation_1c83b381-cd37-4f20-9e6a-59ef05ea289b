---
id: aafeab38
title: Introduction
description: Introduction to the development and production deployment modes.
---

import { <PERSON>ert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- what are the two different deployment modes
- why do we need different deployment modes

</Alert>

## Overview

The **Core** and **API** project application can be deployed in two different deployment modes: **development** and **production**.

This is simply because, for development purposes, not all of the production cloud infrastructure resources are needed. For example, for improved security posture, the production mode will deploy all of your AWS Lambda functions into a custom [Virtual Private Cloud (VPC)](https://aws.amazon.com/vpc/) with private subnets. It will also deploy your [Amazon OpenSearch Service](https://aws.amazon.com/opensearch-service/) into multiple availability zones (AZs), in order to provide high availability.

<Alert type="info">

The **Admin Area** and **Website** project applications do not posses the ability to be deployed in development and production modes, as it's not needed.

</Alert>

By default, the development mode is used when deploying into any environment, except `prod`. In that case, the production mode will be used.
