---
id: aafeab39
title: Development
description: Learn how the necessary cloud infrastructure resources are deployed in the development mode.
---

import { Alert } from "@/components/Alert";
import devMode from "./assets/webiny_api_overview_vpc_default.png";

<Alert type="success" title="What you’ll learn">

- how the necessary cloud infrastructure resources are deployed in the development mode

</Alert>

## Diagram

<Image
  src={devMode}
  title="Webiny Cloud Infrastructure - Modes - Development Mode"
  shadow={false}
/>

## Description

<Alert type="info">

Virtual Private Clouds (VPCs) is a topic that requires some general networking knowledge and knowledge on AWS-specific concepts like regions, availability zones, different network gateways, and so on. Be sure to [read about it](https://aws.amazon.com/vpc/) before going through this section.

You can also check this [Understanding the Default Virtual Private Cloud](https://www.rackspace.com/blog/aws-201-understanding-the-default-virtual-private-cloud) article. It clearly lays out a lot of useful information about AWS's default VPC setup.

</Alert>

In development mode, your project is deployed into the default Virtual Private Cloud (VPC), which is automatically created for every AWS Account.

In the diagram above, we can see that the default VPC setup consists of three subnets <diagram-letter>c</diagram-letter> <diagram-letter>d</diagram-letter> <diagram-letter>e</diagram-letter> that span across three availability zones (AZs). All of these subnets are public subnets, since they can both receive and send network traffic to the public internet. When it comes to availability zones, note that the number of zones may vary, depending on the region you're deploying into (some regions only have two).

<br />
<br />
We can also see that AWS Lambda functions are located in all three subnets. This is because, by
default, AWS runs AWS Lambda functions in multiple AZs to ensure it is highly available in case of
an AZ failure. The same cannot be said for the Amazon OpenSearch Service <diagram-letter>
  h
</diagram-letter>, which is only deployed in a single AZ <diagram-letter>C</diagram-letter>.

<Alert type="info">

The Amazon OpenSearch Service is only deployed with the **Amazon DynamoDB + Amazon OpenSearch Service** setup.

For more information, visit the [Different Setups](/docs/{version}/get-started/install-webiny#pick-your-database) sections.

</Alert>

Development mode is a reasonable choice when it comes to development environments. But, for production, since some of the cloud infrastructure resources may require that they are not exposed to the public internet, the [production](/docs/{version}/architecture/deployment-modes/production) may be a better solution.

## FAQ

### Is Amazon OpenSearch Service deployed into multiple AZs?

In the development mode, it is deployed into a single AZ, just for cost reasons. But yes, production workloads should use two or three AZs, hence the production mode. Check out the [Amazon OpenSearch Service's FAQ page](https://aws.amazon.com/opensearch-service/faqs/) for more information.
