---
id: aafeab34
title: Introduction
description: Introduction to the Architecture section, which explains everything related to the cloud infrastructure Webiny relies on.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- what are the default project applications included in a Webiny project
- what is the most complex project application in terms of the deployed cloud infrastructure

</Alert>

## Overview

A Webiny project consists of four [project applications](/docs/{version}/core-development-concepts/project-organization/project-applications):

1. **Core** (`./apps/core`) - defines core cloud infrastructure resources
1. **API** (`./apps/api`) - your project's (GraphQL) HTTP API
1. **Admin** (`./apps/admin`) - your admin area
1. **Website** (`./apps/website`) - your public website

If we wanted to compare these applications by the complexity of the cloud infrastructure, we could say that **Core** and **API** are the most complex ones. This is simply because these deploy more different cloud infrastructure resources than the remaining **Admin** and **Website** project applications.

<Alert type="info">

Visit the [Deployments](/docs/{version}/infrastructure/basics/introduction) key topics section to learn more about how Webiny approaches cloud infrastructure deployment.

</Alert>

In the following sections, we examine which cloud infrastructure resources get deployed for each project application, how they are working together, different configurations, and other related information.

Before we continue, a couple of words on different Webiny setups and deployment modes.

## Different Database Setups

At the moment, Webiny can be exclusively deployed into [Amazon Web Services (AWS)](https://aws.amazon.com/), where users can choose between two different database setups:

1. Amazon DynamoDB
2. Amazon DynamoDB + Amazon OpenSearch

Essentially, the setup you choose depends on the size of your project. For small and medium-sized projects or prototypes, we recommend users go with the Amazon DynamoDB setup. For larger and enterprise projects, we recommends the Amazon DynamoDB + Amazon OpenSearch one.

<Alert type="info">

The diagrams shown in the upcoming sections are representing the Amazon DynamoDB + Amazon OpenSearch database setup. When it comes to the Amazon DynamoDB setup, everything stays the same, except, of course, the Amazon OpenSearch service isn't used.

</Alert>

## Development and Production Deployment Modes

Note that the **Core** and **API** project application can be deployed in two different deployment modes: **development** and **production**.

This is simply because, for development purposes, not all of the production cloud infrastructure resources are needed. For example, for improved security posture, the production mode will deploy all of your AWS Lambda functions into a custom [Virtual Private Cloud (VPC)](https://aws.amazon.com/vpc/) with private subnets. It will also deploy your [Amazon OpenSearch Service](https://aws.amazon.com/opensearch-service/) into multiple availability zones (AZs), in order to provide high availability.

By default, the development mode is used when deploying into any environment, except `prod`. In that case, the production mode will be used.

<Alert type="info">

  The **Admin Area** and **Website** project applications do not posses the ability to be deployed in development and production deployment modes, as it's simply not needed.

</Alert>

<Alert type="info">

  If needed, Webiny can also be deployed into a preexisting [Virtual Private Cloud (VPC)](https://aws.amazon.com/vpc/). For more information, please refer to the [Use Existing Amazon VPC](/docs/{version}/enterprise/use-existing-amazon-vpc) guide.

</Alert>
