---
id: 4738c8e7
title: Introduction
description: Learn what does the Admin Area project application represent.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- what does the **Admin Area** project application represent

</Alert>

## The Admin Area Project Application

The **Admin Area** project application represents, as the name itself suggests, your administration area, which is a simple React single-page-application (SPA).

With only two cloud infrastructure resources, the Amazon CloudFront and Amazon S3, hosting single page applications is simple, and most importantly, cost-effective.
