---
id: aafeab36
title: Overview
description: Learn about the necessary cloud infrastructure resources on which the Website project application relies on.
---

import { <PERSON><PERSON> } from "@/components/Alert";
import websiteOverview from "./assets/overview/webiny_website_overview.png";

<Alert type="success" title="What you’ll learn">

- what does the **Website** project application represent
- what are the common problems when serving a public website
- briefly, how Webiny approaches these problems
- the necessary cloud infrastructure resources on which the **Website** project application relies on

</Alert>

## The Website project application

The **Website** project application represents your public website.

In terms of the code the application holds, essentially, we're talking about a classic React application that renders pages created using the Page Builder application.

In terms of the necessary cloud infrastructure, things are not as simple as it's the case with the [Admin Area](/docs/{version}/architecture/admin/overview) project application. Basically, public websites do not work well if they are served as a classic React single-page-application (SPA) that's relying on the client-side rendering approach.

This is because of the following two factors:

1. UX is not good because every time a users open a webpage, they are presented with a loading screen.
2. SEO suffers significantly. Search engines and web crawlers are still having problems interpreting the dynamically generated HTML, and some are not supporting this at all.

<Alert type="info">

Learn more about different rendering approaches in [this article](https://developers.google.com/web/updates/2019/02/rendering-on-the-web) published by Google.

</Alert>

Because of this, Webiny developed its own **Prerendering Service**, which prerenders your website pages. In other words, when a page is published, it captures the complete HTML code, and relevant data for it in advance. And, when an actual website visitors visits one of your pages, the page is just served as a simple static HTML file, with the page data directly embedded in it. This approach resolves both of the issues mentioned above.

<Alert type="success">

The explanation on prerendering of pages, presented above, is a very brief one. This is intentional, as this key topic is focused on the cloud infrastructure Webiny deploys and not on the prerendering itself. If you want to learn more about how the Page Builder application handles page rendering, visit the [Prerendering Pages](/docs/{version}/architecture/website/prerendering-pages) key topic.

</Alert>

## Diagram

<Alert type="info">

For brevity, the diagram doesn't include network-level cloud infrastructure resources, like region, VPC, availability zones, and so on. Check out the [Deployment Modes](/docs/{version}/architecture/deployment-modes/introduction) section if you're interested in that aspect of the deployed cloud infrastructure.

</Alert>

<Alert type="info">

Note that the Amazon DynamoDB and Amazon OpenSearch databases are deployed as part of the [Core](/docs/{version}/architecture/core/overview) project application. Also, the GraphQL Handler AWS Lambda function is deployed as part of the [API](/docs/{version}/architecture/api/overview) project application

These are still included in the diagram, just so it's more clear to the reader.

</Alert>

<Image
  src={websiteOverview}
  title="Webiny Cloud Infrastructure - Website - Overview"
  shadow={false}
/>

## Description

The diagram gives an overview of the complete cloud infrastructure that's needed to host the **Website** project application and, in combination with the **Core** and **API** project applications, that makes prerendering of pages possible.

We have two Amazon CloudFront distributions <diagram-letter>A</diagram-letter> <diagram-letter>D</diagram-letter>, two Amazon S3 buckets <diagram-letter>B</diagram-letter> <diagram-letter>C</diagram-letter>, and the Webiny Prerendering Service <diagram-letter>E</diagram-letter> that consists of three AWS Lambda functions and an Amazon SQS. We also have the **Core** and **API** project applications, which are also involved in the whole process, which we cover in the upcoming [Prerendering Pages](/docs/{version}/architecture/website/prerendering-pages) section.

<Alert type="info">

To learn more about the **Core** and **API** project applications' cloud infrastructure, check out the [Core](/docs/{version}/architecture/core/overview) and [API](/docs/{version}/architecture/api/overview) sections.

</Alert>
