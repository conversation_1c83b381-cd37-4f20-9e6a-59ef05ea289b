---
id: aafeaa32
title: Introduction
description: An overview of how theming works in Webiny's Page Builder application.
---

import { Alert } from "@/components/Alert";

<!-- Always include this section on top of the page. -->

<Alert type="success" title="What You’ll Learn">

- what are the different parts that make up a theme
- what plugins are used upon creating themes

</Alert>

## Overview

By default, every Webiny project includes the default website theme which defines different visual aspects of your website, for example the default set of colors, typography, the default page layout, and more.

In a new Webiny project, if we were to open the [`extensions/theme`](https://github.com/webiny/webiny-js/tree/v5.40.0/packages/cwp-template-aws/template/common/extensions/theme) folder and in it the [`extensions/theme/src/index.ts`](https://github.com/webiny/webiny-js/tree/v5.40.0/packages/cwp-template-aws/template/common/extensions/theme/src/index.ts) file, we'd see the following:

```ts extensions/theme/src/index.ts
import { PbPageLayoutPlugin } from "@webiny/app-page-builder";
import { FbFormLayoutPlugin } from "@webiny/app-form-builder";
import { ThemePlugin } from "@webiny/app-website";

// The central theme object which defines different visual aspects of your website,
// for example the default set of colors, typography, breakpoints, and more.
import theme from "./theme";

// Default layouts used by Page Builder pages and Form Builder forms.
import StaticLayout from "./layouts/pages/Static";
import DefaultFormLayout from "./layouts/forms/DefaultFormLayout";

// Ultimately, theme and layouts need to be registered via their respective plugins.
// To learn more, see: https://www.webiny.com/docs/page-builder/theming/introduction.
export default () => [
  new ThemePlugin(theme),

  new PbPageLayoutPlugin({
    name: "static",
    title: "Static page",
    component: StaticLayout
  }),

  new FbFormLayoutPlugin({
    name: "default",
    title: "Default form layout",
    component: DefaultFormLayout
  })
];
```

Respectively, via the [`ThemePlugin`](https://github.com/webiny/webiny-js/blob/v5.40.0/packages/app-theme/src/index.ts#L4-L12), [`PbPageLayoutPlugin`](https://github.com/webiny/webiny-js/blob/v5.40.0/packages/app-page-builder/src/plugins/PbPageLayoutPlugin.ts#L4-L12), and [`FbFormLayoutPlugin`](https://github.com/webiny/webiny-js/blob/v5.40.0/packages/app-form-builder/src/plugins/FbFormLayoutPlugin.ts#L4-L12) plugins, the file achieves the following:

1. registers the central theme object
2. registers the **Static page** layout, to be used with pages created with the Page Builder application
3. registers the **Default form layout** layout, to be used with forms created with the Form Builder application

In the following sections, we cover the essential information and best practices related to the theme object and page layouts.

[//]: # "If you're interested in form layouts and styling forms created with the Form Builder application, please visit the separate [Form Builder Theming](/docs/{version}/form-builder/theming/introduction) section of articles."

## How Styling Works

Before we dive into the theme object and page layouts, it's useful to know how styling works inside Webiny.

For styling, Webiny relies on [Emotion](https://emotion.sh/docs/introduction) - a library that provides a set of tools for writing CSS styles with JavaScript. Emotion is a CSS-in-JS library, which means that it allows writing CSS styles directly inside JavaScript code.

As we'll be able to see in the following sections, Emotion is used when defining the theme object and styling page layouts. It is also used upon creating and styling [custom page elements](/docs/{version}/page-builder/extending/create-a-page-element).

More information on all of this will be outlined in the next couple of sections.
