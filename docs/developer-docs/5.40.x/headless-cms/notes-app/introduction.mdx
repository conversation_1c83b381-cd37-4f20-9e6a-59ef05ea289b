---
id:
title: Introduction
description: Learn how to set up a new User Pool in Webiny, enable authentication for it, and restrict users’ access to only their own content. Additionally, we will build a React Notes App where users can sign up to create, read, and delete their own notes.
---

import { Alert } from "@/components/Alert";
import notesReactApp from "./assets/notes-app/notes-react-app.mp4";

<Alert type="info" title="Can I Use This?">

This feature has been available since Webiny **v5.40.0** and is available in the Business & Enterprise tiers.

</Alert>

<Alert type="success" title="What you will learn">

- how to set up a new User Pool in Webiny and enable authentication for it
- how to limit users' access to only the content they've created
- how to use Amplify UI in a React app to build a sign-up and login form
- how to build a notes React application where users can create, read, and delete notes after signing up

</Alert>

## Overview

Imagine an end-user-facing application such as an E-commerce site, Instagram, Twitter (X), or similar, where users can sign up and interact with the app. If you have this kind of requirement, then this guide is for you.

To achieve this functionality in Webiny, we need to create a new User Pool and enable authentication for this pool. In this guide, we will look into the step-by-step instructions on how to make all the necessary changes in your Webiny project. We will also create a React app using the [Amplify UI](https://ui.docs.amplify.aws/react/connected-components/authenticator) library, demonstrating how to create a sign-up and login form. This React app is a notes application, allowing users to sign up and perform operations such as creating, reading, and deleting notes that they have created.

This guide is divided into two parts:
- **[Webiny Infrastructure Setup](/docs/{version}/headless-cms/notes-app/webiny-infrastructure-setup)** - The first part focuses on creating the necessary infrastructure on the Webiny side and limiting users' access to only the content they've created.
- **[Building a React Notes Application with the Amplify UI Library](/docs/{version}/headless-cms/notes-app/react-notes-app)** - The second part covers creating a notes React application using the Amplify UI library.

Here’s a preview of the React application we’ll be building.

<Video src={notesReactApp} controls={true}/>
