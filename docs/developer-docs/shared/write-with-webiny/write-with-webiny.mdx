---
id: aafea9f6
title: Write with Webiny
description: Learn about our program for technical writers
---

**Are you curious about new technology? Do you love the idea of composable content, GraphQL APIs, JAMStack, serverless, open source or the JavaScript & NodeJS ecosystem in general? Do you love writing about them? Why not join our writing community and get paid too!**

The “Write with Webiny” program is a way for you to contribute to the open source movement by writing technical content, and getting paid for doing so. We're looking for content that features technical subjects to help more developers to start building with Webiny and advance their skills.

You can choose between a pool of pre-selected subjects that we would like to cover, or suggest your own original topic that might be valuable to our dev community. You can review the program guidelines for more details and examples of the type of content we're interested in.

## Why you should apply to the program?

- We will compensate you for your time. Depending on the scope and quality of submitted article, we will pay you up to $300.
- We will sign you as the author on all your published articles.
- We will promote your article across the web. Twitter, Reddit, Hacker News, Dev.to etc
- You can reach a wider audience and build a name for yourself.
- You can help other developers by sharing your knowledge and experience.
- You can republish your work on personal blog/website, once it has been published by Webiny.

## What can you write about?

We are looking for detailed tutorials and articles that generally cover these main topic categories:

- Step-by-step tutorials that walk the reader through creating a real-world project using Webiny from start to finish. These can also cover topics related to using Webiny with other relevant programming frameworks, including Next.JS, React, Gatsby, Angular, Vue, Svelte…
- Use cases that cover specific problems or scenarios the developers would need to address using Webiny.
- Articles that illustrate Webiny apps and features, including Headless CMS, Page Builder, multi-tenancy, self-hosting, data privacy etc
- Best practices on building full-stack serverless, JavaScript, and Typescript applications.
- Insights on using Webiny from your own experience.

If there is a topic that you might find relevant for Webiny community, but doesn't fall under these categories, you can still submit your proposal and we'll review it.

## How does it work?

### Step 1. Join our Slack community

All of the main communication between the writers and Webiny team is taking place in the dedicated [#write-for-webiny](https://www.webiny.com/slack/) channel on our [community Slack](https://www.webiny.com/slack/).

Here you can find all the news and updated regarding the program, plus have an easy access to the Webiny team and other community writers in the program.

### Step 2. Register your interest as a writer

If you are joining the program for the first time, please [register as a new writer](https://site.webiny.com/forms/write-with-webiny/) before submitting or picking out any content.

We'll set you up as a new writer, provide all the necessary information that you need to start writing, and answer any questions you may have.

### Step 3. Pick or propose a topic you want to cover

There are two main ways you can submit your interest for writing an article:

1. [**Pick an existing topic that has been requested by our community.**](https://github.com/webiny/write-with-webiny/issues?q=is%3Aissue+is%3Aopen+label%3A%22Looking+for+a+writer%22)
   You can simply browse through the topics that are labeled with "Looking for a writer" label. Find a topic you would like to cover, add a comment to it including a brief abstract a detailed outline.

2. [**Suggest your own topic that you believe might be valuable to our dev community.**](https://github.com/webiny/write-with-webiny/issues/new?assignees=&labels=&template=1-submit-article-proposal.yml&title=%5BSUBMIT%5D)
   Create a new issue using the provided template and submit an outline of the topic you would like to request to be covered.

### Step 4. Topic request review and approval

Once your proposal has been submitted, our team will review the information you provided. Then approve and assign you the article, or come back to you with relevant feedback.

At this stage, we will also agree on the fee, expected timelines and other details of this cooperation.

### Step 5. Review submission

You should review the submission guidelines, [FAQs](https://github.com/webiny/write-with-webiny/blob/main/FAQ.md) and examples of previously published articles before you start working on drafting your article. If you still have any outstanding questions, please reach out to our team through the Slack channel to get further clarity.

### Step 6. Submit your article

Once you have a first draft of your article written, you can submit it to our editorial team for review. If any revisions are needed, our team will provide you the feedback and work with you on getting the draft polished to the versions that is ready for publishing.

### Step 7. Get paid for your work

Once you get the confirmation from the editorial team that your article is finalised and ready to be published, you can submit your invoice and get paid for your work. Please follow the instructions provided to you at the approval stage (step 4).

## Who can apply?

- Anyone that has the knowledge and skills to cover the proposed topics can participate in this writing program.
- We're looking for technical angles, so you will need to be able to write on technical subjects and for tutorials include code samples.
- Tutorials will need to feature integrations with Webiny, so you should be familiar with our products before you enroll on the program.

## FAQ

### Q. Can I submit work that has already been published? I've already written an article on one of your topics.

A. At this time we are only paying for original content not published elsewhere. We will do due diligence to ensure that all submitted content is original and does not violate any copyright.

We're always interested in promoting and sharing content that is beneficial to our community and audience. So if you have an existing article that hasn't already been published, and that you think would be relevant, please contact us to discuss potential content syndication.

### Q. I work for another developer company. Can I submit writing that mentions or promotes our product?

A. We'd love to chat about new partnerships and co-marketing opportunities. Reach out to [<EMAIL>](mailto:<EMAIL>) to discuss the partnership opportunities.

### Q. Can I publish the article to my personal blog after it's published through the program?

A. Yes, you may publish to your personal blog once the article has been published to Webiny blog. You will only need to add a canonical tag or reference where the article originally appeared at the top of your new publication.
