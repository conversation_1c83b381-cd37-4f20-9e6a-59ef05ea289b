---
id: aafea9f3
title: Webiny Telemetry
description: Learn about the Webiny telemetry
---

## Our Products

Webiny CMS is a self-hosted product that runs in the customer’s own cloud infrastructure. Data that is generated and created by the product is processed and stored with the cloud provider.

Webiny Control Panel is a management dashboard that tracks usage and license terms of individual Webiny CMS instances. It is a SAAS product that is hosted on our AWS account.

## Transparency and Improvement

We have created this page to be fully transparent about the information we collect, how we use and store it. We want to add that this data doesn't reveal any personal identifiable information (PII) of you as an individual.

We use the data in an honest way and with a single purpose - to improve Webiny as a product for yourself and the wider community.

## What data do you collect?

We collect these different types of telemetry data:

1. [CMS Product Analysis](#1-product-analytics)
2. [CMS Usage Analytics](#2-usage-analytics)
3. [Users and Environments (via Webiny Control Panel)](#3-users-and-environments)

### 1. Product Analytics

The product analytics are optional; you have control to turn it on or off.

Product analytics help us build a better product by better understanding the usage patterns of Webiny CMS. The product analytics data is captured within the Webiny CMS Admin dashboard, and within the Webiny CLI utility.

| Source                                                        | Purpose                                                                                                                                 | Events                                                                                                                                                                                                                                                                                                                                                        | Data sent / stored                                                                                                                                                                                         | Bindingness          |
| ------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------- |
| [CLI](https://www.npmjs.com/package/@webiny/cli)              | <ul><li>Drive product improvements</li><li> Product usage insights</li><li> Troubleshooting issues in critical workflow steps</li></ul> | <ul><li>Create Webiny project started</li><li>Create Webiny project finished</li><li>Create Webiny project errors (with error messages and stack trace provided)</li><li>Deployment started</li><li>Deployment finished</li><li>Deployment errors (with error messages and stack trace provided)</li><li>Enable telemetry</li><li>Disable telemetry</li></ul> | <ul><li>Event Name</li><li>Timestamp</li><li>Unique project ID (UUID)</li><li>End-user IP Address</li><li>Error messages and stack trace (in the case of errors)</li></ul>                                 | Optional via opt-out |
| [Admin Dashboard](https://www.npmjs.com/package/webiny-admin) | <ul><li>Drive product improvements</li><li>Product usage insights</li></ul>                                                             | <ul><li>Admin App launched</li><li>Custom website domain updated (with domain name provided)</li></ul>                                                                                                                                                                                                                                                        | <ul><li>Event name</li><li>Timestamp</li><li>Product version</li><li>Unique project ID (UUID)</li><li>Admin App URL</li><li>End-user IP Address</li><li>Custom website domain event: Website URL</li></ul> | Optional via opt-out |

### 2. Usage Analytics

Usage analytics track the number of API calls and number of user seats the customer is using. The data is anonymized and tracked as a cumulative number only. Usage analytics are used to ensure the customer is using the product within the agreed terms and conditions defined by the Webiny Enterprise contract. Usage analytics are mandatory and cannot be turned off.

| Source                                                                   | Purpose                                                                                                 | Events                       | Data sent / stored                                                                                                                                                                                                            | Bindingness |
| ------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------- | ---------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| [Webiny CMS API](https://www.npmjs.com/package/@webiny/api-headless-cms) | <ul><li>License management (inc. entitlements)</li><li>Billing</li><li>Product usage insights</li></ul> | <ul><li>Usage data</li></ul> | <ul><li>Customer name</li><li>Number of API calls</li><li>Number of user seats in the CMS</li><li>API calls HTTP status codes</li><li>API calls timestamp</li><li>API calls execution time</li><li>Environment name</li></ul> | Mandatory   |

### 3. Users and Environments

This data is only obtained via Webiny Control Panel.

Webiny Control Panel requires every Webiny Enterprise customer to register for an account. Through Webiny Control Panel the customer can see their usage and ensure it’s within the agree license terms.

Each member of the customer’s development team working on making code changes to Webiny CMS also requires an account to retrieve the appropriate Webiny license for their own personal development environment.

| Source               | Purpose                                                                                                 | Events                       | Data sent / stored                                                                                                                                                                                              | Bindingness |
| -------------------- | ------------------------------------------------------------------------------------------------------- | ---------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| Webiny Control Panel | <ul><li>License management (inc. entitlements)</li><li>Billing</li><li>Product usage insights</li></ul> | <ul><li>Usage data</li></ul> | <ul><li>Customer name</li><li>List of CI/CD environment names</li><li>First name, last name and email of team members with the access to the project (usually members of customer’s engineering team)</li></ul> | Mandatory   |

## Is the Data Stored or Distributed to a 3rd Party?

No, the data is collected by a service running in our own infrastructure and is not collected or distributed to any 3rd parties.

## What About Sensitive Data? (e.g. Secrets)

We do not track anything other than what is specified above.
