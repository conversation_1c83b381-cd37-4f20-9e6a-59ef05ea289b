---
id: aafea9f5
title: Community Plugins
description: Learn how to create or contribute to community plugins.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- What are community plugins
- How to create or contribute to community plugins

</Alert>

<Alert type="info" title="Can I use this?">

In order to follow this guide, you must use Webiny version **5.20.0** or greater.

</Alert>

## Introduction

Community plugins are 3rd party supported plugins for Webiny. These plugins are created and managed by the Webiny community.
Here is the [repository](https://github.com/webiny-contrib/plugins) for community managed plugins.

## Contribute a New Plugin

### 1. Clone the Plugins Repository

All the community-supported 3rd party plugins are maintained at the [plugins repository](https://github.com/webiny-contrib/plugins).  
Clone the plugins repository in the Webiny project root.

`git clone https://github.com/webiny-contrib/plugins`

### 2. Create a React Component Package

We will use the React Component Package scaffold to create the plugin. From your project root, simply run the `yarn webiny scaffold` command:

```bash
yarn webiny scaffold
```

Then, from the list of all available scaffolds, select **React Component Package** and follow the on-screen instructions.

- Enter the name of the component (in pascal-case)  
  e.g. `IFrame`

- Enter the package location (packages/iframe)  
  Based on your plugin type, i.e. whether you are building a plugin for Page Builder or Headless CMS, select the respective directory, e.g. if you are creating an iframe plugin for Page Builder, the location will look something like this
  `plugins/pageBuilder/iframe`

- Enter the package name (@custom-components/iframe)  
  Please follow this pattern for package name `@webiny-contrib/name-of-your-plugin`  
  e.g. `@webiny-contrib/iframe`

### 3. Plugin Implementation

To implement your plugin, based on the plugin you want to create, please refer to the following tutorials:

- [Create a New Page Element for Page Builder](/docs/{version}/page-builder/extending/create-a-page-element)
- [Create a Custom Field Plugin for Headless CMS](/docs/{version}/headless-cms/extending/custom-field-type)

<Alert type="info" title="Important">

Please place your code under the `src` directory e.g. `plugins/pageBuilder/iframe/src`.

</Alert>

### 4. Raise the Pull Request

Once the plugin is ready, please create a pull request to [https://github.com/webiny-contrib/plugins](https://github.com/webiny-contrib/plugins) repository.
Webiny team will take care of publishing the community plugins to `npm` at [@webiny-contrib](https://www.npmjs.com/package/@webiny-contrib/).

## Contribute to Existing Plugins

You can find all the community managed plugins in [this repository](https://github.com/webiny-contrib/plugins), and every plugin has a contribution guide.  
Here is a process to set up and contribute to the [secretText Headless CMS plugin](https://github.com/webiny-contrib/plugins/tree/main/headlessCMS/secretText#how-to-contribute-to-this-plugin).
Please follow the respective contribution guide for contributing to the existing plugins.

<Alert type="info">

If you need additional help or information on community plugins contribution, feel free to contact us over [Slack](https://www.webiny.com/slack).

</Alert>
