---
id: aafea9f2
title: Branching Strategy
description: Learn how Webiny team organizes Github branches and what are our release cycles.
---

import branchingStrategyImage from "./branching-strategy.png";

<WhatYouWillLearn>

- how often do we release new versions of Webiny
- what are the main branches that take part in the release process

</WhatYouWillLearn>

## Overview

At Webiny, we like to release versions relatively often (in 2-3 weeks cycles), so our users get all the new features and bug fixes that we add to the product. However, since Webiny is not a simple library, upgrading between project versions can be cumbersome, and not all users want to do it that often. Sometimes, upgrades require code mods to modify your project files, database migrations get executed, so it's a bit more complex than just pulling in a new NPM package version.

After battling many of these issues over the past year, we've designed a branching strategy and a new release strategy to address some of the issues that were the most painful to manage.

## Release Strategy

We've identified the main pain points, and came up with a process that allows us to still release bug fixes every sprint, but batch new features, and potential project upgrades, into quarterly releases.

With that being said, at the end of every sprint (~3 weeks), we'll be releasing [patch versions](https://semver.org/#summary) (e.g., `5.33.1`), so you get urgent bugs fixed periodically. This will also allow users to skip patch versions, and upgrade to the latest patch version, without going through every patch version one by one. To illustrate: if your project is on `5.33.0`, you'll be able to install `5.33.3` without having to install `5.33.1`, and `5.33.2`.

Features, and potential project upgrades (code mods, data migrations, etc.), will only be released once per quarter (every 3 months), in form of a minor version (e.g., `5.34.0`). This will allow us to dedicate more time to testing features, preparing more robust upgrades, and making sure that when you do upgrade, everything goes smoothly. Our users, on the other hand, will only need to upgrade once per quarter, which is a lot less taxing on the team and time management.

## Branching Strategy

To make this release management process possible, our branches are organized in the following manner:

<Image src={branchingStrategyImage} title={"Branching Strategy"} />

The `dev` branch is set as the main repository branch, and all fixes are pushed to this branch, to be published in the upcoming release. The stable release happens when the `dev` branch is merged into the `stable` branch, which publishes packages using the `latest` tag, to NPM.

The `next` branch is used to accumulate features to be released in the quarterly release. The features that live in this branch are released under the `unstable` tag to NPM, so users have a chance to create an "unstable" version of a project, and provide us with early feedback.

Right before the quarterly release, we merge the `next` branch into the `dev` branch, resolve any potential merge conflicts, and then merge `dev` into `stable` to cut another stable release. This time, `feat` commits will make their way into the [conventional commits](https://www.conventionalcommits.org/en/v1.0.0/) versioning process, and a new minor version will be released.

Notice the green **sync commits**, they're necessary to pull in the files modified by the publishing process into the `dev` branch.

## Conclusion

The strategies described in this article should make releases more predictable, upgrades more reliable, and our users more confident when they do get to upgrading their projects.

If you're interested in test-driving the latest unstable features of Webiny, the ones that live in the `next` branch, read the article about [unstable releases](/docs/release-management/unstable-releases).
