---
id: aafea9f4
title: Unstable Releases
description: Learn how to install and test unstable features.
---

<WhatYouWillLearn>

- how to install the unstable release of Webiny

</WhatYouWillLearn>

## Overview

As described in the [Branching Strategy](/docs/release-management/branching-strategy) article, features that are scheduled to be released in the next quarterly release are published under the `unstable` tag to NPM. If you're interested in taking that release for a spin, follow the instructions below.

## Setup the Unstable Release

<Alert type="danger" title={"READ THIS BEFORE CONTINUING!"}>

An unstable release is exactly that: no guarantees, bleeding edge, potentially broken, and
any API can change before becoming stable.

We highly recommend **against** using this release in
production, as we cannot guarantee that projects created with this release will be upgradeable to
stable releases.

</Alert>

The process of setting up an unstable release is the same as creating a regular Webiny project, described in the [Install Webiny](/docs/{version}/get-started/install-webiny) article. The only difference is in the `create-webiny-project` command. The following is the command you need to use to create an unstable project:

```bash
npx create-webiny-project@unstable my-new-project --tag=unstable
```

From this point on, the process is identical to our [regular project setup](/docs/{version}/get-started/install-webiny#pick-your-database).

## Updating to the latest unstable release

If you already have an unstable project, you can update it to the latest unstable release by running the following command:

```bash
yarn up "@webiny/*@unstable"
```
