---
id: aafea812
title: Render Rich Text Content
description: Render rich text content from Headless CMS in a custom React app.
---

import { Alert } from "@/components/Alert";
import { CanIUseThis } from "@/components/CanIUseThis";
import Image from "next/image";
import overviewLexicalRenderer from "./assets/overview-lexical-renderer.jpeg";

<CanIUseThis since={"5.37.0"} />

<WhatYouWillLearn>
  - how to render rich text content from Headless CMS in a custom React app
</WhatYouWillLearn>

## Overview

One of the commonly used field types in Headless CMS content models is the Rich Text field. This field uses [Lexical Rich Text Editor](https://playground.lexical.dev/) to allow users to quickly create nicely formatted rich text content.

<Image src={overviewLexicalRenderer} title="Render Rich Text Content From Headless CMS" />

In this article we cover the steps to render the Headless CMS rich text content by using our React component
`<RichTextLexicalRenderer/>`, provided by the `@webiny/react-rich-text-lexical-renderer` package.

<Alert type="info">

To learn how to use Headless CMS GraphQL API, make sure to check out the [Using GraphQL API](/docs/{version}/headless-cms/basics/using-graphql-api) key topic.

</Alert>

# Usage

Rendering of rich text content from Headless CMS consists of the following three steps:

1. Install the `@webiny/react-rich-text-lexical-renderer` package.
2. Add Webiny theme styles to your project.
3. Mount the `RichTextLexicalRenderer` component.

### 1. Installation

In your React app, install the following NPM package:

```
npm install --save @webiny/react-rich-text-lexical-renderer
```

Or if you prefer Yarn:

```
yarn add @webiny/react-rich-text-lexical-renderer
```

### 2. Add Webiny Theme Styles To Your Project

To use the same theme styles that are configured in your Webiny project, copy the `apps/theme/theme.ts` file into your React app.

### 3. Mount the component and Render the CMS Content

Now you need to import and mount the `<RichTextLexicalRenderer />` component in your app, and pass the rich text content, and optionally theme styles.

<Alert type="warning">

Note that in this code sample we do not include the code to load the data from the API. Instead,
we use the imaginary `useCmsContent()` hook to show where the content _could_ come from.

</Alert>

```tsx HomePage.tsx
import React from "react";
import { RichTextLexicalRenderer } from "@webiny/react-rich-text-lexical-renderer";
import { useCmsContent } from "../myWebinyGraphqlHooks/useCmsContent";
import { theme } from "../myWebinyTheme/theme";

export const HomePage = () => {
  // Fetch rich text content from the Headless CMS GraphQLQ API.
  const { content } = useCmsContent();

  return <RichTextLexicalRenderer value={content} theme={theme} />;
};
```
