---
id: aafea80f
title: Customize Entry Editor Actions
description: Learn how to add, replace, or remove actions in the Entry Editor.
---

import hcmsActions from "./assets/hcms-actions.png";
import hcmsActionsButton from "./assets/hcms-actions-button.gif";
import hcmsActionsButtonDiscovery from "./assets/hcms-actions-button-discovery.png";
import hcmsActionsButtonBefore from "./assets/hcms-actions-button-before.png";
import hcmsActionsMenuItem from "./assets/hcms-actions-menu-item.gif";
import hcmsActionsMenuItemDiscovery from "./assets/hcms-actions-menu-item-discovery.png";
import hcmsActionsMenuItemBefore from "./assets/hcms-actions-menu-item-before.png";

import { Alert } from "@/components/Alert";

<Alert type="info" title="Can I Use This?">

This feature is available since Webiny **v5.37.0**.

</Alert>

<Alert type="success" title="What you'll learn">

- how to add a custom action to the Entry Editor
- how to discover existing action names
- how to change the position of actions, remove, or replace an existing action

</Alert>

## Overview

By default, Headless CMS offers built-in actions that handle entry saving, publishing and deletion. These are located at the top of the content entry form.

To customize actions, you need to use the `ContentEntryEditorConfig` component. For this article, we will use `ButtonAction` and `MenuItemAction` components in the `Actions` namespace.

As you can see, there are two types of actions:

1. `ButtonAction` listed as a button;
2. `MenuItemAction` listed in the top right corner menu.

<Image src={hcmsActions} title="Overview built-in actions" />

## Button Actions

### Add a Button

To add a new button, use the `ContentEntryEditorConfig` component and mount it within your `Admin` app. Your button is just a regular React element, so it can render anything you want.

To help developers keep the UI consistent, you'll find a handy `useButtons` hook that exposes four button components:

- `ButtonDefault`
- `ButtonPrimary`
- `ButtonSecondary`
- `IconButton`

Use one of these components to render a button that inherits UI rules and guidelines from the Webiny core package.

```diff-tsx apps/admin/src/App.tsx
import React from "react";
import { Admin } from "@webiny/app-serverless-cms";
import { Cognito } from "@webiny/app-admin-users-cognito";
+ import { ContentEntryEditorConfig, useContentEntry } from "@webiny/app-headless-cms";
+ import { useSnackbar } from "@webiny/app-admin";
import "./App.scss";

+ // You can destructure child components to make the code more readable and easier to work with.
+ const { Actions } = ContentEntryEditorConfig;
+ const { useButtons } = Actions.ButtonAction;

+ const ButtonCopyJson = () => {
+   // useButtons() exposes the button components also used internally: use these to keep the UI consistent.
+   const { ButtonPrimary } = useButtons();
+   // useContentEntry() provides helpful details about the currently selected entry.
+   const { entry } = useContentEntry();
+   // showSnackbar allows to provide a feedback to users.
+   const { showSnackbar } = useSnackbar();
+
+   const copyJson = () => {
+       navigator.clipboard.writeText(JSON.stringify(entry, null, 2));
+       showSnackbar("JSON data copied to clipboard.");
+   };
+
+   return <ButtonPrimary onAction={copyJson}>Copy JSON data</ButtonPrimary>;
+ }

export const App = () => {
    return (
      <Admin>
        <Cognito />
+       <ContentEntryEditorConfig>
+         <Actions.ButtonAction
+           name={"copy-json"}
+           element={<ButtonCopyJson />}
+           modelIds={["car"]}
+         />
+       </ContentEntryEditorConfig>
      </Admin>
    );
};
```

By declaring the `modelIds` prop, you can define in which Content Model Entry Editor you want to show your custom button. If you exclude this prop, the button will be registered for all models in the system.

In the example above, you'll see your new element the next time you open a `car` Content Model Entry Editor. This is the whole process of registering a new button element.

<Image src={hcmsActionsButton} title="Custom Button in Action" />

### Discovering Buttons

This section demonstrates how you can discover the names of existing buttons. This is important for further sections on positioning, removing, and replacing buttons.

The easiest way to discover existing buttons is to use your browser's React Dev Tools plugins and look for the `Buttons` element. From there, you can either look for `actions` props or look at the child elements and their keys:

<Image src={hcmsActionsButtonDiscovery} title="Discover Existing Buttons" />

### Positioning a Button

To position your custom button before or after an existing button, you can use the `before` and `after` props on the `<ButtonAction>` element:

```tsx
<ContentEntryEditorConfig>
  <Actions.ButtonAction name={"copy-json"} element={<ButtonCopyJson />} before={"save"} />
</ContentEntryEditorConfig>
```

<Image src={hcmsActionsButtonBefore} title="Position Your Button Before Another Button" />

### Removing a Button

You may want to remove an existing button. All you need to do is reference the button by name and pass a `remove` prop to the `<ButtonAction>` element:

```tsx
<ContentEntryEditorConfig>
  <Actions.ButtonAction name={"save"} remove />
</ContentEntryEditorConfig>
```

### Replacing a Button

To replace an existing button with a new button element, you need to reference the existing button by name and pass a new element via the `element` prop:

```tsx
<ContentEntryEditorConfig>
  <Actions.ButtonAction name={"save"} element={<button>A new button!</button>} />
</ContentEntryEditorConfig>
```

## Menu Item Actions

### Add a Menu Item

To add a new menu item, use the `ContentEntryEditorConfig` component and mount it within your `Admin` app. Your menu item is just a regular React element, so it can render anything you want.

To help developers keep the UI consistent, you'll find a handy `useOptionsMenuItem` hook that exposes the `OptionsMenuItem` component, inheriting UI rules and guidelines from the Webiny core package.

```diff-tsx apps/admin/src/App.tsx
import React from "react";
import { Admin } from "@webiny/app-serverless-cms";
import { Cognito } from "@webiny/app-admin-users-cognito";
+ import { ReactComponent as CopyIcon } from "@material-design-icons/svg/filled/content_copy.svg";
+ import { ContentEntryEditorConfig, useContentEntry } from "@webiny/app-headless-cms";
import "./App.scss";

+ // You can destructure child components to make the code more readable and easier to work with.
+ const { Actions } = ContentEntryEditorConfig;
+ const { useOptionsMenuItem } = Actions.MenuItemAction;

+ const MenuItemCopyJson = () => {
+   // useOptionsMenuItem() exposes the menu item component, also used internally: use this to keep the UI consistent.
+   const { OptionsMenuItem } = useOptionsMenuItem();
+   // useContentEntry() provides helpful details about the currently selected entry.
+   const { entry } = useContentEntry();
+   // showSnackbar allows to provide a feedback to users.
+   const { showSnackbar } = useSnackbar();
+
+   const copyJson = () => {
+       navigator.clipboard.writeText(JSON.stringify(entry, null, 2));
+       showSnackbar("JSON data copied to clipboard.");
+   };
+
+   return (
+       <OptionsMenuItem
+           onAction={copyJson}
+           label={"Copy JSON data"}
+           icon={<CopyIcon />}
+       />
+   );
+ }

export const App = () => {
    return (
      <Admin>
       <Cognito />
+      <ContentEntryEditorConfig>
+        <Actions.MenuItemAction
+          name={"copy-json"}
+          element={<MenuItemCopyJson />}
+          modelIds={["car"]}
+        />
+      </ContentEntryEditorConfig>
      </Admin>
    );
};
```

By declaring the `modelIds` prop, you can define in which Content Model Entry Editor you want to show your custom menu item. If you exclude this prop, the menu item will be registered for all models in the system.

In the example above, you'll see your new element the next time you open a `car` Content Model Entry Editor. This is the whole process of registering a new menu item element.

<Image src={hcmsActionsMenuItem} title="Custom Menu Item in Action" />

### Discovering MenuItems

This section demonstrates how you can discover the names of existing menu items. This is important for further sections on positioning, removing, and replacing menu items.

The easiest way to discover existing menu items is to use the React Dev Tools plugins for your browser, and look for the `OptionsMenu` element. From there, you can either look for `actions` props or look at the child elements and their keys:

<Image src={hcmsActionsMenuItemDiscovery} title="Discover Existing Menu Items" />

### Positioning a MenuItem

To position your custom menu item before or after an existing menu item, you can use the `before` and `after` props on the `<Action.MenuItemAction>` element:

```tsx
<ContentEntryEditorConfig>
  <Actions.MenuItemAction name={"copy-json"} element={<MenuItemCopyJson />} before={"delete"} />
</ContentEntryEditorConfig>
```

<Image src={hcmsActionsMenuItemBefore} title="Position Your Menu Item Before Another Menu Item" />

### Removing a MenuItem

You may want to remove an existing menu item. All you need to do is reference the menu item by name and pass a `remove` prop to the `<Action.MenuItemAction>` element:

```tsx
<ContentEntryEditorConfig>
  <Actions.MenuItemAction name={"delete"} remove />
</ContentEntryEditorConfig>
```

### Replacing a MenuItem

To replace an existing menu item with a new menu item element, you need to reference the existing menu item by name and pass a new component via the `element` prop:

```tsx
<ContentEntryEditorConfig>
  <Actions.MenuItemAction name={"delete"} element={<span>A new menu item!</span>} />
</ContentEntryEditorConfig>
```
