---
id: aafea50a
title: Teams
description: Learn how to use the Teams and how enterprise organizations can benefit from it.
---

import aaclCustomAccess from "./assets/aacl-custom-access.png";
import aaclCustomAccessUpgrade from "./assets/aacl-custom-access-upgrade.png";
import aaclTeams from "./assets/aacl-teams.png";
import aaclTeamsMenu from "./assets/aacl-teams-menu.png";
import aaclTeamsAutocomplete from "./assets/aacl-teams-autocomplete.png";
import usersRoles from "./assets/users-roles.png";
import usersTeamsRoles from "./assets/users-teams-roles.png";

<CanIUseThis enterprise={true} since={"5.37.0"} />

<WhatYouWillLearn>

- what are the three tiers of Webiny's security layer
- an overview of the features the Teams feature provides and how to use it
- how the enable Teams

</WhatYouWillLearn>

## The Three Tiers of Webiny's Security Layer

Webiny's security layer divided into three tiers. The higher the tier, the more features are available.

All Webiny project start with the **Open Source** tier. The tier is free to use, but is limited when it comes to defining fine-grained permissions, allowing only the **No Access** and **Full Access** to be selected when defining permissions for individual Webiny apps.

Trying to select **Custom Access** will result in an alert message being shown, informing the user that the feature is only available with the Advanced Access Control Layer (AACL), which is available on the **Business** and **Enterprise** tiers.

<Image src={aaclCustomAccessUpgrade} title={"Selecting Custom Access Level on Open Source Tier"} />

To upgrade to **Business** tier, users [link their project](/docs/wcp/link-a-project) with [Webiny Control Panel (WCP)](/docs/wcp/overview), from where they can activate the Advanced Access Control Layer (AACL) for their project. By doing this, users will be able to define fine-grained permissions for individual Webiny apps.

<Image
  src={aaclCustomAccess}
  title={"Selecting Custom Access with Advanced Access Control Layer (AACL) Enabled"}
/>

Finally, for the most advanced use cases, users can upgrade to the **Enterprise** tier. On top of the features available with the first two tiers, the Enterprise tier introduces **Teams**, which essentially enables users to assign users into teams and easily have them linked with one or more roles.

More on this in the following section.

## Overview

With the Open Source and Business tiers, admin users can only be linked with a single security role.

<Image src={usersRoles} title={"Roles Assigned To Users"} shadow={false} />

This means that if you want to have a user with multiple roles, you need to manually create a new role that combines the permissions of the two roles you want to combine.

And although this approach might work for some users, it can quickly become cumbersome to manage. This is where **Teams** comes in. With it, users can be assigned into a team, where each team can be linked with one or more roles.

<Image src={usersTeamsRoles} title={"Users Assigned to Teams"} shadow={false} />

This feature is especially useful for larger organizations, where it's common to have multiple teams working on different projects. Also, it's a great way to simplify the process of managing permissions for multiple users, as you can simply assign a role to a team, instead of assigning it to each individual user.

## Enabling Teams and Feature Overview

For Webiny Enterprise users, apart from [linking their Webiny project](/docs/wcp/link-a-project) with Webiny Control Panel (WCP), there are no additional steps required to enable Teams.

Once linked, Teams will be automatically enabled and the module can be accessed from the main menu:

<Image src={aaclTeamsMenu} title={"Teams Available From the Main Menu"} />

Via the Teams module, users can create new teams that consist of one or more security roles.

<Image src={aaclTeams} title={"Creating a New Team"} />

Once a team is created, users can assign it to one or more users. This can be done by editing the user and selecting the team via the Team field.

<Image src={aaclTeamsAutocomplete} title={"Assigning Admin Users To Teams"} />

## FAQ

### Can I Assign Multiple Security Roles To a User Without The Teams Enabled ?

No, you can't assign multiple security roles to a user without the Teams enabled.

### Can I Assign Multiple Roles To a Team?

Yes, you can assign multiple roles to a team.

### Can I Assign Users To Multiple Teams?

No, users can only be assigned to a single team.

### Can I use Teams with the Open Source or Business tier?

No. Teams are only available with the Enterprise tier.
