---
id: aafea819
title: Cognito Federation
description: Learn how to configure Cognito Federation to sign into the Admin app.
---

import { CanIUseThis } from "@/components/CanIUseThis";
import { Image } from "@/components/Image";
import { WhatYouWillLearn } from "@/components/WhatYouWillLearn";
import cognitoGoogle from "./assets/cognito-google.png";
import cognitoOutput from "./assets/core-output.png";

<CanIUseThis since={"5.37.6"} enterprise={true} />

<WhatYouWillLearn>

- how to configure federated identity providers in the Admin app
- what are the prerequisites necessary to enable federated providers

</WhatYouWillLearn>

## Overview

Out of the box, Webiny comes with an AWS Cognito setup to manage its admin users. There's a preconfigured Cognito User Pool dedicated to admin users. This user pool doesn't allow external users to sign-up, which means that new admin user accounts can only be created by a Webiny admin, from within the Admin app.

In this article, we show the process of configuring your Webiny project to use one or more public identity providers (Google, Facebook, Apple, Amazon).

<Alert type="warning">

Custom `OIDC` providers are not supported at this point.

</Alert>

<Image src={cognitoGoogle} title={"Using Google IdP to Sign Into the Admin App"} />

## Prerequisites

In this article, we're using Google as the identity provider of choice. To set up a Google Cloud project and obtain the `client_id` and `client_secret`, [follow the official documentation](https://docs.aws.amazon.com/cognito/latest/developerguide/cognito-user-pools-social-idp.html). In particular, see the **To register an app with Google** section.

<Alert type="warning" title={"Update Your AWS Permissions!"}>

If your project was created before _v5.37.6_, and you used our [AWS Cloudformation template](https://webiny-public.s3.us-east-2.amazonaws.com/cloudformation/DEPLOY_WEBINY_PROJECT_CF_TEMPLATE.yaml) to setup your deployment user, make sure you update the permissions using the template.

</Alert>

## Configure AWS Infrastructure

Cognito resources are part of the Core app, which is responsible for stateful resources (databases, user pools, S3 buckets, etc.). To configure Cognito to use federated identity providers, we need to modify the `webiny.application.ts` file of the `core` app. Webiny provides a utility to facilitate the configuration of infrastructure resources involved.

Once you have your `client_id` and `client_secret` ready, you can store them in your project's `.env` file, like so:

```dotenv .env
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

```ts apps/core/webiny.application.ts
import { createCoreApp, configureAdminCognitoFederation } from "@webiny/serverless-cms-aws";

export default createCoreApp({
  pulumiResourceNamePrefix: "wby-",
  pulumi(app) {
    configureAdminCognitoFederation(app, {
      // Provide a name for the user pool domain.
      domain: "webiny-admin",
      // Whitelist callback URLs (can be localhost or real domain).
      callbackUrls: ["http://localhost:3001"],
      // Configure external identity providers.
      identityProviders: [
        {
          type: "google",
          providerDetails: {
            authorize_scopes: "email profile openid",
            client_id: String(process.env.GOOGLE_CLIENT_ID),
            client_secret: String(process.env.GOOGLE_CLIENT_SECRET)
          }
        }
      ]
    });
  }
});
```

<Alert type={"info"} title={"Multiple Identity Providers"}>

You can add multiple identity providers to your Cognito user pool. Simply add more identity provider configs to the `identityProviders` array.

</Alert>

<Alert type="warning" title={"Real Callback URLs"}>

Note that for development purposes, it's enough to have just the `localhost` defined in the `callbackUrls`. This article assumes that you'll be researching this feature in a development environment, on localhost.

However, once you deploy your Admin app, and want to use the real Cloudfront URL, or a custom domain, you'll need to go back to this config, and update the `callbackUrls` with the live URL. You'll also need to redeploy your `core` app for your config to apply.

</Alert>

Once the config of your identity provider is in place, you need to deploy your `core` app, to apply the new infrastructure configuration:

```shell
yarn webiny deploy core --env=dev
```

Once deployed, you need to whitelist your Cognito domain in your Google OAuth 2.0 Client ID, as described in the [official documentation](https://docs.aws.amazon.com/cognito/latest/developerguide/cognito-user-pools-social-idp.html) (see points **13-14** in the **To register an app with Google** section).

To get the domain name, copy the value of `cognitoUserPoolDomain` output from the following command:

```shell
yarn webiny output core --env=dev
```

<Image src={cognitoOutput} title={"Core App Output"} />

## Configure GraphQL API

Cognito will be handling the communication with the external identity providers, and creating the `idToken` which is then sent to the API from the Admin app. This means that the authentication process remains the same as with regular Cognito users. However, external user profiles do not exist in the Admin app, and cannot be mapped to a security group or a team using their profile.

To help Webiny map your external users to specific user groups, we need to add the group information to the identity object , generated in the authentication process.

Open your `apps/api/graphql/src/security.ts` file, and update the existing `cognitoAuthentication` configuration:

```ts apps/api/graphql/src/security.ts
// ...
cognitoAuthentication({
  region: String(process.env.COGNITO_REGION),
  userPoolId: String(process.env.COGNITO_USER_POOL_ID),
  identityType: "admin",
  getIdentity({ identity, token }) {
    const federatedIdentity = Boolean(token.identities);

    return {
      ...identity,
      group: federatedIdentity ? "full-access" : undefined
    };
  }
});
// ...
```

In this basic example, we're checking if the token contains the `identities` claim, which is only present on tokens of identities coming from external IdPs. If so, we map those identities to a `full-access` user group.

`group` is an identity attribute which Webiny uses to map the identity to the specified user group. This gives identities their permissions, which are then used in the authorization process. If this attribute is not set (or is `undefined`), Webiny will attempt to load the user group using a user profile, which exist only for users created via the Admin app.

With this approach, we can manage group mapping programmatically, and decide where the mapping information is coming from. We might fetch the group slug from the `idToken` claim (which can be managed outside Webiny, within Cognito, or within the external identity provider), or we can use a hardcoded map (as we do in this example, using the `full-access` group slug).

Once the config is in place, you need to deploy your `api` app:

```shell
yarn webiny deploy api --env=dev
```

## Configure Admin App

We need to update the default Cognito configuration in the Admin app, to enable sign in via Google:

```tsx apps/admin/src/App.tsx
import React from "react";
import { Admin } from "@webiny/app-serverless-cms";
import { Cognito, CreateAuthenticationConfig } from "@webiny/app-admin-users-cognito";
import "./App.scss";

const cognitoConfig: CreateAuthenticationConfig = {
  oauth: {
    domain: String(process.env.REACT_APP_USER_POOL_DOMAIN),
    redirectSignIn: "http://localhost:3001",
    redirectSignOut: "http://localhost:3001",
    scope: ["profile", "email", "openid"],
    responseType: "token"
  },
  federatedProviders: ["Google"]
};

export const App = () => {
  return (
    <Admin>
      <Cognito config={cognitoConfig} />
    </Admin>
  );
};
```

<Alert type="warning" title={"Real Callback URLs"}>

Notice the use of `localhost` redirect URLs. In your staging and production environments, you'll
need to update these values to reflect the domains of your deployed systems.

</Alert>

And lastly, we need to get the value of `REACT_APP_USER_POOL_DOMAIN` environment variable in the code above. Its value is the fully qualified Cognito User Pool domain, which lives in the Core app. It looks something like this: `webiny-admin.auth.us-east-1.amazoncognito.com`, and is generated once the User Pool Domain is created in your AWS infrastructure.

To pull this value from the Core app, and inject it into the Admin app, there's a utility provided by Webiny. Update your `apps/admin/webiny.config.ts` file like so:

```ts apps/admin/webiny.config.ts
import {
  createAdminAppConfig,
  configureAdminCognitoUserPoolDomain
} from "@webiny/serverless-cms-aws";

export default createAdminAppConfig(modifier => {
  configureAdminCognitoUserPoolDomain(modifier);
});
```

To run and test the new identity provider, start your Admin app:

```shell
yarn webiny watch admin --env=dev
```

Once your app is up and running, you should see the following login screen:

<Image src={cognitoGoogle} title={"Using Google IdP to Sign Into the Admin App"} />
