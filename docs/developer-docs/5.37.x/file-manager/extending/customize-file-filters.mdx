---
id: aafea816
title: Customize File Filters
description: Learn how to add, replace, or remove filters in the File Manager.
---

import { Alert } from "@/components/Alert";
import { CanIUseThis } from "@/components/CanIUseThis";
import { WhatYouWillLearn } from "@/components/WhatYouWillLearn";
import fmFilters from "./assets/fm-filters.png";
import fmToggleFilter from "./assets/fm-toggle-filter.gif";
import fmDropdownFilter from "./assets/fm-dropdown-filter.gif";
import fmPositioBefore from "./assets/fm-position-before.png";
import fmDiscoverFilters from "./assets/fm-discover-filters.png";

<CanIUseThis since={"5.37.0"} />

<WhatYouWillLearn>

- how to add a custom filter to the File Manager
- how to discover existing filter names
- how to change the position of filters, remove, or replace an existing filter

</WhatYouWillLearn>

## Overview

By default, File Manager offers a single built-in filter, to help you quickly filter files based on their type (images, documents, videos). To customize filters, you need to use the `FileManagerViewConfig` component. For this article, we will use the `Filter` component, located in the `Browser` namespace.

Browser is the main area of the File Manager, where users browse files, apply filters, perform searching, organize files into folders, upload new files, etc.

<Image src={fmFilters} title="Browser and the built-in filter by type" />

## Add a Filter

To add a new filter, you need to use the `FileManagerViewConfig` component and mount it within your `Admin` app. Your filter is just a regular React element, so it can render anything.

```diff-tsx apps/admin/src/App.tsx
import React from "react";
import { Admin } from "@webiny/app-serverless-cms";
import { Cognito } from "@webiny/app-admin-users-cognito";
+ import { FileManagerViewConfig } from "@webiny/app-file-manager";
import "./App.scss";

+ // You can destructure child components to make the code more readable and easier to work with.
+ const { Browser } = FileManagerViewConfig;

+ const DemoFilter = () => {
+   return <span>Demo Filter</span>;
+ }

export const App = () => {
    return (
      <Admin>
        <Cognito />
+       <FileManagerViewConfig>
+         <Browser.Filter name={"new-filter"} element={<DemoFilter />} />
+       </FileManagerViewConfig>
      </Admin>
    );
};
```

Next time you open your File Manager, and expand the filters bar, you'll see your new element there. This is the whole process of registering a new filter element. In the following sections, we'll focus on implementing actual filtering.

### Simple Filter

The following example shows how to implement a filter that toggles between "all files" and "large files". For this example, we treat all files larger than 200KB as "large files". Note that we are only showing the actual filter implementation. To see the filter registration
process, revisit the previous section.

```tsx apps/admin/src/App.tsx
import { useBind } from "@webiny/form";
import { ButtonSecondary } from "@webiny/ui/Button";

const DemoFilter = () => {
  const bind = useBind({
    name: "size_gt"
  });

  const toggleFilter = () => {
    bind.onChange(bind.value ? undefined : 204800);
  };

  return (
    <ButtonSecondary onClick={toggleFilter}>
      {bind.value ? "Show All Files" : "Show Large Files"}
    </ButtonSecondary>
  );
};
```

Under the hood, filters are handled via a regular form component. This means that we can simply hook into the form to set new filter values. For that, we're using the `useBind` hook, and give it a name. This `name` will become a key within the form data object, which will in turn be passed to the GraphQL query. To unset a filter, we simply set `undefined`. On button click, we toggle between the 2 values, and also show a label that corresponds to the current state of the filter.

<Image src={fmToggleFilter} title="Toggle Filter in Action" />

### Advanced Filter

Sometimes, you need to implement filtering logic that is a bit more complex than just setting a single value. The following example shows a filter in form of a dropdown menu, where each option needs to use a different GraphQL query operator. `> 200KB` needs to use the `size_gt` key, while `< 200KB` needs to use the `size_lt` key. This cannot be achieved with simple form binding.

To correctly format our filters, we'll use the `Browser.FiltersToWhere` component to register our custom converter, which will allow you to convert form data to a valid GraphQL query input. With this approach, you have full control over values that will be passed to the API, when the user interacts with the filters UI.

```tsx apps/admin/src/App.tsx
import { useBind } from "@webiny/form";
import { Select } from "@webiny/ui/Select";

export const FilterBySize = () => {
  const bind = useBind({
    name: "size",
    defaultValue: "all",
    beforeChange(value, cb) {
      cb(value === "all" ? undefined : value);
    }
  });

  const options = [
    { label: "All", value: "all" },
    { label: "> 200KB", value: "gt:204800" },
    { label: "< 200KB", value: "lt:204800" }
  ];

  return <Select {...bind} placeholder={"Filter by size"} options={options} size="medium" />;
};

const convertFilters = ({ size, ...rest }: any) => {
  if (!size) {
    return rest;
  }

  const [operator, value] = size.split(":");
  return { ...rest, [`size_${operator}`]: parseInt(value) };
};

/* Register your custom filter, and the filters converter. */
<FileManagerViewConfig>
  <Browser.Filter name={"size"} element={<FilterBySize />} />
  <Browser.FiltersToWhere converter={convertFilters} />
</FileManagerViewConfig>;
```

<Image src={fmDropdownFilter} title="Dropdown Filter in Action" />

<Alert type={"info"} title={"Multiple Converters"}>
  You can add as many converters as you need. The output of the previous converter will be passed as
  an input to the next converter.
</Alert>

## Discover Filters

This section demonstrates how you can discover the names of existing filters. This is important for further sections on positioning, removing, and replacing filters.

The easiest way to discover existing filters is to use the React Dev Tools plugins for your browser, and look for the `Filters` element. From there, you can either look for `filters` props, or look at the child elements and their keys:

<Image src={fmDiscoverFilters} title="Discover Existing Filters" />

## Position a Filter

To position your custom filter before or after an existing filter, you can use the `before` and `after` props on the `<Filter>` element:

```tsx apps/admin/src/App.tsx
<FileManagerViewConfig>
  <Browser.Filter name={"size"} element={<FilterBySize />} before={"type"} />
</FileManagerViewConfig>
```

<Image src={fmPositioBefore} title="Position Your Filter Before Another Filter" />

## Remove a Filter

Sometimes you might want to remove an existing filter. All you need to do is reference the filter by name, and pass a `remove` prop to the `<Filter>` element:

```tsx apps/admin/src/App.tsx
<FileManagerViewConfig>
  <Browser.Filter name={"type"} remove />
</FileManagerViewConfig>
```

## Replace a Filter

To replace an existing filter with a new filter element, you need to reference the existing filter by name, and pass a new element via the `element` prop:

```tsx apps/admin/src/App.tsx
<FileManagerViewConfig>
  <Browser.Filter name={"type"} element={<span>A new filter!</span>} />
</FileManagerViewConfig>
```
