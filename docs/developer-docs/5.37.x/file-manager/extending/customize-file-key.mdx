---
id: aafea818
title: Customize File Key
description: Learn how to customize file key before upload to S3
---

import { Alert } from "@/components/Alert";
import { CanIUseThis } from "@/components/CanIUseThis";
import { WhatYouWillLearn } from "@/components/WhatYouWillLearn";
import fmCustomFileKey from "./assets/fm-custom-file-key.png";

<CanIUseThis since={"5.37.0"} />

<WhatYouWillLearn>

- how to customize file key before it is uploaded to the S3 bucket

</WhatYouWillLearn>

## Overview

File Manager allows you to control the file key that is used to store your file to the S3 bucket. Note that file `name` and file `key` are different things. A `name` is what a user sees in the File Manager UI, and is managed by Webiny. A `key` is used by the underlying file storage (in our case, AWS S3). By default, Webiny generates a file key from the file name, and applies some basic sanitization, to form a valid S3 file key.

However, sometimes, you need to have a very specific structure of a file key, because you might have other AWS services hooked up to S3 events, and they will extract information from the key, etc. This articles demonstrates the process of file key customization, so you're not limited by the built-in defaults.

## Customize File Key

To hook into the file upload process, use the `createFileUploadModifier` plugin factory, provided by the `@webiny/api-file-manager-s3` package.

<Alert type="warning">
  The source code below only highlights the changes you need to make to your project file. The rest
  of your file contents is not shown for better clarity.
</Alert>

```diff-ts apps/api/graphql/src/index.ts
+ import fileManagerS3, { createFileUploadModifier } from "@webiny/api-file-manager-s3";

export const handler = createHandler({
    plugins: [
        // Other plugins were omitted for clarity.

        // Add the following code after your existing plugins.
+       createFileUploadModifier(({ modifier }) => {
+           modifier.setFile(file => {
+               if (file.key.toLowerCase().endsWith("mp4")) {
+                   return { ...file, key: `my-custom-key-${Date.now()}.mp4` };
+               }
+
+               return file;
+           });
+       })
    ],
    http: { debug }
});
```

In this example, we check if the file extension is `mp4`, and generate a custom file key. You can also change the file name, by returning a new `name` property, alongside the `key` property.

Once the code shown above is applied, you need to deploy your API. Make sure to use the `--env` value that corresponds to your development environment.

```shell
yarn webiny deploy api --env=dev
```

After the deployment is done, try uploading an `mp4` file, and you'll see a custom key being applied to the file.

<Image src={fmCustomFileKey} title={"Custom File Key Applied to Mp4 Files"} />
