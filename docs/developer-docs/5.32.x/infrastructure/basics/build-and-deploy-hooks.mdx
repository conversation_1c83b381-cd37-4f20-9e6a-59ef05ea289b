---
id: aafeaa3e
title: Build and Deploy Hooks
description: Learn how to hook onto key lifecycle events of the "webiny deploy" commands.
---

import { Alert } from "@/components/Alert";
import deployPhases from "./assets/build-and-deploy-hooks/webiny_deploy_phases.png";

<Alert type="info" title="Can I Use This?">

Build-related hooks outlined in this guide are available since Webiny **v5.20.0**, while deploy-related ones are available already since Webiny **v5.0.0**.

</Alert>

<Alert type="success" title="What you’ll learn">

- how to run custom code before and after build and deploy steps

</Alert>

## Introduction

Internally, the [`webiny deploy`](/docs/{version}/core-development-concepts/basics/project-deployment) command consists of two steps:

1. **build** - within this step, your application code gets compiled and is made ready for deployment
2. **deploy** - within this step, the compiled code, along with the defined cloud infrastructure resources gets deployed

<Alert type="info">

To deploy necessary cloud infrastructure, by default, Webiny relies on Pulumi, a modern infrastructure as code framework. Find out more in the following [IaC with Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi) key topic.

</Alert>

In some cases, it might be necessary to run some code before or after build or deployment. For example, you may want to add some additional logging or validation.

This is why the [`webiny deploy`](/docs/{version}/core-development-concepts/basics/project-deployment) command enables hooking onto a couple of its key lifecycle events:

1. pre-build
2. post-build
3. pre-deploy
4. post-deploy

The following diagram shows in which order these lifecycle events are triggered:

<Image src={deployPhases} title="webiny deploy Command's Lifecycle Events" shadow={false} />

We can hook onto any of these events via a dedicated plugin type, which we cover in the following section.

## Creating a Hook

The following code demonstrates all of the plugin types we can use in order to hook onto the command's key lifecycle events. We are defining them within the [`cli.plugins`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/common/webiny.project.ts#L23) section of the object exported from the root [`webiny.project.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/common/webiny.project.ts) file.

```ts webiny.project.ts
// Some of the code is removed for brevity.

export default {
  name: "your-project",
  cli: {
    plugins: [
      // ... all other plugins go here
      {
        type: "hook-before-build",
        name: "my-custom-hook-before-build",
        async hook(args, context) {
          // Gets executed before the build (application code compilation) step.
        }
      },
      {
        type: "hook-after-build",
        name: "my-custom-hook-after-build",
        async hook(args, context) {
          // Gets executed after the build (application code compilation) step.
        }
      },
      {
        type: "hook-before-deploy",
        name: "my-custom-hook-before-deploy",
        async hook(args, context) {
          // Gets executed before the deployment of the compiled code
          // and other necessary cloud infrastructure resources.
        }
      },
      {
        type: "hook-after-deploy",
        name: "my-custom-hook-after-deploy",
        async hook(args, context) {
          // Gets executed after the deployment of the compiled code
          // and other necessary cloud infrastructure resources.
        }
      }
    ]
  }
};
```

## Watch Command Hooks

Except for the [`webiny deploy`](/docs/{version}/core-development-concepts/basics/project-deployment) command, hooking onto key lifecycle events is also supported by the [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) command.

At the moment, via the `hook-before-watch` lifecycle event, we can perform additional steps once the command has been initialized, which is demonstrated by the following code:

```ts webiny.project.ts
// Some code is removed for brevity.

export default {
  name: "your-project",
  cli: {
    plugins: [
      // ... all other plugins go here
      {
        type: "hook-before-watch",
        name: "my-custom-hook-before-watch",
        async hook(args, context) {
          // Gets executed before the watch command starts.
        }
      }
    ]
  }
};
```

## FAQ

### Can I define my plugin in a separate file and simply import it in the `webiny.project.ts` file?

Yes, you can and that's probably the best way to do it because the `webiny.project.ts` file will be easier to read.
