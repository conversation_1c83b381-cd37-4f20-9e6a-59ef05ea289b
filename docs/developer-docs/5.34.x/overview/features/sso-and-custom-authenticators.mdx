---
id: aafeaa2d
title: SSO & Custom Authenticators
description: Overview of different user authentication options within Webiny
---

import { Alert } from "@/components/Alert";

<CanIUseThis enterprise={true} since={"5.22.0"} />

<Alert type="success" title="What You’ll Learn">

- Different authentication options
- API Gateway Lambda authorizer

</Alert>

## About

Webiny by default ships with Cognito user pools as the default identity provider (IdP). Cognito is a proven IdP used in many enterprises today and should be enough to satisfy most of the security requirements within many different organizations.

## Webiny OSS vs Webiny Enterprise

In certain cases, an enterprise organization might already have an internal IdP in place for their employees and often the security department requires all vendors and solutions to integrate with this IdP.

Customers using Webiny Enterprise edition can integrate those 3rd party IdPs with Webiny and use it to control, from a central place, which of their employees are allowed to access their Webiny instance.

Webiny provides integrations with the most popular IdPs such as [OKTA](/docs/{version}/enterprise/okta-integration) and [Auth0](/docs/{version}/enterprise/auth0-integration) out of the box. For other IdPs and SSO Webiny's core engineering team can work directly with the customer and make those integrations, as part of their Webiny Enterprise contract.

## API Gateway Lambda authorizers

Certain organizations take every possible step to harden the security of applications and solutions deployed within their network. In certain cases, it might not be possible to use a specific solution if it doesn't meet those security requirements. One of those security policies is a requirement that API Gateways cannot have their Authorizers set to NONE. Instead, a Lambda authorizer function is required. A Lambda authorizer is an API Gateway feature that uses a Lambda function to control access to your API.

As a Webiny Enterprise customer, you will get the required support and ready-made plugins and scripts to implement your API Gateway Lambda authorizer.

## FAQ

**Can I use the OKTA and Auth0 integrations if I'm not an enterprise customer?**

The OKTA and Auth0 ready-made integrations are only available under the Webiny Enterprise contract and only organizations under that contract are allowed to use this feature.

**Can I build my own integration if I'm not an enterprise customer?**

The short answer is no. Webiny is licensed under a dual-license model, where the majority of the code base is under the MIT license, and the other smaller part is under our proprietary license. Developing a custom IdP integration requires access to the code that's under the proprietary license.

**Why did you lock this feature under an enterprise offering?**

As with many other businesses we too need to have a sustainable revenue model. Webiny in the open-source version is giving away over 90% of its capabilities and features completely for free. But to fund the future development of Webiny certain features are only available under the Enterprise of Business paid tiers. By purchasing the paid features you are supporting us as a business and as a product for many years to come.

**Can I still use the open-source version of Webiny inside my enterprise organization if we use Cognito?**

Yes. you can. The open-source version of Webiny ships with AWS Cognito as the IdP and uses the Cognito user pools to manage the user accounts and access. This setup is free for anyone to use.
