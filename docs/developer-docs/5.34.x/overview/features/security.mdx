---
id: aafeaa2c
title: Security
description: Overview of Webiny security strategy.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What You’ll Learn">

- How we approach security
- Additional security recommendations

</Alert>

## About

If we don't have security, we can't say we have a good product. Although Webiny is a self-hosted solution, meaning you control your security, it's still important for us to provide an overview of our security practices. This will make you acquainted with our approach, and will make it easier to align your implementation to some of the best practices we established.

This being an overview article, it will provide the main elements and a clear distinction in terms of the security responsibilities that are on your end in comparison to the ones being handled by Webiny.

We've structured the article into 4 main sections:

- **Codebase** - how we ensure our codebase isn't compromised
- **Infrastructure** - how the security layer is implemented as part of the infrastructure that Webiny deploys
- **Application** - how Webiny protects the application from unauthorized access
- **Data** - how your data is stored and secured

## Codebase

Webiny, being an open-source project, our code is public, and with that comes great responsibility for the security aspects around it.

Our code base is regularly scanned for vulnerabilities by [CodeQL](https://securitylab.github.com/tools/codeql)—the world’s most powerful code analysis engine.

All the 3rd party dependencies we use are scanned via [Dependabot](https://dependabot.com).

No single change made to our codebase skips these checks.

## Infrastructure

Webiny is designed to run on top of the AWS cloud at the moment, with support for other clouds coming at a later point.

<Alert type="info" title="Webiny Architecture">

To learn more about Webiny's architecture, check out [this article](/docs/{version}/architecture/introduction).

</Alert>

The serverless services Webiny uses as part of its architecture are fault-tolerant by design. The only exception is Elasticsearch. In the Webiny production deployment stack, Elasticsearch is automatically deployed in multiple AZs, making it highly available. Any public access, in both VPC and non-VPC deployment, is restricted.

With Webiny there are zero servers you need to manage, reducing the risk of a security breach due to misconfiguration. Serverless services are tested by the cloud provider against all forms of attacks across millions of customers, giving your confidence in their security. Serverless services in general reduce the potential attack vectors that can be used against your application.

Webiny ships with two deployment modes by default, a development one and a production one.

<Alert type="warning" title="Use the right deployment mode">

We highly recommend that you use the production deployment mode for all your projects running production traffic. [Learn more about the deployment modes.](/docs/{version}/architecture/deployment-modes/introduction)

</Alert>

The production stack by default is deployed inside a private VPC preventing direct access to resources like DynamoDB and the Elasticsearch database. Keeping your data safe.

### Deploying Into Amazon Virtual Private Cloud (Amazon VPC)

In its Enterprise tier, Webiny offers an additional infrastructure configuration, one that further hardens the security posture and keeps the communication between specific services within the private network without exposing any of its data or communication to the public internet.

Webiny Enterprise customers can deploy a Webiny project into their existing Amazon VPC that they already manage using their existing toolset.

In addition to deploying Webiny inside an Amazon VPC, Amazon VPC endpoints can be used with services like Amazon DynamoDB, Amazon S3, Amazon SQS and Amazon EventBridge.

As many organizations already have a securely configured Amazon VPC and Amazon VPC endpoints, a Webiny project can be configured to utilize those existing resources. If the preference is to deploy Webiny into an isolated AWS account, but still utilize the VPC and VPC endpoints, that configuration is also available.

<Alert type={"info"}>

To learn more about deploying your Webiny project into an existing Amazon VPC, check out the [Use Existing Amazon VPC](/docs/{version}/enterprise/use-existing-amazon-vpc) article.

</Alert>

Additional security improvements you can make to your project:

- **Firewall**: Webiny intentionally deploys a CloudFront web distribution in front of an API Gateway. This setup allows you to place a web application firewall on all incoming requests for added security.

## Application

Webiny is built to [support different authentication mechanisms](/docs/{version}/overview/features/sso-and-custom-authenticators). Out of the box, it is configured to work with [AWS Cognito](https://aws.amazon.com/cognito/), an enterprise-grade, battle-tested identity provider. The framework, however, supports any identity provider, by means of plugins.

Webiny draws a very clear line between **authentication** and **authorization**. We rely on third-party IdPs for authentication and use a very flexible system of permissions for authorization.

Authorization is usually very closely tied to business logic, so using scopes from the JWT, or simple string-based roles is not enough for complex business applications. To allow different types of integration, we provide a very thin security framework, based on plugins. On top of that, we define permissions as rich objects with different application-related properties.

## Data

Because Webiny is self-hosted and under the MIT license, your data stays within your cloud infrastructure. In practice, this usually means there is no need for any legal, data compliance or GDPR changes to be made when adopting Webiny.

When it comes to storing your data, it's saved inside a DynamoDB database and Elasticsearch, and files are stored inside an S3 bucket.

All the data inside DynamoDB is encrypted in transit and at rest by default.

All the data inside Elasticsearch is encrypted at transit only, by default.

<Alert type="info" title="Elasticsearch Encrypt at rest">

For production cases, we recommend you also encrypt data at rest in Elasticsearch. Ensure you have a proper key management strategy in place beforehand otherwise it could lead to making your data inaccessible. For more info visit: https://docs.aws.amazon.com/elasticsearch-service/latest/developerguide/encryption-at-rest.html

</Alert>

The data inside S3 bucket by default is not encrypted, but it's limited to private access only. Public access is not allowed. Write operations to the buckets need to be signed with a security token to be allowed. We advise that you adjust these parameters to the needs of your project accordingly.

<Alert type="info" title="Making S3 private">

As of version 5.31 all new Webiny projects will have S3 buckets restricted to private access only. Before 5.31 the S3 buckets have public access enabled.

</Alert>

## SOC2 Type 1 Certified

Webiny is proud to announce it has completed the System and Organization Controls (SOC) 2 Type I examination in recognition of its commitment to information security. Developed by the American Institute of CPAs (AICPA), SOC 2 (System and Organization Controls) defines criteria for managing customer data based on five “trust service principles” — security, availability, processing integrity, confidentiality, and privacy.

A SOC 2 examination provides organizations with a report on an organization's internal controls and how it protects customer data and sensitive information. It is the standard for data security among digital companies in the U.S.
An independent auditor, Insight Assurance, conducted the detailed examination via Vanta. Through this process, Webiny demonstrated its adherence to data security, availability and confidentiality standards developed by the American Institute of Certified Public Accountants (AICPA). With its SOC 2 recognition, Webiny not only protects the safety of its customers' data today but demonstrates that it has set the right standards in place for the future.

## FAQ

### Can I use a different identity provider?

Yes, absolutely. The default IdP implementation contains a set of plugins on the API and React sides. All you need to do is follow the same principle and implement your own plugins that will work with the IdP of your choice.

### Can I roll my own authentication system instead of using a third-party IdP?

We don't recommend it, but yes, you can. Following the same implementation strategy that comes with Webiny by default, you can create all kinds of authentication mechanisms.
