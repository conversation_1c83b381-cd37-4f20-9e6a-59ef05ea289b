---
title: Using the GraphQL API Filtering
description: Learn how to use the Headless CMS's built-in GraphQL API filter.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- how to use the Headless CMS GraphQL API filtering
- why do you need to make multiple queries to filter a referenced field

</Alert>

## Overview

In this article we will go through simple filtering of all of our built-in fields. Available filters are, sometimes, quite different from field to field.

In all of our examples we will use an `Article` model which has all the available fields in it.

<Alert type="info">

When filtering by equal value, there is no `eq` or `equal` or anything similar, like in other filters. You simply use the field name and the value you want to filter by.

</Alert>

## Simple Field Filtering

### The Boolean Field

The `Boolean` field is the simplest one. It has only two possible values: `true` and `false`.

Available filters are:

- `equal`
- `not`

```graphql
query {
  listArticles(where: { isImportant: true, isImportant_not: false }) {
    data {
      id
      title
      isImporant
    }
  }
}
```

### The Number Field

The `Number`field has the following filters:

- `equal`
- `not`
- `in`
- `not_in`
- `lt`
- `lte`
- `gt`
- `gte`
- `between`
- `not_between`

```graphql
query {
  listArticles(
    where: {
      priority: 1
      priority_not: 2
      priority_in: [2, 3, 4, 5]
      priority_not_in: [21, 31, 41, 51]
      priority_lt: 501
      priority_lte: 500
      priority_gt: 0
      priority_gte: 1
      priority_between: [1, 500]
      priority_not_between: [501, 1000]
    }
  ) {
    data {
      id
      title
      priority
    }
  }
}
```

### The DateTime Field

The `DateTime` field has the following filters:

- `equal`
- `not`
- `in`
- `not_in`
- `lt`
- `lte`
- `gt`
- `gte`

```graphql
query {
  listArticles(
    where: {
      date: "2023-01-01"
      date_not: "2023-02-01"
      date_in: ["2023-01-01", "2023-01-02", "2023-01-03", "2023-01-04"]
      date_not_in: ["2023-02-01", "2023-03-01"]
      date_lt: "2024-02-02"
      date_lte: "2024-02-01"
      date_gt: "2023-01-01"
      date_gte: "2023-01-02"
    }
  ) {
    data {
      id
      title
      date
    }
  }
}
```

### The Text Field

The `Text` field has the following filters:

- `equal`
- `not`
- `in`
- `not_in`
- `contains`
- `not_contains`

```graphql
query {
  listArticles(
    where: {
      title: "Webiny"
      title_not: "Not serverless"
      title_in: ["Webiny", "Serverless", "DynamoDB", "S3"]
      title_not_in: ["Server", "On-Premise"]
      title_contains: "serverless"
      title_not_contains: "server"
    }
  ) {
    data {
      id
      title
    }
  }
}
```

### The Long-Text Field

The `LongText` field has the following filters:

- `contains`
- `not_contains`

```graphql
query {
  listArticles(where: { description_contains: "serverless", description_not_contains: "server" }) {
    data {
      id
      description
    }
  }
}
```

### The Rich-Text Field

The `RichText` field has no filters. This is due to the `RichText` field being a JSON object.

### The File Field

The `File` field has no filters.

### The Reference Field

The `Reference` field has the following filters:

- `id` (`equal`)
- `id_not`
- `id_in`
- `id_not_in`
- `entryId_equal`
- `entryId_not`
- `entryId_in`
- `entryId_not_in`

```graphql
query {
  listArticles(
    where: {
      category: {
        id: "abc#0001"
        id_not: "abc#0002"
        id_in: ["abc#0001", "abc#0003", "abc#0004"]
        id_not_in: ["abc#0999"]
        entryId: "abc"
        entryId_not: "abc"
        entryId_in: ["abc", "abc", "abc"]
        entryId_not_in: ["abc"]
      }
    }
  ) {
    data {
      id
      category {
        id
        title
      }
    }
  }
}
```

When filtering with the `id`, you will filter by that exact ID, which means Entry ID + Revision. If you want to filter for any revision of the entry you need, you will need to use the `entryId` filter.

<Alert type="warning">

If you want to filter by a referenced field title value, first you must run the query on that referenced model and then use the result set from it in your main query.

The reason for this is mainly about the amount of referenced entries we would need to load to be able to filter by a referenced field.

</Alert>

### The Object Field

The `Object` field has all the filters available for the which ever field was inserted in it. For example, if your `Object` (`seo`) field has a `Text` (`title`) and a `Number` (`priority`) field, it will have filters for those field types:

```graphql
query {
  listArticles(
    where: { seo: { title: { title_contains: "Webiny" }, priority: { priority_gte: 100 } } }
  ) {
    seo {
      title
      priority
    }
  }
}
```

## Conditional Filtering

The article [`Using the GraphQL API Advanced Filtering`](/docs/headless-cms/basics/using-graphql-api-advanced-filtering) will cover the conditional (advanced) filtering, where we will show you how to use the `AND` and `OR` conditionals.
