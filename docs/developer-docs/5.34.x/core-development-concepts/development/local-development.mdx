---
id: aafeaa38
title: Local Development
description: Learn about how to do local development in Webiny
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- how to do local development in Webiny
- working with team on a Webiny project

</Alert>

## Overview

Serverless development has slightly different principles from traditional application development. In the traditional development process, developers typically develop and test their project locally before deploying it to a server. However, in the serverless world, this process is slightly different. In this article, we will look at how to do local development in Webiny, a serverless CMS.
If you are absolutely beginner to serverless, we will recommend you to read [this article](https://www.webiny.com/knowledge-base/serverless).

Webiny is a serverless application, which means that it uses serverless services like AWS Lambda, DynamoDB, S3, etc. to run. For the sake of simplifying the understanding, in context of development, we can devide Webiny code into two parts: the API & Infrastructure (backend) and the User Interface (frontend).

To do local development in Webiny, the API or any infrastructure changes must be deployed in the cloud. However, we have built various CLI tools that make the experience seamless, so when you make a change to the code and save it, it will be deployed to the cloud simultaneously, and you can see the changes in no time. For the frontend development, you can develop and test locally, and if everything looks good, you can deploy it to the cloud later.

In summary, changes made to API and infrastructure must be deployed to the cloud during local development. However, for changes made to only UI, deploying to the cloud is not necessary. Once the UI changes are finalised, these changes can be pushed to the cloud.

If you're wondering how to push changes to the cloud during development, don't worry. As previously mentioned, we have developed various CLI tools, such as the `watch` command, to make deploying changes to the cloud during development easy. The `watch` command allows you to continuously rebuild and redeploy your code, making the development process seamless.
For detailed information, please refer to the [`watch command`](/docs/core-development-concepts/basics/watch-command) documentation.

Now let's learn more about local development with the frequently asked questions related to Webiny development.

## Is it possible to perform local development in Webiny without deploying any changes to the cloud?

As previously mentioned, for the API and infrastructure related portion of the development, deployment to the cloud is necessary as it is powered by various serverless services such as AWS Lambda, DynamoDB, API Gateway, etc. However, for frontend development, it is possible to develop and test locally and only deploy to the cloud once everything is looking good.

## Can I use a tool like LocalStack as cloud service emulator for development?

Webiny utilizes a variety of AWS serverless services, such as AWS Lambda, Amazon Cognito, SQS, S3 etc.
While simulating Lambda locally is not that hard (although it's still not trivial),
but it's hard to emulate cloud native mechanisms, like reacting to S3 objects-related events,
Amazon Event Bridge events, emulating services like Amazon Cognito, SQS. So, it will not be possible to test Webiny with tools like LocalStack because Webiny does not work without AWS services.  
If you find it challenging to work with any part of local development, get in touch with us on our [Community Slack](https://www.webiny.com/slack). It will help us offer some advice or improve the local development experience.

## What is the potential cost associated with using an AWS account for development?

Webiny supports [two storage types](/docs/get-started/install-webiny#pick-your-database) at the moment: DynamoDB and DynamoDB+Elasticsearch. The DynamoDB-only version utilizes only serverless services, which means it follows pay-per-use pricing. This means that for development, the cost is typically minimal or free.  
On the other hand, in the DDB + ES version, ElasticSearch is the only non-serverless service used by Webiny. To mitigate costs, you can have one ElasticSearch domain, and each developer can have a different index prefix (there's an `ENV` variable for that). This way, only one ElasticSearch domain is paid for and all developers have their own infrastructure deployed in their accounts, but they all use the same ElasticSearch domain and have isolated indexes with prefixes. A step-by-step document will be provided on how to implement this setup.

## Working with team on a Webiny project

Our recommended approach is for each developer to have their own isolated environment in which they deploy and work. This approach also involves each developer having their own AWS account. Once changes are ready, they are then merged into a shared environment, such as the `dev` environment. For more information on this approach, including branches, environments, state files, and more, please refer to the [CI/CD section](/docs/{version}/core-development-concepts/ci-cd/introduction). If cost is your concern, please refer to the section above, which states that when using the DynamoDB-only flavor of Webiny, all services utilized are serverless, which typically results in minimal or no cost during development.
