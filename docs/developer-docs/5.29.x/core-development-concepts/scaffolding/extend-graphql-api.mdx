---
id: aafeab25
title: Extend GraphQL API
description: Learn how to use the Extend GraphQL API scaffold.
---

import { Alert } from "@/components/Alert";

<Alert type="info" title="Can I use this?">

The **Extend GraphQL API** scaffold (previously GraphQL **API Package**) received a major overhaul in [Webiny 5.9.0](/docs/release-notes/5.9.0/changelog). Note that this guide is not relevant for projects that are using Webiny 5.8.0 and lower.

</Alert>

<Alert type="success" title="What you'll learn">

- main features of the **Extend GraphQL API** scaffold
- how to continue developing on top of the generated application code

</Alert>

## Overview

The **Extend GraphQL API** scaffold extends your project's GraphQL HTTP API with a new [CRUD](https://en.wikipedia.org/wiki/Create,_read,_update_and_delete) query and mutation operations.

Note that, when we say GraphQL HTTP API, by default, we're referring to the one deployed as part of the **API** project application, located within the [`api/graphql`](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cwp-template-aws/template/api/code/graphql) folder. But, the scaffold can be used with any GraphQL API you might have in your project, especially the ones created with the [GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api) scaffold.

<Alert type="info">

Learn more about the **API** project application on the cloud infrastructure level in the [Cloud Infrastructure](/docs/{version}/architecture/api/overview) key topics section.

</Alert>

## Features

### New CRUD Query and Mutation Operations

The **Extend GraphQL API** extends your GraphQL API with two query and three mutation operations.

The names of these depend on the name of the initial data model, which is specified during the scaffold's wizard. For example, by passing **CarManufacturer** as the initial data model name, the GraphQL API will be extended with the following queries and mutations:

```graphql
type CarManufacturerQuery {
  # Returns a single CarManufacturer entry by given ID.
  getCarManufacturer(id: ID!): CarManufacturer

  # Returns a list of CarManufacturer entries. Supports basic sorting and pagination.
  listCarManufacturers(
    limit: Int
    before: String
    after: String
    sort: CarManufacturersListSort
  ): CarManufacturersList!
}

type CarManufacturerMutation {
  # Creates and returns a new CarManufacturer entry.
  createCarManufacturer(data: CarManufacturerCreateInput!): CarManufacturer!

  # Updates and returns an existing CarManufacturer entry.
  updateCarManufacturer(id: ID!, data: CarManufacturerUpdateInput!): CarManufacturer!

  # Deletes and returns an existing CarManufacturer entry.
  deleteCarManufacturer(id: ID!): CarManufacturer!
}

extend type Query {
  carManufacturers: CarManufacturerQuery
}

extend type Mutation {
  carManufacturers: CarManufacturerMutation
}
```

<Alert type="info">

If you want to check out the full GraphQL schema that's generated during the scaffolding, take a look at the [template](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-graphql-service/template/typeDefs.ts) that's used in the process.

</Alert>

### Storing and Retrieving Data

All of the generated GraphQL queries and mutations rely on a single database for storing and retrieving data. By default, this is [Amazon DynamoDB](https://aws.amazon.com/dynamodb/), which is defined in [`api/pulumi/dev/dynamoDb.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/api/pulumi/dev/dynamoDb.ts) and [`api/pulumi/prod/dynamoDb.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/api/pulumi/prod/dynamoDb.ts).

For all database interactions, the generated application code relies on a library called [DynamoDB Toolbox](https://github.com/jeremydaly/dynamodb-toolbox):

> The DynamoDB Toolbox is a set of tools that makes it easy to work with Amazon DynamoDB and the DocumentClient. It's designed with Single Tables in mind, but works just as well with multiple tables.

Note that if you prefer a different library, like for example the [DynamoDB Document Client](https://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/dynamodb-example-document-client.html), you're free to use it, but it will require some manual migration work.

### Multi-Locale Support Enabled

By default, Webiny Admin Area is a multi-locale system, which means entries like pages, page categories, forms, settings, and so on, are always created for a specific locale.

The generated application code follows the same idea. There aren't any additional steps that you need to take in order to enable multi-locale support.

### Testing Setup

With the application code, the scaffold also generates one unit, one integration, and one end-to-end (E2E) test, which you can use as a reference in further development. All of the tests can be found in the [`__tests__`](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cli-plugin-scaffold-graphql-service/template/__tests__) folder located within the scaffolded application code. More on this in the [Development](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api#development) section below.

<Alert type="success">

A brief overview of different types of tests can be found in the [CI/CD / Testing](/docs/{version}/core-development-concepts/ci-cd/testing#application-testing---a-brief-overview) key topic.

</Alert>

### No Additional Cloud Infrastructure Resources

The scaffold does not add any additional cloud infrastructure resources. The generated application code is simply added to the existing GraphQL API application code ([`api/graphql`](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cwp-template-aws/template/api/code/graphql)).

Of course, if additional cloud infrastructure resources are needed, you're free to amend the [existing cloud infrastructure code](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cwp-template-aws/template/api/pulumi), and reference the added and deployed resources in your application code.

## Development

### Usage

In order to use this scaffold, from your project root, simply run the `webiny scaffold` command:

```bash
yarn webiny scaffold
```

Then, from the list of all available scaffolds, select **Extend GraphQL API** and follow the on-screen instructions.

### Essential Files

The following are the most essential files and folders that are generated in the scaffolding process.

<Alert type="info">

By default, all of the files are generated in a new folder, located at `api/graphql/src/plugins/scaffolds/{dataModelName}`, for example `api/graphql/src/plugins/scaffolds/carManufacturers`.

</Alert>

#### [`index.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-graphql-service/template/index.ts)

This is the entry file, which exports the [`GraphQLSchemaPlugin`](https://github.com/webiny/webiny-js/blob/v5/packages/handler-graphql/src/plugins/GraphQLSchemaPlugin.ts) plugin responsible for extending the existing GraphQL API schema, using new [type definitions](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-graphql-service/template/index.ts#L13) and supporting [resolvers](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-graphql-service/template/index.ts#L14-L25).

#### [`typeDefs.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-graphql-service/template/typeDefs.ts)

A file that contains the schema with which we are extending our GraphQL API. This will usually be the starting point if you want to make further changes to the generated code, for example add new fields to the initial data model, add new GraphQL query or mutation operations, and more.

Note that in the case of the latter, you also need to define a standalone resolver function that is responsible for handling newly added query or mutation operation.

#### [`resolvers/`](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cli-plugin-scaffold-graphql-service/template/resolvers)

This folder contains the GraphQL resolver classes that support the generated [query](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-graphql-service/template/resolvers/TargetDataModelsQuery.ts) and [mutation](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-graphql-service/template/resolvers/TargetDataModelsMutation.ts) GraphQL operations.

These will need to be updated as you make changes in the mentioned [`typeDefs.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-graphql-service/template/typeDefs.ts) file. For example, if you were to introduce a new `listPopularCarManufacturers` GraphQL query, the same `listPopularCarManufacturers` method would need to be defined within the [`CarManufacturersQuery`](https://github.com/webiny/webiny-js/blob/next/packages/cli-plugin-scaffold-graphql-service/template/resolvers/TargetDataModelsQuery.ts) class.

#### [`entities/`](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cli-plugin-scaffold-graphql-service/template/entities)

Contains [DynamoDB Toolbox](https://github.com/jeremydaly/dynamodb-toolbox) entities, which are used within GraphQL resolver functions. You are free to add additional entities, or modify the existing one (for example - add new entity attributes).

#### [`__tests__/`](https://github.com/webiny/webiny-js/tree/v5.10.0/packages/cli-plugin-scaffold-graphql-service/template/__tests__)

Contains tests that ensure your application code is working as expected. As mentioned, three tests are included by default: one unit, one integration, and one end-to-end (E2E) test.

### First Deploy

Once you've completed the scaffold's wizard and the files have been generated, in order to actually see the changes made to your GraphQL API, you need to deploy them. This can be done as usual via the [`webiny deploy`](/docs/{version}/core-development-concepts/basics/project-deployment) command, or, even easier, if you're about to jump straight into coding, by running the [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) command. This command will not only deploy the changes, but also start a new watch session, which will automatically redeploy further application code changes, as you perform them. More on this below.

### Development Using the Watch Command

The most straightforward way to further develop on top of the generated code would be via the [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) command.

In order to get started, from your project root, simply run the following command:

```
yarn webiny watch apps/api/graphql --env {env}
```

With the new watch session initialized, every change you make in the code will automatically trigger a re-deploy of the code, enabling you to see the changes in the cloud almost immediately as you make them (every redeployment takes 2-4 seconds to complete).

And although developers often choose this approach when developing smaller features or proof of concepts, for anything larger in scope, we recommend the new code to be tested via one or more of the mentioned types of tests.

### Writing and Running Tests

As mentioned in the [Features](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api#features) section, by default, the scaffold includes three tests: one unit, one integration, and one end-to-end (E2E) test. When generated, all three tests should be passing once you run them, but do note that in order for the integration and end-to-end (E2E) tests to pass, you have to have your **API** project application already deployed, preferably into a development environment, for example `dev`.

<Alert type="info">

To run the tests, Webiny relies on [Jest](/docs/{version}/core-development-concepts/basics/tools-and-libraries#testing), a JavaScript testing framework with a focus on simplicity.

</Alert>

#### Initial Test Files

#### [`typeDefs.unit.test.ts`](https://github.com/webiny/webiny-js/blob/v5/packages/cli-plugin-scaffold-graphql-service/template/__tests__/typeDefs.unit.test.ts)

A simple unit test which simply ensures that the base GraphQL type exists. Might not be a super useful example, but still, this test is here more for awareness purposes (so that developers know these can be added too).

#### [`crud.integration.test.ts`](https://github.com/webiny/webiny-js/blob/v5/packages/cli-plugin-scaffold-graphql-service/template/__tests__/crud.integration.test.ts)

An integration test which ensures essential CRUD GraphQL query and mutation operations are working as expected. These tests are run against the application code that you have locally, but note that the code is still interacting with real cloud infrastructure resources.

#### [`crud.e2e.test.ts`](https://github.com/webiny/webiny-js/blob/v5/packages/cli-plugin-scaffold-graphql-service/template/__tests__/crud.e2e.test.ts)

An end-to-end (E2E) test which ensures essential CRUD GraphQL query and mutation operations are working as expected.

Note that, although it may seem this test is testing the same functionality we've tested with the integration test, this test is run against the application code that's deployed into the actual cloud, by issuing HTTP requests to the deployed Cloudfront distribution, behind which our AWS Lambda function resides. In other words, this test makes sure not only our code is working as expected, but also that the cloud infrastructure resources are deployed and configured properly.

#### Running Tests

All of the test-running scripts can already be found in your root [package.json](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/dependencies.json#L68-L71) file.

In order to run all tests, use the `yarn test` command. Additionally, to only run tests of a specific type, use the `yarn test:unit`, `yarn test:integration`, and `yarn test:e2e` commands.

<Alert type="success">

Jest supports the [`--watch`](https://jestjs.io/docs/cli#--watch) argument, which enables us to continuously re-run tests as we're making changes to them. You can use it with the above-shown commands.

</Alert>

#### Creating New Tests

You are free to create new tests in the similar fashion, or amend the existing one.

## FAQ

### How does security (authentication and authorization) work?

Please note that, by default, the authentication and authorization logic isn't included in the generated code. In other words, all of the generated GraphQL query and mutation operations can be performed by anonymous (not logged-in) users, which is in most cases not the desired behavior.

Luckily, with a couple of built-in utilities, this can be relatively easily added. Please check out the [existing tutorials](/docs/admin-area/new-app-tutorial/security) to learn how to implement these on your own.

### I need more flexibility when it comes to data querying. Can I bring in a different database, for example ElasticSearch?

Yes, you can. You can actually utilize the already deployed ElasticSearch cluster that's deployed with every Webiny project, by default.

Until we create a dedicated tutorial on the topic, feel free to ping us via our community [Slack](https://www.webiny.com/slack).

### Can Webiny's multi-tenancy feature be utilized with this scaffold?

Yes, but do note that since multi-tenancy is part of Webiny's enterprise offering, the relevant multi-tenancy code is not included in the generated application code.

For more information on multi-tenancy, we recommend you check out the [Multi-Tenancy](/docs/overview/features/multi-tenancy) key topic. Also, for any implementation-related questions, feel free to contact us directly via our community [Slack](https://www.webiny.com/slack).
