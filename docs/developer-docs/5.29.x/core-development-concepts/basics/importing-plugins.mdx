---
id: aafeab1e
title: Importing Plugins
description: Learn how to add plugins to various sections of Webiny.
---

import { Al<PERSON> } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- how to add plugins to various parts of Webiny
- how to best organize your plugins

</Alert>

<Alert type="info">

If you're not yet familiar with the concept of plugins in Webiny, please refer to the [Plugins](/docs/{version}/core-development-concepts/basics/plugins) key topic article.

</Alert>

## Overview

Throughout our docs, and especially in the how-to guides, we often show which plugins you need to use to achieve certain goals, but we skip the step-by-step instructions on how to enable those plugins. That is to prevent redundant content being copied in every article. In those guides, you'll simply see an instruction like _"Add this plugin to your Admin Area application"_.

This article explains how to add plugins to:

- [React applications (Admin Area and Website)](/docs/{version}/core-development-concepts/basics/importing-plugins#react-applications)
- [GraphQL APIs (Main GraphQL API and Headless CMS API)](/docs/{version}/core-development-concepts/basics/importing-plugins#graphql-apis)
- [Webiny CLI](/docs/{version}/core-development-concepts/basics/importing-plugins#webiny-cli)

## React Applications

Both **Admin Area** and **Website** React applications follow the same folder structure, and so, the process of registering new plugins into both applications is the same.

The plugins entry files for these applications can be found at the following locations:

- Admin Area: [`apps/admin/src/plugins/index.ts`](https://github.com/webiny/webiny-js/blob/next/apps/admin/code/src/plugins/index.ts)
- Website: [`apps/website/src/plugins/index.ts`](https://github.com/webiny/webiny-js/blob/next/apps/website/code/src/plugins/index.ts)

To register a new plugin, you need to navigate to the appropriate plugins entry file, and add your plugin to the end of the array that is being registered into the [PluginsContainer](/docs/{version}/core-development-concepts/basics/plugins#plugins-container).

<Alert type="success" title="Organize your plugins">

We always recommend creating a dedicated file, or even a folder, to hold your custom plugins.

</Alert>

```ts apps/{admin|website}/src/plugins/myPlugins.ts
// A dedicated file for your custom plugin(s)
export default [
  {
    type: "my-plugin-type",
    hello() {
      console.log("Hello!");
    }
  }
];
```

Now that we have a file containing our plugins, we can import it and register in the React application:

```ts apps/{admin|website}/src/plugins/index.ts
import { plugins } from "@webiny/plugins";
// ...other imports...

// Import your custom plugins
import myPlugins from "./myPlugins";

plugins.register([
  // ... other plugins ...

  // Add your own plugins at the very end of the array.
  myPlugins
]);
```

With this, your new plugin(s) will be registered into the application.

<Alert type="success">

If you already have your React app running locally, the new import will be picked up automatically, and your plugin should be registered as soon as the browser reloads.

If your application is deployed to the cloud, you'll need to redeploy it to apply your new plugin(s).

</Alert>

## GraphQL APIs

All GraphQL API Lambda function handlers present in the default Webiny project follow the same structure, and so, the process of registering new plugins is the same for all of them.

We'll cover the two main GraphQL API Lambda function handlers: **Main GraphQL API** and **Headless CMS GraphQL API**. Their entry files can be found at the following locations:

- Main GraphQL API: [`apps/api/graphql/src/index.ts`](https://github.com/webiny/webiny-js/blob/next/api/code/graphql/src/index.ts)
- Headless CMS GraphQL API: [`apps/api/headlessCMS/src/index.ts`](https://github.com/webiny/webiny-js/blob/next/api/code/headlessCMS/src/index.ts)

To register a new plugin, you need to add it to the end of the `plugins` array that is being passed to the `createHandler` factory function.

```ts apps/api/{graphql|headlessCMS}/src/plugins/myPlugins.ts
// A dedicated file for your custom plugin(s)
export default [
  {
    type: "my-plugin-type",
    hello() {
      console.log("Hello!");
    }
  }
];
```

Now that we have a file containing our plugins, we can import it and register in the corresponding Lambda function entry file:

```ts apps/api/{graphql|headlessCMS}/src/index.ts
// ...other imports...

// Import your custom plugins
import myPlugins from "./plugins/myPlugins";

export const handler = createHandler({
  plugins: [
    // ... other plugins ...

    // Add your own plugins at the very end of the array.
    myPlugins
  ]
});
```

<Alert type="success">

If you already have the [watch](/docs/{version}/core-development-concepts/basics/watch-command#webiny-watch-command) running, the new import will be picked up, built, and deployed to the cloud automatically.

If not, you'll need to [deploy](/docs/{version}/core-development-concepts/basics/project-deployment#the-deploy-command) your API manually, to apply the new plugin(s).

</Alert>

<Alert type="info" title="IMPORTANT!">

Even though this process looks very similar to how we register plugins into React applications, there's a big difference between the two. React applications have one single global `PluginsContainer`, while API applications create a new one to handle each individual invocation (HTTP request).

To learn more, please read the [Plugins](/docs/{version}/core-development-concepts/basics/plugins) key topic article.

</Alert>

## Webiny CLI

To register a new plugin into the Webiny CLI, navigate to `webiny.project.ts` file, located in the root of your Webiny project, and add your new plugin to the `plugins` array.

As usual, we recommend creating a dedicated file/folder for your custom plugins, to keep things nicely organized.

```ts cliPlugins.ts
export default [
  {
    type: "cli-command",
    create({ yargs, context }) {
      yargs.command("say-hi", "Prints a welcome message.", () => {
        console.log("Hi there! I'm a new Webiny CLI command.");
      });
    }
  }
];
```

Now add your new plugin to the Webiny CLI:

```ts webiny.project.ts
// ... other imports ...
import cliPlugins from "./cliPlugins";

export default {
  template: "@webiny/cwp-template-aws@5.x.y",
  name: "my-project",
  cli: {
    plugins: [
      // ... other plugins ...
      cliPlugins
    ]
  }
};
```

Next time you run the Webiny CLI, your new plugin will be applied.
