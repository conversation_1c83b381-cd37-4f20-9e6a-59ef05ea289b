---
id: aafeab24
title: Project Applications and Packages
description: Learn about fundamental organizational units of every Webiny project.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU’LL LEARN">

- what are the fundamental organizational units of every Webiny project and their most important characteristics

</Alert>

<!--
## Webiny Project
Everything starts with a **Webiny project**, which is what gets created for you (on your local file system) in the [Webiny Installation](/docs/{version}/get-started/install-webiny) process. This is the top organizational unit, which is made of packages and project applications.
-->

## Packages and project applications

Every Webiny project consists of two fundamental organizational units - **packages** and **project applications** (or just **applications**).

Packages are just regular [NPM packages](https://docs.npmjs.com/about-packages-and-modules#about-packages), or in other words, folders with its own `package.json` declaration file and some code.
On the other hand, project applications are higher-level organizational units formed from one or more packages that, as the name itself suggests, form applications. Applications consist of both application code and cloud infrastructure that is needed in order to run them.

<Alert type="info">

Visit the [Project Applications](/docs/{version}/core-development-concepts/project-organization/project-applications) section to learn more about project applications.

</Alert>

<Alert type="success">

Every Webiny project is organized as a monorepo. Visit the [Monorepo Organization](/docs/{version}/core-development-concepts/project-organization/monorepo-organization) topic to learn more.

</Alert>

The following diagram shows the project organization in a simplified and clear manner:

![Project Organization](./assets/project-applications-and-packages/project-organization.png)

If we were to translate the above diagram into a simplified directory tree, we would end up with the following:

```
.
├── Project Application 1
│   ├── package-1a
│   │   ├── src
│   │   └── package.json
│   ├── package-1b
│   │   ├── src
│   │   └── package.json
│   └── package-1c
│       ├── src
│       └── package.json
│  
├── Project Application 2
│   ├── package-2a
│   │   ├── src
│   │   └── package.json
│   └── package-2b
│       ├── src
│       └── package.json
│  
├── packages
│   ├── shared-package-1
│   │   ├── src
│   │   └── package.json
│   └── shared-package-2
│       ├── src
│       └── package.json
└── (...)
```

Packages **1a**, **1b**, and **1c** are located in the `Project Application 1` folder, and the **2a** and **2b** in `Project Application 2`. And, as seen in the diagram above, **Project Application 1** is using the shared package `shared-package-1`, and **Project Application 2** both `shared-package-1` and `shared-package-2`. These shared packages are located in a separate `packages` folder.

<Alert type="info">

The name of the `packages` folder is arbitrary and at this point, not important.

</Alert>

### Important Characteristics

Here are some of the most important characteristics of packages and applications.

#### Package scope

A package can represent literally anything. From a simple JavaScript function or a class, one, or more plugins, a whole REST/GraphQL API, or even a full-blown React app. You can even create utility packages that, for example, export one, or more utilities, and are imported by other packages (applications) in your project.

#### Limits

There is no limit in terms of the total number of packages and applications a single Webiny project can have.

#### Package and project application dependencies

Packages and applications can be independent, but, more importantly, can also be dependent on other packages, and applications, respectively.

In terms of packages, an example might be a utility (shared) package whose code is imported by other packages. This way you keep your code in a single location and don't repeat yourself, which effectively makes the code easier to maintain.

The same can happen with project applications. For example, let's say you have a simple HTTP API and a React app that's relying on it. In this case, the project application that's holding the React app depends on the project application that's holding the HTTP API.

![Dependent Packages and Applications](./assets/project-applications-and-packages/dependent-packages-apps.png)

## FAQ

### Does Webiny enforce strict files and folders organization?

No. You can organize packages and project applications in any way you see fit. For example, the folder in which you store your shared packages can be named in any way you like. You can have as many of those as needed, and can be organized in different folders, again, as you see fit. The same goes for project applications.
