---
id: aafeab22
title: Project Applications
description: Learn what are project applications and how they're structured.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU’LL LEARN">

- what are project applications and how they're structured

</Alert>

## The basics

Webiny uses the term **project application** (or just **application**) in order to depict a specific logical segment of your project.

Project applications are higher-level organizational units formed from one or more packages that, as the name itself suggests, form applications. Every application, essentially, consists of two pieces:

1. Your application code, which includes one or more Node.js packages. These packages can be anything from a simple GraphQL API or a single Lambda function to a complete React application.
2. Cloud infrastructure that hosts your code or which is being utilized by it, which is also described and deployed using code. For that matter, by default, Webiny relies on a solution called [Pulumi](/docs/{version}/infrastructure/pulumi-iac/iac-with-pulumi), which is a modern infrastructure as code solution.

<Alert type="info">

Learn more about the fundamental organizational units - project applications and packages, by visiting the [Project Applications and Packages](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages) page.

</Alert>

Moving on, every application has its own folder. For example, every Webiny project consists of four applications.
The **core** (`./apps/core`), **api** (`./apps/api`), which represents your project's (GraphQL) API, **admin** (`./apps/admin`), and finally, the **website** (`./apps/website`), which is your public website.

These applications are shown in the following directory tree:

```
.
├── apps
│   ├── core
│   ├── api
│   ├── admin
│   └── website
└── (...)
```

## FAQ

### How many applications can a single Webiny project have?

Every Webiny project can have any number of applications. This depends on the project requirements that are in front of you.

### Can applications work together?

Yes, and in most cases, [they are working together](/docs/{version}/core-development-concepts/project-organization/project-applications-and-packages#package-and-project-application-dependencies).
