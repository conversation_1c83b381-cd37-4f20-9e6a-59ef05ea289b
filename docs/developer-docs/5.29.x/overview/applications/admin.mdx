---
id: aafeab0f
title: Admin
description: Overview of Webiny Admin app, an extensible admin interface application.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- Webiny Admin app overview

</Alert>

![Webiny CMS - Admin](./assets/webiny-admin.jpg)

Webiny CMS is a unified product that consists of several apps. To access them, you use the Admin app. The Admin app not only contains ready-made apps, but also provides a framework for developers to extend their functionality, and develop custom ones.

The Admin app consists of numerous plugins you use to do different things, like changing the logo, adding or removing items from the main menu, adding new functionality to the existing UI.
Admin app also features a scaffold template to quickly create a fully functional CRUD (Create, Read, Update, Delete) app. Explore the Admin app in the left menu to find out more.

## Admin App Setup

If you inspect the files that make up the Admin app in your Webiny project (located in `apps/admin` folder within your Webiny project), you'll notice that it only has this small code snippet:

```tsx apps/admin/src/App.tsx
import React from "react";
import { Admin } from "@webiny/app-serverless-cms";
import { Cognito } from "@webiny/app-admin-users-cognito";
import "./App.scss";

export const App = () => {
  return (
    <Admin>
      <Cognito />
    </Admin>
  );
};
```

The `Admin` component provides the full setup of the Webiny CMS Admin app, and the `@webiny/app-serverless-cms` package also exports various utilities and components you can use to extend the CMS.

In the code example above, you can see we're also using a `Cognito` component, which extends the CMS and provides AWS Cognito authentication capabilities, admin user management, etc. This is how you add new functionality to the CMS: you simply mount a React component, which internally uses other React components to add menus, routes, user menu items, etc. More on this in the next article.

## FAQ

### Can I disable individual apps?

Webiny CMS is a unified product. Think of it as a single product which solves a set of problems. Individual apps are often interconnected and rely on each other, so you can't just disable one app and expect the whole thing to continue working. If you need to hide individual menu items, so they are not crowding your UI, get in touch with us on our [Community Slack](https://www.webiny.com/slack), and we'll help you out.

### What about the old plugins?

If you're familiar with the previous versions of Webiny, you'll notice that we're moving away from the global plugins registry to this new, React based, API. Old plugins still work, but over time, we'll be migrating more and more of our apps to this new API (components). This is not only more natural in the context of React, but it also unlocks various features and allows us to use the full power of React when building our plugins.
