---
id: aafeab11
title: Introduction
description: How theming works inside Webiny Page Builder.
---

import { Alert } from "@/components/Alert";

<!-- Always include this section on top of the page. -->

<Alert type="success" title="What You’ll Learn">

- what is the Page Builder theme consist of
- what plugins are used in Page Builder theme

</Alert>

## Configuration File

The main configuration file for the Page Builder theme is located at: [`apps/theme/pageBuilder/index.ts`](https://github.com/webiny/webiny-js/blob/next/apps/theme/pageBuilder/index.ts).
This file exports a list of Page Builder theme plugins, and that's what you later import in your [**website**](https://github.com/webiny/webiny-js/blob/next/apps/website/code/src/plugins/index.ts#L6) app and then register as a plugin.

The configuration file is structured in several sections like so:

```ts apps/theme/pageBuilder/index.ts
[
    // Main theme plugin containing the theme definition.
    {
        name: "pb-theme-default"            // Name of your theme.
        type: `pb-theme`                    // Plugin type.
        theme: {}                           // Theme definition.
    } as PbThemePlugin,

    // Register a layout ... you can register multiple layouts inside a single theme.
    {
        name: "pb-page-layout-static"       // Name of the page layout.
        type: "pb-page-layout",             // Plugin type.
        layout: {}                          // Layout definition.
    } as PbPageLayoutPlugin
]
```

As you can see from the example above, we export two plugins that make the default Page Builder theme.
Let's discuss them in detail in the next section.

## Page Builder Theme Plugins

### [`pb-theme`](/docs/{version}/page-builder/references/plugins#pb-theme) Plugin

This is the plugin that registers the actual theme, together with several other elements.
It contains the following elements:

```ts apps/theme/pageBuilder/index.ts
{
        name: "pb-theme-default",
        type: "pb-theme",
        theme: {
            colors: {
                primary: "var(--webiny-theme-color-primary)",
                secondary: "var(--webiny-theme-color-secondary)",
                // (...)
            },
            elements: {
                button: {
                    types: [
                        { className: "", label: "Default" },
                        { className: "primary", label: "Primary" },
                        // (...)
                    ]
                }
            }
        }
    }
```

Learn more about the specific theme components:

- [colors](/docs/{version}/page-builder/theming/colors)
- [elements](/docs/{version}/page-builder/theming/elements)

### [`pb-page-layout`](/docs/{version}/page-builder/references/plugins#pb-page-layout) Plugin

The layout plugin is used to register layouts. Layouts are used to render the page content.
The most commonly used layout includes a header at the top, page content in the center and a footer at the very bottom.

In the Page Builder, you can have multiple layouts. For example, one layout where the page is rendered in two-columned, the other in three columned layout.

Here is an example of how to register a layout:

```ts apps/theme/pageBuilder/index.ts
import StaticLayout from "./layouts/Static";

(...)

{
    name: "pb-page-layout-static",
    type: "pb-page-layout",
    layout: {
        name: "static",
        title: "Static page",
        component: StaticLayout
    }
} as PbPageLayoutPlugin
```

Each layout has the following attributes:

- `name` - an internal identifier for that particular layout
- `type` - plugin type, must be set to `pb-page-layout`
- `layout` - an object containing the layout `name`, `title`, and a React `component` used to render the layout

To introduce multiple layouts all you need to do is register multiple `pb-page-layout` plugins.
It's also quite easy to register a new layout into the default theme:

```js apps/admin/src/plugins/index.ts
// Some code is removed for the sake of brevity.
import { plugins } from "@webiny/plugins";
import CustomLayout from "../custom-layout";

plugins.register([
  // (...)
  {
    name: "pb-page-layout-my-custom-layout",
    type: "pb-page-layout",
    layout: {
      name: "my-custom-layout",
      title: "My layout",
      component: CustomLayout
    }
  }
  // (...)
]);
```

After saving the change, your newly registered layout should appear in the Categories form, in the Page Builder application as shown below:

![New custom layout](./images/webiny-page-category.png)

<Alert type="info">

In the Page Builder, layouts are linked to page categories. When you create a page category, say "Blog", you need to select a layout.
Each page created inside the "Blog" category, by default, use that layout. However, inside the page settings, you can overwrite that on a per-page basis if you need to.

</Alert>

To learn more about layouts and how to compose one, have a look at the [layouts article](/docs/{version}/page-builder/theming/layouts).
