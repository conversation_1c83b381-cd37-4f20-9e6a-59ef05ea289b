---
id: aafeab1a
title: Extending Entities
description: Learn how to continue developing on top of the generated application code, by extending the initially generated entity with new attributes.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU’LL LEARN">

- how to continue developing on top of the generated application code
- GraphQL API: how to extend the initially created entity with new attributes
- Admin Area: how to extend the initially created form with new form fields

</Alert>

<Alert type="info" title="Can I use this?">

In order to follow this tutorial, you must use Webiny version **5.9.0** or greater.

</Alert>

## Overview

Initially, the [`CarManufacturer`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/api/code/graphql/src/plugins/scaffolds/carManufacturers/entities/CarManufacturer.ts) entity that was created in the scaffolding process only contains two attributes: [`title`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/api/code/graphql/src/plugins/scaffolds/carManufacturers/entities/CarManufacturer.ts#L18) and [`description`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/api/code/graphql/src/plugins/scaffolds/carManufacturers/entities/CarManufacturer.ts#L19). Of course, these are just starter attributes and we are certainly encouraged to extend the entity with additional ones.

Let's see how we can add the new `isPopular` boolean attribute to the [`CarManufacturer`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/api/code/graphql/src/plugins/scaffolds/carManufacturers/entities/CarManufacturer.ts) entity, and then, via the new Admin Area application module, enable logged-in users to modify its value, by adding a simple [`Switch`](https://storybook.webiny.com/?path=/story/components-switch--usage) form field to the Car Manufacturers form.

## GraphQL API

Starting from the GraphQL API, let's open our [`CarManufacturer`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/api/code/graphql/src/plugins/scaffolds/carManufacturers/entities/CarManufacturer.ts#L20) entity and simply define the new attribute on it (parts of code removed for brevity):

```ts apps/api/graphql/src/plugins/scaffolds/carManufacturers/entities/CarManufacturer.ts
(...)

export default new Entity<CarManufacturerEntity>({
    table,
    name: "CarManufacturers",
    timestamps: false,
    attributes: {
        PK: { partitionKey: true },
        SK: { sortKey: true },
        id: { type: "string" },
        title: { type: "string" },
        description: { type: "string" },
        isPopular: { type: "boolean" },
        createdOn: { type: "string" },
        savedOn: { type: "string" },
        createdBy: { type: "map" },
        webinyVersion: { type: "string" }
    }
});
```

Notice how we're passing the [`CarManufacturerEntity`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/api/code/graphql/src/plugins/scaffolds/carManufacturers/types.ts#L6) interface upon instantiating the `Entity` class. Since the interface is essentially listing all attributes that our entity consists of, let's update it as well:

```ts apps/api/graphql/src/plugins/scaffolds/carManufacturers/types.ts

(...)

export interface CarManufacturerEntity {
    PK: string;
    SK: string;
    id: string;
    title: string;
    description?: string;
    isPopular?: boolean;
    createdOn: string;
    savedOn: string;
    createdBy: Pick<SecurityIdentity, "id" | "displayName" | "type">;
    webinyVersion: string;
}

(...)
```

Finally, let's update our `CarManufacturer` GraphQL types in our schema, located in the [`typeDefs.ts`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/api/code/graphql/src/plugins/scaffolds/carManufacturers/typeDefs.ts) file:

```ts apps/api/graphql/src/plugins/scaffolds/carManufacturers/typeDefs.ts
export default /* GraphQL */ `
    type CarManufacturer {
        id: ID!
        title: String!
        description: String
        isPopular: Boolean
        createdOn: DateTime!
        savedOn: DateTime!
        createdBy: CarManufacturerCreatedBy
    }

    input CarManufacturerCreateInput {
        title: String!
        description: String
        isPopular: Boolean
    }

    input CarManufacturerUpdateInput {
        title: String
        description: String
        isPopular: Boolean
    }

    (...)
`;
```

<Alert type="success">

If you've run the two [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) commands, mentioned in the [previous section](/docs/{version}/admin-area/new-app-tutorial/get-started#using-the-watch-command), the performed application code changes should be automatically rebuilt and redeployed into the cloud.

</Alert>

Once we've done all of the above shown changes, we can switch to our Admin Area application, and continue by updating the Car Manufacturers form.

## Admin Area Application

In the [`CarManufacturersForm`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/apps/admin/code/src/plugins/scaffolds/carManufacturers/views/CarManufacturersForm.tsx) React component, let's add a new [`Switch`](https://storybook.webiny.com/?path=/story/components-switch--usage) form element, which will enable logged-in users to mark a particular car manufacturer as popular or not popular (parts of code removed for brevity):

```tsx apps/admin/src/plugins/scaffolds/carManufacturers/views/CarManufacturersForm.tsx
(...)

// We've imported the Switch component from the `@webiny/ui` package.
import { Switch } from "@webiny/ui/Switch";

(...)

return (
    <Form data={carManufacturer} onSubmit={onSubmit}>
        {({ data, form, Bind }) => (
            <SimpleForm>
                {loading && <CircularProgress />}
                <SimpleFormHeader title={data.title || "New Car Manufacturer"} />
                <SimpleFormContent>
                    <Grid>
                        (...)

                        <Cell span={12}>
                            <Bind
                                name="isPopular"
                            >
                                <Switch
                                    label={"Popular Car Manufacturer"}
                                    description={"Is this car manufacturer popular?"}
                                />
                            </Bind>
                        </Cell>
                    </Grid>
                </SimpleFormContent>
                <SimpleFormFooter>
                    <ButtonDefault onClick={cancelEditing}>Cancel</ButtonDefault>
                    <ButtonPrimary onClick={form.submit}>Save Car Manufacturer</ButtonPrimary>
                </SimpleFormFooter>
            </SimpleForm>
        )}
    </Form>
);
```

Once we've updated the [`CarManufacturersForm`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/apps/admin/code/src/plugins/scaffolds/carManufacturers/views/CarManufacturersForm.tsx#L75-L84) React component, the form itself should look like the following:

![Updated Car Manufacturers Form](./assets/extending-entities/form-updated.png)

Finally, we need to add the `isPopular` field to all relevant GraphQL query and mutation operations. This ensures that we're both sending and receiving the `isPopular` value while interacting with the GraphQL API.

Adding the `isPopular` field can be easily done by updating the [`CAR_MANUFACTURER_FIELDS_FRAGMENT`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/apps/admin/code/src/plugins/scaffolds/carManufacturers/views/hooks/graphql.ts#L3) GraphQL fragment, located in the [`graphql.ts`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area/apps/admin/code/src/plugins/scaffolds/carManufacturers/views/hooks/graphql.ts#L3) file:

```ts apps/admin/src/plugins/scaffolds/carManufacturers/views/hooks/graphql.ts
import gql from "graphql-tag";

export const CAR_MANUFACTURER_FIELDS = /* GraphQL */ `
  fragment CarManufacturerFields on CarManufacturer {
    id
    title
    description
    isPopular
    createdOn
    savedOn
    createdBy
  }
`;

(...)
```

With this final change in place, we should be able to edit the **isPopular** attribute for each car manufacturer entry. To test this, we can simply open an existing entry, try marking it as popular, and submitting the form. If everything was done correctly, the form submission should be successful.

![Updated Car Manufacturers Form](./assets/extending-entities/popular-car-manufacturer-saved.png)

## FAQ

### I see DynamoDB Toolbox is being used for interacting with Amazon DynamoDB. Can I use a different library?

On the GraphQL API side, you might have noticed we're using [DynamoDB Toolbox](https://github.com/jeremydaly/dynamodb-toolbox), which is a neat little library that makes interaction with DynamoDB a bit easier. But, note that if you wanted, you could easily replace it with the default [AWS DynamoDB Document Client](https://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/dynamodb-example-document-client.html) or maybe some other preferred library.

### Can I bring my own NPM libraries?

You are free to bring your own preferred NPM libraries.

### How can I perform data validation?

For data validation in general, we recommend you check out the [`@webiny/validation`](https://github.com/webiny/webiny-js/tree/next/packages/validation) library. It's easy to use, provides a plethora of different data validation rules, and also enables you to expand it with your own.

### What about security (authentication and authorization)?

Because this is not something we can effectively predict, the generated application code does not include any authentication and authorization logic. But luckily, with a couple of [built-in utilities](/docs/{version}/overview/features/security), this is not too hard to implement.
