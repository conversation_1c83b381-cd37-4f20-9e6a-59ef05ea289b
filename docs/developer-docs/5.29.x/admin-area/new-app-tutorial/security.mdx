---
id: aafeab1b
title: Security overview
description: Learn how you can secure your applications.
---

import { Al<PERSON> } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- what are security permissions and how to use them in code
- how to add custom permissions UI to permissions editor in Admin Area

</Alert>

<Alert type="info" title="Can I use this?">

In order to follow this tutorial, you must use Webiny version **5.9.0** or greater.

</Alert>

<Alert type="info">

Learn more about the Webiny Security Framework in the [Security Framework](/docs/{version}/core-development-concepts/security-framework/introduction) key topics section.

</Alert>

## Overview

In the previous two sections, we've seen how we can quickly extend our GraphQL API and Admin Area application, by creating a standalone Car Manufacturers module.

In this tutorial, we continue working on our module by adding a layer of security around it. With it, we want to achieve the following:

1. prevent users from executing GraphQL API queries and mutations, if they don't have the necessary security permissions
2. prevent users from accessing modules we've added into the Admin Area application, if the user doesn't have the necessary security permissions

Note that in order to implement both behaviors, we are relying on **security permissions**, which is the first step of implementing a security layer for our application, and is the first topic we cover in this introduction section.

<Alert type="info">

If you want to learn more about the main GraphQL API and how it works on the cloud infrastructure level, check out the [GraphQL Requests](/docs/{version}/architecture/api/graphql-requests) page of the [Cloud Infrastructure - API](/docs/{version}/architecture/api/overview) key topics section.

</Alert>

<Alert type="info">

The code that we cover in this tutorial can also be found in our [GitHub examples repository](https://github.com/webiny/webiny-examples/tree/master/extend-admin-area-security).

</Alert>

## Security Permissions

> It all starts with security permissions.

On the code level, security permissions are just plain JavaScript objects, which allow us to perform identity [authorization](https://en.wikipedia.org/wiki/Authorization). In other words, these objects contain information that tells us which resources an identity (for example, a logged-in user) can access, and which it cannot.

"Can this user access this GraphQL query?", "Can this user edit this particular piece of data?", or "Can this user view this particular piece of user interface?", are just some questions that security permission objects can answer for us.

Except for the required `name` property, these objects don't follow any strict structure, which means that while we're developing our custom application, we can structure them in any way that fits our needs and business logic.

A simple security permission object might look something like the following:

```js
{
    // "name" property is required, the rest are optional.
    name: "my.custom.permission.object",
    canRead: true,
    canWrite: true,
    hasAccessToSomethingGreat: "maybe",
    canAccessUntil: "2025-06-01",
    somethingNested: {
        someRule: "here"
    }
}
```

## Assigning Security Permissions to Identities

Within Admin Area application's security model, security permissions are assigned to identities via security groups.

Every identity (for example, a logged-in user) belongs to **exactly one** security group. But, note that a single security group can have multiple security permissions objects assigned to it, allowing or preventing access across multiple Webiny and your custom applications.

This can be seen while creating a new security group via the security groups module.

![Security Groups Module](./assets/security/groups-module.png)

By default, we get to specify permissions for every installed Webiny application:

![Only a Single Security Group Allowed](./assets/security/groups-permissions.png)

Making changes in one or more of these sections will, internally, result in one or more permission objects to be created. And, once we submit the form, all of the different permission objects that were created in the process, are sent to the GraphQL API and stored in the database (along with other general security group information - name, slug, and description).

Ultimately, no matter if we're performing authorization on the API side or in the UI, via a couple of built-in code utilities, we can retrieve identity's security group and all of the permissions it contains. With that data, we can then determine whether a user has access to a particular API resource or a section in the UI (more on this in the following sections).

## Creating Custom Security Permissions

By adding a new permissions section in the security group form that we've seen above, we can allow logged-in users to manually toggle access for our own custom features, for all identities that are linked with the particular security group.

Continuing with the car manufacturers example that we've used in previous tutorials, we could add a new Car Manufacturers section, and offer a couple of different options to the logged-in user:

![Added Security Permissions Group](./assets/security/added-security-permissions-group.png)

To achieve this, within the Admin Area application, we need to register the following [`PermissionRendererPlugin`](https://github.com/webiny/webiny-js/blob/v5.10.0/packages/app-admin/src/plugins/PermissionRendererPlugin.ts#L19) plugin:

```tsx apps/admin/src/plugins/scaffolds/carManufacturers/permissions.tsx
import React, { useCallback, useMemo } from "react";
import { PermissionRendererPlugin } from "@webiny/app-admin/plugins/PermissionRendererPlugin";

// Components for working with forms:
import { Form } from "@webiny/form";
import { Select } from "@webiny/ui/Select";
import { Switch } from "@webiny/ui/Switch";

// UI React Components - let's make it look nice:
import { Grid, Cell } from "@webiny/ui/Grid";
import { AccordionItem } from "@webiny/ui/Accordion";
import { ReactComponent as CarManufacturersIcon } from "./assets/directions_car-24px.svg";
import { PermissionInfo } from "@webiny/app-admin/components/Permissions";

const PERMISSION_NAME = "car-manufacturers";

export default new PermissionRendererPlugin({
  render(props) {
    // `value` represents an array of all permission objects selected for the
    // security group we're currently editing. To apply changes to the `value`
    // array, we use the provided `onChange` callback.
    const { value, onChange } = props;

    // Callback that gets triggered whenever a form element has changed.
    // If needed, additional object manipulations can be performed too.
    const onFormChange = useCallback(
      data => {
        // Let's filter out the `car-manufacturer` permission object.
        // It's just easier to build a new one from scratch.
        const newPermissions = value.filter(item => item.name !== PERMISSION_NAME);

        // We only want the permissions object to end up in the `value` array if
        // we have a value in `rwd` or `specialFeature` properties.
        if (data.rwd || data.specialFeature) {
          newPermissions.push(data);
        }

        // Finally, call the `onChange` callback to assign the permissions
        // object into the `value`.
        onChange(newPermissions);
      },
      [value]
    );

    // Set up default form data, which happens once the security group data
    // has been retrieved from the GraphQL API.
    const defaultFormData = useMemo(() => {
      return value.find(item => item.name === PERMISSION_NAME) || { name: PERMISSION_NAME };
    }, [value]);

    // We are using a couple of different React components to get the job done:
    // - for a nicer UI - AccordionItem, Grid, Cell, and PermissionInfo components
    // - for working with forms - Form, Bind, Select, and Switch components
    return (
      <AccordionItem
        icon={<CarManufacturersIcon />}
        title={"Car Manufacturers"}
        description={"Manage Car Manufacturer app access permissions."}
      >
        <Form data={defaultFormData} onChange={onFormChange}>
          {({ Bind }) => (
            <Grid>
              <Cell span={6}>
                <PermissionInfo title={"Access Level"} />
              </Cell>
              <Cell span={6}>
                <Grid>
                  <Cell span={12}>
                    <Bind name={"rwd"}>
                      <Select label={"Access Level"}>
                        <option value={""}>{"No Access"}</option>
                        <option value={"r"}>{"Read"}</option>
                        <option value={"rw"}>{"Read, write"}</option>
                        <option value={"rwd"}>{"Read, write, delete"}</option>
                      </Select>
                    </Bind>
                  </Cell>
                  <Cell span={12}>
                    <Bind name={"specialFeature"}>
                      <Switch label={"Has access to a special feature"} />
                    </Bind>
                  </Cell>
                </Grid>
              </Cell>
            </Grid>
          )}
        </Form>
      </AccordionItem>
    );
  }
});
```

<Alert type="success">

In order to actually compile the code changes we're about to make and see them in browser, we need to run the following Webiny CLI command:

```bash
yarn webiny watch apps/admin --env dev
```

To learn more, check out the [Use the Watch Command](/docs/{version}/core-development-concepts/basics/watch-command) guide.

</Alert>

Feel free to copy and paste the shown code into your project and use it as a starting point. When doing that, please note the following:

- the `permissions.tsx` file is a new file that we created, it wasn't initially there
- the code uses a custom [`directions_car-24px.svg`](https://github.com/webiny/webiny-examples/tree/master/extend-admin-area-security/apps/admin/code/src/plugins/scaffolds/carManufacturers/assets) SVG icon which you'll also want to copy into your project (otherwise the build will fail)

After you do that, just make sure that the plugin is actually imported in the [`apps/admin/code/src/plugins/scaffolds/carManufacturers/index.ts`](https://github.com/webiny/webiny-examples/blob/master/extend-admin-area-security/apps/admin/code/src/plugins/scaffolds/carManufacturers/index.ts) file:

```diff-js packages/car-manufacturers/admin-app/src/index.ts
import menus from "./menus";
import routes from "./routes";
+ import permissions from "./permissions";

+ export default [menus, routes, permissions];
```

By completing this step, we've added the new Car Manufacturers permissions section in the security group form. logged-in users can now create new (or extend existing) security groups, and give access to our new Car Manufacturers module. For example, one security group can allow only reading car manufacturers data, while the other one may allow all three primary actions - read, write, and delete, and even allow access to the imaginary special feature.

Now that we have this new security permissions section, let's create the new Car Manufacturers security group, that enables full access to our new Car Manufacturers module. We'll need this security group in order to perform some manual testing down the road.

![New Car Manufacturers Security Group](./assets/security/new-security-group.png)

Note that, at this point, nothing will actually happen for identities that have or don't have these newly added permissions in their security group. We still need to implement the actual authorization logic, both on the GraphQL API and Admin Area side, which we cover in the following sections.

### Content Locales

While creating the new Car Manufacturers security group, you might have noticed you immediately get to define access on a locale level.

![Define Access on a Locale Level](./assets/security/content-locale-section.png)

This is because, out of the box, the Admin Area application is designed to be a multi-locale system, and as such, it gives you the ability to specify which locales an identity can access. Note that you cannot specify different permissions for different locales. All of the permissions you specify in one or more permission sections will be applied to all allowed locales.

For now, know that we will need to take this into account while performing the authorization checks in the following section. Also, for purposes of this tutorial, upon creating the new Car Manufacturers security group, we can select **All Locales**, to allow access across all locales.

## FAQ

### Can I assign multiple security groups to a single identity (user)?

No, you can't. Every identity can be part of exactly one security group. If you are in a situation where you need to assign multiple security groups to an identity, you'll need to create a new security group, that consist of all permissions that are assigned to the initial two.

### What does the `rwd` stand for?

It stands for read, write, and delete.

### Can I define my security permission objects differently from what it's used this tutorial?

Yes. The `rwd` and `hasSpecialFeature` properties are not required and you can define your security permission objects in any way you like or need. For example, if you don't prefer the `rwd` approach we showed here, you could create standalone `hasRead`, `hasWrite`, and `hasDelete` properties instead.

We just thought the `rwd` approach is lightweight and simple to understand.

### Is it possible to create a checkbox which would immediately allow full access to my module?

Sometimes, permissions sections may grow in size. And in that case, it might be easier to have a simple checkbox that would enable full access to your module, instead of having the user click through all of the available permissions.

So, if you've reached this point, then it might make sense to extend your security permissions object, with a special boolean `fullAccess` property, and take that into account while performing GraphQL API and Admin Area application authorization (covered in following sections of this tutorial).
