---
id: 0e9c3ae0
title: Custom Routes
description: Learn how to add new routes in the Admin app.
---

import { CanIUseThis } from "@/components/CanIUseThis";
import { WhatYouWillLearn } from "@/components/WhatYouWillLearn";

<CanIUseThis since={"5.29.0"} />

<WhatYouWillLearn>

- how to add new Admin app routes

</WhatYouWillLearn>

## Add a Route

The simplest route registration looks like this:

```diff-tsx apps/admin/src/App.tsx
import React from "react";
+import { Admin, Plugins, AddRoute, Layout } from "@webiny/app-serverless-cms";
import { Cognito } from "@webiny/app-admin-users-cognito";
import "./App.scss";

export const App = () => {
    return (
      <Admin>
        <Cognito />
+       <Plugins>
+         <AddRoute path={"/new-route"}>
+           <Layout>
+             <div>Hello from new route!</div>
+           </Layout>
+         </AddRoute>
+       </Plugins>
      </Admin>
    );
};
```

`Plugins` element is necessary to make sure your route is registered after all the other application routes.

`Layout` component is the default Admin app layout, which contains header, navigation, etc. This allows you to control whether you want to use the Admin app layout or not. For example, a login screen doesn't have a layout, and if you want to build such routes, you can skip adding the Layout container.

`AddRoute` component simply registers a route internally, but it doesn't immediately render it. Rendering of routes is handled by Webiny, so think of this as a configuration, and Webiny will decide when the route will be rendered.
