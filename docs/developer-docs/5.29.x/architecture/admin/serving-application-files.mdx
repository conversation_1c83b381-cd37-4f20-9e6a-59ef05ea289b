---
id: aafeab35
title: Serving Application Files
description: Learn how are React application files served via the deployed cloud infrastructure.
---

import { Alert } from "@/components/Alert";
import servingFiles from "./assets/serving-application-files/webiny_admin_page_visit.png";

<Alert type="success" title="What you’ll learn">

- how are React application files served via the deployed cloud infrastructure

</Alert>

## Diagram

<Alert type="info">

For brevity, the diagram doesn't include network-level cloud infrastructure resources, like region, VPC, availability zones, and so on. Check out the [Deployment Modes](/docs/{version}/architecture/deployment-modes/introduction) section if you're interested in that aspect of the deployed cloud infrastructure.

</Alert>

<Image
  src={servingFiles}
  title="Webiny Cloud Infrastructure - Admin Area - Serving Application Files"
  shadow={false}
/>

## Description

The diagram shows how HTTP requests travel through the deployed cloud infrastructure and how files are returned back to the client. This happens repeatedly as the user opens and navigates through the **Admin Area** React application. Note that, when talking about the API HTTP requests that the **Admin Area** React application issues, those are still issued to the Amazon CloudFront distribution that's deployed as part of the [API](/docs/{version}/architecture/api/overview) project application.

The flow consists of the following three steps:

1. The HTTP request first reaches the Amazon CloudFront <diagram-letter>A</diagram-letter>.
2. The request is forwarded to the Amazon S3 bucket <diagram-letter>B</diagram-letter>.
3. Amazon CloudFront <diagram-letter>A</diagram-letter> receives the file from Amazon S3 <diagram-letter>B</diagram-letter>, caches it accordingly, and returns it back to the client.

## FAQ

### Are there any Amazon CloudFront caching rules in place?

All of the files that are served from the `/static/*` path are cached for 30 days. The rest is cached for five minutes. If need be, this can be additionally adjusted.

### What is the `static` folder anyways?

The Admin Area React application is actually a [create-react-app](https://create-react-app.dev/) application, which, upon making a production build, places all of the static files (JS, CSS, images, ...) into this folder.
