---
id: gdkhsbfgiy23
title: User Project Upgrade
description: Learn about how to do a project upgrade
---

import {<PERSON><PERSON>} from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- what is Webiny project upgrade
- what are the steps to upgrade a project

</Alert>

## Overview

As a part of our upgrade process, we provide a way to update your codebase on each new release - at least what we can do automatically.

To start the project upgrade use the `yarn webiny upgrade` command.

<Alert type="warning">
  The upgrade process can only update one minor version at a time. So you can upgrade from 5.40.x to 5.41.x, but not from 5.40.x to 5.42.x.
</Alert>

## Steps

When a user runs the `yarn webiny upgrade` command, there are multiple steps depending on what are we upgrading in that version.

There might even be some breaking changes, so we need to notify the user about them.

### 1. Notify the User about Breaking Changes

The first step is to notify the user about breaking changes:
```text
webiny warning: Note that Webiny 5.xx.x introduces potential breaking changes!
Before continuing, please review the upgrade guide located at https://webiny.link/upgrade/5.xx.x.

I have read the upgrade guide and I am ready to proceed with the upgrade (y/N):
```

User needs to press `y` to continue with the upgrade process.

Note that this step will not show if there are no breaking changes in the version user is upgrading to.

### 2. Check User Project Dependency Versions Against the Webiny Dependency Versions

Next step, which will always be executed, is to check if the user project dependencies are in sync with the Webiny dependencies. If they are not, the user will be prompted to update them.

<Alert>
  
This does not mean that we use newer versions of the dependencies than our users do in their projects.

We ship Webiny with the dependencies that are working with our code. If it is possible, our users should keep their dependencies in sync with Webiny ones to avoid any unexpected problems.
  
</Alert>

```text
webiny warning: Found dependencies that are out of sync. Please sync them before continuing with the upgrade process...
package.json files which have dependencies out of sync:
 - /packages/apiLib/package.json
 - /myApp/reactLib/package.json

Dependencies out of sync:
- react: 17.0.0 (18.2.5)
- fastify: 4.0.0 (4.2.9)

Do you want us to upgrade those dependencies? (y/N):
```

User is encouraged to press `y` to update all dependencies in their project.

If user chooses not to upgrade the dependencies, the upgrade process will prompt once more:

```text
We strongly recommend you update the dependencies.
Failing to do so may result in unexpected problems.
Do you still want to continue with the project upgrade and skip updating dependencies? (y/N)
```

If user chooses to continue without updating the dependencies, the upgrade process will continue, but it is not recommended.

## Version Specific Steps

All steps after the dependency check are specific to the version user is upgrading to.
