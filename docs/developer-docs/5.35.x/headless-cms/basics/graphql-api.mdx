---
id: aafea826
title: GraphQL API Overview
description: Learn what is the Headless CMS GraphQL API, what are its main characteristics, and how to access it.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- what is the Headless CMS GraphQL API
- what are its main characteristics
- how to access it

</Alert>

## Overview

The Headless CMS Webiny application comes with a fully-fledged GraphQL API, which you use in order to perform GraphQL queries and mutations on Headless CMS content models, groups, and entries.

The easiest way to explore it and try different things is via the API Playground, which's part of the Webiny Administration Area. To access it, simply open the main menu on the left side of the screen, and click on the API Playground:

![Access API Playground via Main Menu](./assets/graphql-api/api_playground_from_main_menu.png)

<Alert type="info">

If you want to learn how to use the Headless CMS GraphQL API via the built-in API Playground or
programmatically, via a GraphQL client, make sure to check out the [Using the GraphQL
API](/docs/{version}/headless-cms/basics/using-graphql-api) guide.

</Alert>

## Main Characteristics

### GraphQL API Types

The Headless CMS GraphQL API comes in three different types - **manage**, **preview** and **read**. Each type is served via a separate URL and has a specific purpose.

<Alert type="info">

Skip to the [The GraphQL API URL
Structure](#the-graph-ql-api-url-structure)
to learn more about the Headless CMS GraphQL API's URL structure.

</Alert>

#### Manage

The manage GraphQL API is here for management purposes. In other words, it provides the necessary queries and mutations for managing your content models, groups, and entries. It is heavily utilized by the Headless CMS module in the Webiny Administration Area.

Except for the fact that this GraphQL API type allows you to create, edit and publish existing content entries, another specific aspect of it is that it returns both published and unpublished content entries. This is intentional, because, as a content manager, you need to be able to access all of the data, not just published.

#### Read

Unlike the manage GraphQL API, which is used by content managers, the **read** API is used by data consumers - like your public website, a web or mobile application, and others. The API consists of multiple query operations that enable you to retrieve content entries, previously published via the mentioned **management** GraphQL API.

#### Preview

Finally, there is the **preview** GraphQL API type, which is used for previewing purposes. Imagine a website that's only available to the internal team, that wants to see how a particular page or a section of your website will look like, once a specific content entry is actually published. This internal website is, usually, available under a protected URL, and should always interact with the **preview** GraphQL API.

The GraphQL schema is the same as the **read** GraphQL API's, except the returned data also includes not-yet-published content entries.

#### Summary

The following table clearly shows all of the specific characteristics of each Headless CMS GraphQL API type:

| Behaviour                                                         | Manage              | Read                              | Preview                         |
| ----------------------------------------------------------------- | ------------------- | --------------------------------- | ------------------------------- |
| Allows mutations on <br/>content models, groups,<br/> and entries | ✅                  | ❌                                | ❌                              |
| Allows performing <br/>queries on content entries                 | ✅                  | ✅                                | ✅                              |
| Returns unpublished<br/>content entries                           | ✅                  | ❌                                | ✅                              |
| Usually used in...                                                | Administration area | Production website or application | Internal website or application |

### Multiple Locales Support

Except for the different types, the Headless CMS GraphQL API also supports creating both content models, and entries, in multiple locales. This not only means that you can have your content entries localized for different users, but also that you can design your content models separately for each locale.

For example, you might have a `Personal Document` content model, which for the `en-GB` locale, consists of three fields, and for the `en-US`, only two. Or, you can have the same fields in both, but different input data validation rules defined for each field.

Overall, this brings additional flexibility when designing your content models for different locales.

### Secure

The Headless CMS GraphQL API sits behind a security layer that provides fine-grained access control, both for logged-in, and anonymous users. The following are some of the available options:

- allow users to access content on all, or only specific locales
- allow users to access only specific Headless CMS GraphQL API types (**manage**, **read**, **preview**)
- allow users to access only specific content models, groups, and entries

![Fine-grained Access Controls](./assets/graphql-api/security-cms-form-1.png)

![Fine-grained Access Controls](./assets/graphql-api/security-cms-form-2.png)

## The GraphQL API URL Structure

The Headless CMS GraphQL API is accessed via multiple URL endpoints, each including the GraphQL API type and locale. This is simply because different combinations of the two yield different GraphQL schemas.

For example, the **read** GraphQL API only allows users to perform queries on different content entries. On the other hand, not only the **manage** type allows querying content entries, but also content models, and groups. Furthermore, it also provides the necessary management-related mutation operations. Finally, different locales can have different content models, which, again, means a different GraphQL schema needs to be in place.

The following shows how the Headless CMS GraphQL API URL is structured:

```
https://{yourApiDomain}/cms/{type}/{locale}
```

Note that the Headless CMS GraphQL API is deployed as part of your **API** project application, hence the `yourApiDomain` piece in the URL. A couple of real-world examples:

```bash
# Access "manage" GraphQL API, for the `en-US` locale.
https://random-id-xyz.cloudfront.net/cms/manage/en-US

# Access "read" GraphQL API, for the `de-DE` locale.
https://random-id-xyz.cloudfront.net/cms/read/de-DE

# Access "preview" GraphQL API, for the `it-IT` locale.
https://random-id-xyz.cloudfront.net/cms/preview/it-IT
```

<Alert type="info">

To learn more about the cloud infrastructure deployed as part of the **API** project application,
check out the [Cloud Infrastructure - API](/docs/5.28.x/architecture/api/introduction)
key topic. You can also skip to the [API
Overview](/docs/5.28.x/architecture/api/overview) section, to
immediately see how the Headless CMS GraphQL API fits in.

</Alert>

### Where to Find the URL of the GraphQL API?

You can find the URL of the Headless CMS GraphQL API via one of the following two ways.

#### API Playground

When you access the API Playground via the main menu in the Administration Area, at the top of the screen, in the URL bar, you will be able to see the URL for all three Headless CMS GraphQL API types.

![API Playground](./assets/graphql-api/api-playground.png)

Note that if you want to retrieve the URL for a different locale, you can switch to a different one via the locale selector, located in the top right corner of the screen.

![Switch Locale via the Locale Selector](./assets/graphql-api/api-playground-switch-locale.png)

<Alert type="info">

The locale selector is not visible for systems that have only one active locale.

</Alert>

#### Webiny CLI

The [Webiny CLI](/docs/{version}/core-development-concepts/basics/webiny-cli) offers the `info` command, which will, among other useful information, also return the URLs for all three CMS GraphQL API types. The command is run from your project root, like so:

```bash
# Returns information for the "dev" environment.
yarn webiny info --env dev
```

Running the shown command should produce the following output:

![Find Headless CMS GraphQL API URLs via Webiny CLI](./assets/graphql-api/api-url-webiny-cli.png)

Note that with this method, you will still need to manually replace the `{LOCALE_CODE}` with an actual locale code. A list of all available locales can be found in the Administration Area, in the **Locales** section of the I18N Webiny application.

![Webiny I18N Application - Locales Section](./assets/graphql-api/i18n-locales-section.png)

## FAQ

### When using the API Playground, is my access limited in any way?

You can only access content models, groups, and entries to which you have access, which is based on the security group your user account belongs to. To learn more about how to use the Headless CMS GraphQL API, visit the [Using the GraphQL API](/docs/{version}/headless-cms/basics/using-graphql-api) guide.
