---
id: a4d38492
title: Advanced Content Organization
description: Learn how to organize your content in Webiny using folders.
---

import { <PERSON><PERSON> } from "@/components/Alert";
import YouTube from "@/components/YouTube";

<Alert type="success" title="What You’ll Learn">

- How to organize content inside folders and sub-folders
- How to search for content inside folders

</Alert>

![Advanced Content Organization](./assets/aco.png)

## About

In this article, we'll cover a feature called Advanced Content Organization ("ACO"). This feature allows you to organize your content inside folders and sub-folders. It also allows you to search for content inside those same folders.

The feature is available inside the **Page Builder**, **Headless CMS** and **File Manager** applications.

## How to use

Whenever you access any of the above-mentioned applications you'll see an interface with folders on the left and a list of items and folders on the right. Inside that interface you can create folder and subfolders and then place your content, or assets in case of File Manager, inside those folders.

The goal is to help you organize your content in a way that makes sense to you and your editors. It's also important to note that the organization of content inside ACO doesn't necessarily mean you need to have the content organized the same way on your website. You can use the ACO to organize content around your publishing workflows and editorial teams, while on the website you can organize content around the user experience.

Here is a deeper dive into ACO inside our Page Builder application, but most of the things apply to the other applications as well.

<YouTube id={"A1b7-D0rugE"} />

## Search

ACO helps also when it comes to searching for your content. You'll see a search bar on the top of the interface. When you search for something, the search will be performed only inside the folder you're currently in. If you want to search for content inside all folders, you can click on the **Root** folder button in the folder tree.

This way you can easily scope your search to a particular folder or subfolder and get better results faster.

## Bulk actions

ACO comes with several additional features, one of those features is the bulk action. If in the ACO interface, you select multiple items, you'll see a new menu appear on the top of the interface. This menu allows you to perform bulk actions on the selected items. Actions like build publish, unpublish, delete and move to a folder are supported out of the box. In addition to that, there is also an API you can use to create custom bulk actions.

## Security & Control

What would be the point of having folders if you couldn't control who can access them? That's why we've added a new permission called **Folder Level Permissions**. This permission allows you to control who can access a particular folder. You have the option to allow access to a folder to all users, only to specific users or only to users belonging to a specific team.

![Advanced Content Organization - Folder level permissions](./assets/flp.png)

## Extensibility

The ACO is at the core of our content management applications, it's also extensible. You can create custom bulk actions, add custom basic actions, and remove any of the current actions that are there by default. You can also create custom folder actions and show or hide the columns.
