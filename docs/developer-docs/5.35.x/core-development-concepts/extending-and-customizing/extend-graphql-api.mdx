---
id: aafeaa22
title: Extend GraphQL API
description: Learn how to use GraphQL plugins in order to expand your GraphQL API.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you'll learn">

- the two separate GraphQL APIs that are included by default in every Webiny project
- how to use the [`CmsGraphQLSchemaPlugin`](https://github.com/webiny/webiny-js/blob/v5.35.0/packages/api-headless-cms/src/plugins/CmsGraphQLSchemaPlugin.ts) in order to extend an existing GraphQL API

</Alert>

<Alert type="info">

Use the [`webiny watch`](/docs/{version}/core-development-concepts/basics/watch-command) command to continuously deploy application code changes into the cloud and instantly see them in action. For quick (manual) testing, you can use the built-in [API Playground](/docs/{version}/admin-area/basics/api-playground).

</Alert>

## Introduction

When it comes to HTTP API development, Webiny relies on [GraphQL](https://graphql.org/). In fact, all of the applications that are part of the Webiny Serverless CMS, for example [Page Builder](https://www.webiny.com/serverless-app/page-builder) or [Headless CMS](https://www.webiny.com/serverless-app/headless-cms), are using it in order to enable developers programmatic interaction via a client of their choice, for example a browser.

By default, every Webiny project starts off with two separate GraphQL APIs.

### Default GraphQL API

The **default GraphQL API** (or just **GraphQL API**) is the GraphQL API that's located within the [`api/code/graphql`](https://github.com/webiny/webiny-js/tree/v5.11.0/packages/cwp-template-aws/template/api/code/graphql) folder. All of the Webiny Serverless CMS applications are built on top of it, and, in the same fashion, the API can be extended by developers, if need be.

<Alert type="success">

The [Extend GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api) and [Extend Admin Area](/docs/{version}/admin-area/new-app-tutorial/scaffolding) scaffolds, which can help you extend your GraphQL API in no time, use the same concepts and approaches that are explained in this guide.

</Alert>

### Headless CMS GraphQL API

The **Headless CMS GraphQL API** is a special standalone GraphQL API that is introduced by the Headless CMS application. It's located in the [`api/code/graphql`](https://github.com/webiny/webiny-js/tree/v5.11.0/packages/cwp-template-aws/template/api/code/graphql) folder and, like the default GraphQL API, can also be extended by developers.

<Alert type="info">

Learn more about the [Headless CMS GraphQL API](/docs/{version}/headless-cms/basics/graphql-api).

</Alert>

## Extending GraphQL API

In general, when talking about extending an existing GraphQL API, we're usually referring to one or more of the following:

- adding new [query or mutation](https://graphql.org/learn/queries/) GraphQL operations
- adding new GraphQL [types](https://graphql.org/learn/schema/#object-types-and-fields)
- extending existing GraphQL types with additional [fields](https://graphql.org/learn/queries/#fields)

For example, we might want to add a new, Page Builder-related, `duplicatePage` mutation, that would be responsible for making copies of provided pages. Or, we might just want to add an extra field to the [`PbPage`](https://github.com/webiny/webiny-js/blob/v5.11.0/packages/api-page-builder/src/graphql/graphql/pages.gql.ts#L30) GraphQL type, so that we can store some additional data for each page.

And although, depending on the application and the change we want to perform, some of the steps in the overall GraphQL extension process may differ, in all cases, we will want to start by registering a new [`CmsGraphQLSchemaPlugin`](https://github.com/webiny/webiny-js/blob/v5.35.0/packages/api-headless-cms/src/plugins/CmsGraphQLSchemaPlugin.ts) plugin.

The plugin is registered within your GraphQL API's application code. For example, if we wanted to extend the default GraphQL API, we would create the plugin inside of the [`api/code/graphql/src/plugins`](https://github.com/webiny/webiny-js/tree/v5.11.0/packages/cwp-template-aws/template/api/code/graphql/src/plugins) folder, and register it in the [`api/code/graphql/src/index.ts`](https://github.com/webiny/webiny-js/tree/v5.11.0/packages/cwp-template-aws/template/api/code/graphql/src/index.ts) entrypoint file. On the other hand, if we wanted to extend the Headless CMS GraphQL API, we would create the plugin inside of the [`api/code/graphql/src/plugins`](https://github.com/webiny/webiny-js/tree/v5.11.0/packages/cwp-template-aws/template/api/code/graphql/src/index.ts) folder, and register it in the [`api/code/graphql/src/plugins/index.ts`](https://github.com/webiny/webiny-js/tree/v5.11.0/packages/cwp-template-aws/template/api/code/graphql/src/index.ts) entrypoint file.

<Alert type="info">

The [`CmsGraphQLSchemaPlugin`](https://github.com/webiny/webiny-js/blob/v5.35.0/packages/api-headless-cms/src/plugins/CmsGraphQLSchemaPlugin.ts) plugin is part of the [`@webiny/api-headless-cms`](https://github.com/webiny/webiny-js/tree/v5.35.0/packages/api-headless-cms) package, which can also be used to create new standalone GraphQL APIs.

</Alert>

The following is an example of a simple [`CmsGraphQLSchemaPlugin`](https://github.com/webiny/webiny-js/blob/v5.35.0/packages/api-headless-cms/src/plugins/CmsGraphQLSchemaPlugin.ts) plugin that extends the default GraphQL API with a new `listBooks` query.

```ts api/code/graphql/src/plugins/books.ts
import { CmsGraphQLSchemaPlugin } from "@webiny/api-headless-cms";

// Make sure to import the `Context` interface and pass it to the `CmsGraphQLSchemaPlugin`
// plugin. Apart from making your application code type-safe, it will also make the
// interaction with the `context` object significantly easier.
import { Context } from "~/types";

export default new CmsGraphQLSchemaPlugin<Context>({
  // Extend the `Query` type with the `Book` type and `listBooks` query field,
  // which returns a list of all books previously saved in the database.
  typeDefs: /* GraphQL */ `
    type Book {
      title: String
      description: String
    }
    extend type Query {
      # Returns a list of all users
      listBooks: [Book]
    }
  `,
  // In order for the `listBooks` to work, we also need to create a resolver function.
  resolvers: {
    Query: {
      listBooks: async (_, args, context) => {
        // In a real life application, these would be loaded from the database.
        const books = [
          { title: "First book", description: "This is the first book." },
          { title: "Second book", description: "This is the second book." }
        ];

        // Finally, return the list of books using the `ListResponse` class instance.
        return books;
      }
    }
  }
});
```

Having [the plugin](https://github.com/webiny/webiny-examples/blob/master/extend-graphql-api/list-books/api/code/graphql/src/plugins/books.ts#L8) registered within the default GraphQL API's application code (in the mentioned [`api/code/graphql/src/index.ts`](https://github.com/webiny/webiny-examples/blob/master/extend-graphql-api/list-books/api/code/graphql/src/index.ts#L27) entrypoint file), we should be able to see and execute the `listBooks` query, for example via the [API Playground](/docs/{version}/admin-area/basics/api-playground):

```graphql
{
  listBooks {
    title
    description
  }
}
```

Executing the query should give us the following result:

![Executing the new listBooks Query via API Playground](./assets/extend-graphql-api/list-books-query.png)

<Alert type="success">

To extend your default GraphQL API in no time, make sure to try the [Extend Admin Area](/docs/{version}/admin-area/new-app-tutorial/scaffolding) and [Extend GraphQL API](/docs/{version}/core-development-concepts/scaffolding/extend-graphql-api) scaffolds.

</Alert>

## Additional Related Examples

For more concrete examples, you can also visit the following guides which explain how to extend GraphQL types and operations that belong to different Webiny applications:

- [Page Builder](/docs/{version}/page-builder/extending/extend-graphql-api)
- [Headless CMS](/docs/{version}/headless-cms/extending/extend-graphql-api)
