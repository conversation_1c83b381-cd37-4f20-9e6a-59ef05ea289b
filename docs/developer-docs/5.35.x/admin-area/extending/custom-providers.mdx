---
id: 0e9c45c8
title: Custom Providers
description: Learn how to add new React context providers in the Admin app.
---

import { CanIUseThis } from "@/components/CanIUseThis";
import { WhatYouWillLearn } from "@/components/WhatYouWillLearn";

<CanIUseThis since={"5.35.0"} />

<WhatYouWillLearn>

- how to add new Admin app context providers

</WhatYouWillLearn>

## Add a Context Provider

To register a context provider, use the `createProviderPlugin` utility.

```diff-tsx apps/admin/src/App.tsx
import React from "react";
+ import { Admin, createProviderPlugin } from "@webiny/app-serverless-cms";
import { Cognito } from "@webiny/app-admin-users-cognito";
import "./App.scss";

+ const MyProviderPlugin = createProviderPlugin(Component => {
+   return function MyProvider({ children }) {
+     return (
+       <MyContextProvider>
+         <Component>{children}</Component>
+       </MyContextProvider>
+     );
+   };
+ });

export const App = () => {
    return (
      <Admin>
        <Cognito />
+       <MyProviderPlugin />
      </Admin>
    );
};
```

Providers can be used not only to register a global React context provider, but also to intercept the Admin app rendering and delay it until some condition is met.
