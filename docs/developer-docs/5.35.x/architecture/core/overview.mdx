---
id: aafeaa27
title: Overview
description: What are the necessary cloud infrastructure resources on which the Core project application relies on.
---

import { Alert } from "@/components/Alert";
import coreOverview from "./assets/overview/webiny_core_app.png";

<Alert type="success" title="What you’ll learn">

- what is the **Core** project application
- what are the necessary cloud infrastructure resources on which the **API** project application relies on

</Alert>

## The Core Project Application

The **Core** project application represents your project's core cloud infrastructure resources. Essentially, these are your stateful resources, like [Amazon DynamoDB](https://aws.amazon.com/dynamodb/) for storing data, [Amazon S3](https://aws.amazon.com/s3/) for storing files, and [Amazon Cognito](https://aws.amazon.com/cognito/) for storing user information and authentication. Also, with the **Amazon DynamoDB and Amazon ElasticSearch** setup, the project application also deploys an ElasticSearch cluster via the [Amazon Elasticsearch Service](https://aws.amazon.com/opensearch-service/).

<Alert type="info">

Learn more about the different setups in the [introduction](/docs/{version}/architecture/introduction#different-setups) section.

</Alert>

The **Core** project application also deploys an [Amazon EventBridge](https://aws.amazon.com/eventbridge/), used for communication between different resources.

## Diagram

<Alert type="info">

For brevity, the diagram doesn't include network-level cloud infrastructure resources, like region, VPC, availability zones, and so on.

</Alert>

<Image src={coreOverview} title="Webiny Cloud Infrastructure - Core - Overview" shadow={false} />

## Description

The above diagram gives an overview of the complete cloud infrastructure that is deployed by your project's **Core** project application.

### 1. Amazon Cognito

Although Webiny can work with multiple identity providers, by default, it works with Amazon Cognito <diagram-letter>A</diagram-letter>.

The service is utilized from the **Default GraphQL API** and **Headless CMS GraphQL API** AWS Lambda functions, both part of the [API project application](/docs/{version}/architecture/api/overview).

<Alert type="info">

Learn more about the Headless CMS API in the [Headless CMS GraphQL API Overview](/docs/{version}/headless-cms/basics/graphql-api) article.

</Alert>

### 2. Amazon S3

For storing all types of files, Webiny's File Manager application uses Amazon S3 <diagram-letter>B</diagram-letter>. Note that the application also provides a way to securely serve files and resize images (more in the following sections).

### 3. Amazon EventBridge

Amazon EventBridge is used for communication between different cloud infrastructure resources.

### 4. Amazon DynamoDb and Amazon ElasticSearch Service

Amazon DynamoDb and Amazon ElasticSearch Service are used as your project's databases. Note that the latter is only used if you're using the **Amazon DynamoDB + Amazon ElasticSearch** setup.

## FAQ

### How is the DynamoDB data structured?

Webiny is following the approach called [single-table design](https://www.alexdebrie.com/posts/dynamodb-single-table/), which advocates for storing all the application data in a single DynamoDB table. So, all of the Webiny applications are storing data in a single table, with distinct primary and secondary keys in order to avoid clashing of data.
