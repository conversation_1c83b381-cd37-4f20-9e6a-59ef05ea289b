---
id: aafeaa26
title: Overview
description: What are the necessary cloud infrastructure resources on which the API project application relies on
---

import { Alert } from "@/components/Alert";
import apiOverview from "./assets/overview/webiny_api_overview.png";

<Alert type="success" title="What you’ll learn">

- what does the **API** project application represent
- what are the necessary cloud infrastructure resources on which the **API** project application relies on

</Alert>

## The API Project Application

The **API** project application represents your project's GraphQL API, which is utilized by the **Admin** and **Website** applications.

But do note that the **API** application is more than just a simple GraphQL interface. The default Webiny applications not only define and extend the application's GraphQL schema, but also introduce additional processes and cloud infrastructure resources in order to achieve their goal. For example, in order to serve files and optimize images, the File Manager application brings a couple of dedicated AWS Lambda functions.

Of course, the application can additionally grow in terms of application code and cloud infrastructure on your behalf, if need be.

## Diagram

<Alert type="info">

For brevity, the diagram doesn't include network-level cloud infrastructure resources, like region, VPC, availability zones, and so on. Check out the [Deployment Modes](/docs/{version}/architecture/deployment-modes/introduction) section if you're interested in that aspect of the deployed cloud infrastructure.

</Alert>

<Alert type="info">

Note that the stateful resources like Amazon S3, Amazon Cognito, Amazon DynamoDB and Amazon ElasticSearch are deployed as part of the [Core](/docs/{version}/architecture/core/overview) project application. These are still included in the diagram, just so it's more clear to the reader.

</Alert>

<Image src={apiOverview} title="Webiny Cloud Infrastructure - API - Overview" shadow={false} />

## Description

The diagram gives an overview of the complete cloud infrastructure that is deployed by different Webiny applications, which together form the **API** project application.

1. Security
2. I18N
3. File Manager
4. Page Builder (with Prerendering Service)
5. Form Builder
6. Headless CMS

In the following text, we briefly cover how each of the different Webiny applications are utilizing the shown cloud infrastructure.

### 1. Security

Although the Security application does work with multiple identity providers, by default, it works with Amazon Cognito, hence its icon in the lower section of the diagram <diagram-letter>d</diagram-letter>. The identity checks are performed from the GraphQL Handler <diagram-letter>C</diagram-letter> AWS Lambda function.

With Amazon Cognito, the application also extends the GraphQL schema located on the GraphQL Handler <diagram-letter>C</diagram-letter>.

For storing data, the application relies on Amazon DynamoDB <diagram-letter>E</diagram-letter>.

### 2. I18N

The I18N application doesn't bring any additional cloud infrastructure resources. It just expands the GraphQL schema with its own types and resolvers, so we can say only the GraphQL Handler AWS Lambda function <diagram-letter>C</diagram-letter> gets affected by it.

For storing data, the application relies on Amazon DynamoDB <diagram-letter>E</diagram-letter>.

### 3. File Manager

Besides extending the GraphQL schema located on the GraphQL Handler AWS Lambda function <diagram-letter>c</diagram-letter>, the File Manager application also introduces a couple of dedicated AWS Lambda functions. These perform various file-related tasks like uploading, downloading, and also image optimizations. It also introduces a single Amazon S3 bucket <diagram-letter>G</diagram-letter>, in which the files are ultimately stored.

For storing data and performing search queries, the application relies on Amazon DynamoDB <diagram-letter>E</diagram-letter> and Amazon ElasticSearch Service <diagram-letter>F</diagram-letter>, respectively.

### 4. Page Builder (with Prerendering Service)

Besides extending the GraphQL schema, the Page Builder application also introduces a couple of AWS Lambda functions that are responsible for importing and exporting of pages.

Also, via an Amazon EventBridge (part of the **Core** project application, now shown on the diagram), it interacts with the prerendering service that's deployed as part of the **Website** project application (more on this in the [Website](/docs/{version}/architecture/website/overview) section.

For storing data and performing search queries, the application relies on Amazon DynamoDB <diagram-letter>E</diagram-letter> and Amazon ElasticSearch Service <diagram-letter>F</diagram-letter>, respectively.

### 5. Form Builder

The Form Builder application doesn't bring any additional cloud infrastructure resources. It just extends the GraphQL schema with its own types and resolvers, so we can say only the GraphQL Handler AWS Lambda function <diagram-letter>C</diagram-letter> gets affected by it.

For storing data and performing search queries, the application relies on Amazon DynamoDB <diagram-letter>E</diagram-letter> and Amazon ElasticSearch Service <diagram-letter>F</diagram-letter>, respectively.

### 6. Headless CMS

For storing data and performing search queries, the application relies on Amazon DynamoDB <diagram-letter>E</diagram-letter> and Amazon ElasticSearch Service <diagram-letter>F</diagram-letter>, respectively.

<Alert type="info">

Learn more about the Headless CMS API in the [Headless CMS GraphQL API Overview](/docs/{version}/headless-cms/basics/graphql-api) article.

</Alert>

## FAQ

### Why is Webiny using AWS Lambda to host a GraphQL Handler and not AWS AppSync?

There are a couple of reason for that.

First, implementing a GraphQL Handler using AWS Lambda enables us and our users to make additional customizations to server's behavior.

The default Webiny applications rely on this fact as well. For example, the I18N application gives you the ability to check what is the current locale that the user is on. The Security application gives you the ability to perform custom authentication and authorization checks inside of your resolvers. Finally, it also makes it possible for the Headless CMS application to generate different GraphQL schemas, based on the current I18N locale.

Secondly, you can test your GraphQL Handler easily with a testing library like [Jest](https://jestjs.io/), as you would do with any other JavaScript piece of code.

Finally, this approach makes it much easier for Webiny to be hosted on not just one cloud infrastructure provider (AWS), but on many more, like Microsoft Azure or Google Cloud Platform.

### Which GraphQL implementation are you using?

We started with [Apollo GraphQL Server](https://www.apollographql.com/docs/apollo-server/), but for Webiny version 5, we migrated to [GraphQL tools](https://www.graphql-tools.com/), because it's more lightweight and both easier to implement and grasp.

### Which Amazon API Gateway is deployed as part of the API application?

By default, we deploy [HTTP APIs](https://docs.aws.amazon.com/apigateway/latest/developerguide/http-api.html). This can be changed on your behalf, if need be.

### How is the DynamoDB data structured?

Webiny is following the approach called [single-table design](https://www.alexdebrie.com/posts/dynamodb-single-table/), which advocates for storing all the application data in a single DynamoDB table. So all of the Webiny applications are storing data in a single table, with distinct primary and secondary keys in order to avoid clashing of data.

### Is Amazon ElasticSearch Service deployed into multiple AZs?

Currently it is deployed into a single AZ, just for cost reasons. But yes, production workloads should use two or three AZs. Check out the [Amazon ElasticSearch Service's FAQ page](https://aws.amazon.com/elasticsearch-service/faqs/) for more information.

Deploying the Amazon ElasticSearch Service <diagram-letter>g</diagram-letter> into multiple AZs can be achieved by adjusting [the cloud infrastructure code](https://github.com/webiny/webiny-js/blob/v5/packages/cwp-template-aws/template/api/pulumi_no_vpc/elasticSearch.ts#L9).
