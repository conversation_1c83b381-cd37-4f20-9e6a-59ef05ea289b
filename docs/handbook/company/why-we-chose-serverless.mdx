---
id: 4c0ab180
title: Why we chose serverless infrastructure
description: What are our reasons for using serverless infrastructure
slug: why-we-chose-serverless
---

When we set out to build Webiny, one of our goals was to provide a great experience to the developers. To do that we knew we needed to control the product experience end-to-end, and with a self-hosted product like Webiny, that is not easily achievable. A big part of how a product performs is due to the infrastructure it runs on. How performant, scalable and secure that infrastructure is, is a part of it. But the other part is how costly is it to run and maintain on an ongoing basis. 

We took a bet on serverless. With serverless we can use the best services in the world to build our product. Just imagine the engineering force you would need to have on your end if you wanted to build a fault-tolerant storage similar to AWS S3, or a computing unit that can scale from zero to a thousand instances in a few seconds, like AWS Lambda, or a database that can handle millions of queries per second, like DynamoDB. And did we mention that those services are fault-tolerant by design, that they are priced per usage and that they are maintained and secured by some of the best engineers in the world?

This is what we ship with Webiny to all our customers in a package that with a single command configures and deploys all those services automatically inside their own AWS cloud.

So when someone asks us why we chose serverless infrastructure, the answer is simple. We chose it because it allows us to focus on building the best product we can, and not worry about the infrastructure. It gives us and our customers the piece of mind that the infrastructure will always be secure, it will always be online, and it will always perform well at a cost that directly correlates with the usage.

## FAQ

### What about vendor lock-in?

This is a conscious tradeoff we decided to make. We choose the best infrastructure at the cost of the vendor lock-in. In our opinion, the benefits of using serverless infrastructure outweigh the vendor lock-in.

At the same time, with Webiny you are not locked into our platform, which we believe is the more important question to ask yourself. With Webiny you always have direct access to all of your data and can move it to any other system at any time.

With serverless you will save a significant amount of your engineering and DevOps effort which can be used to build your product and grow your business instead of managing servers. 

### Is it just AWS, what about other cloud providers?

We do want to support other cloud providers, but we are a small team and we need to focus on one cloud provider at a time. We chose AWS because it is the most mature and feature-rich cloud provider. We are planning to support other cloud providers in the future.

### What about the cold start problem?

With the right architecture, you will not face this problem. Just try Webiny for yourself and see. Large enterprises use us and have never raised this problem.

### Where can I learn more about serverless?

Check out our [whitepaper](https://www.webiny.com/resources/benefits-of-a-serverless-cms).