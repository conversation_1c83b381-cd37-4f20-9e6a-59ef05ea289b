---
id: 63c45bc2
title: How we make money
description: Learn about our business model and how we make money.
slug: how-we-make-money
---

The first thing to get out of the way is that open source doesn't mean "free" as in "I don't need to pay for it". It means "free" as in "I have the freedom to use it, study it, modify it, and redistribute it" (within the terms defined in its license).

> When we call software “free,” we mean that it respects the users' essential freedoms: the freedom to run it, to study and change it, and to redistribute copies with or without changes. This is a matter of freedom, not price, so think of “free speech,” not “free beer.”
- by <PERSON> ([Why Open Source Misses the Point of Free Software](https://www.gnu.org/philosophy/open-source-misses-the-point.en.html))

## Our business model

We are a for-profit company. We make money by selling licenses to our software. We also offer professional services to help our customers with their projects and we offer support to make sure that if there are issues, we're there to help resolve them. 

We have a dual-license model. This means that we offer our software under two different licenses: a commercial license and an open-source license. You can read more about our license model and pricing tiers [here](/docs/overview/pricing#webiny-and-39-s-dual-license-model).

## How we do sales

We utilize many tactics to sell our software, combining both inbound and outbound sales. We have a sales team that reaches out to potential customers and we also have a marketing team that creates content to attract potential customers.

We love technical selling, so even if you start off talking business, we love to get in front of your engineering team and do hour-long technical deep dives, open the code, set up live coding demos and similar. We want to make sure that you understand our product and that it's a good fit for your project.

## We are either the best solution for you, or we walk away

Our customers deeply care about at least four of the items from the list below: 

- ** Ability to control and customize the product ** - We are an open-source platform and we come with a development framework. This means that you have full control over the product and you can customize it to your needs. 

- ** Data ownership and governance ** - We are purposely built to be a self-hosted solution. This means that you own your data and you can host it wherever you want.

- ** Performance at scale ** - We are built on top of [AWS](https://aws.amazon.com/) and we utilize many of its serverless services. This means that you can scale your application as much as you want without worrying about performance.

- ** Cost efficiency ** - The cost model of serverless infrastructure is that you pay per usage. This means that you only pay for what you use, you never overpay or underpay. In practice, Webiny can lower your infrastructure costs by up to 80% compared to traditional hosting solutions. ([read the whitepaper](https://www.webiny.com/resources/benefits-of-a-serverless-cms))

- ** Manage multiple projects/websites ** - Webiny is a platform that allows you to manage multiple projects from a single installation. This means that you can build multiple applications and manage them from a single admin interface significantly reducing your maintenance costs.

- ** Developer experience ** - We are a developer-first company. This means that we put a lot of effort into making sure that our product is easy to use and that it provides a great developer experience.

- ** Future-proof technology stack ** - We are built on top of [React](https://reactjs.org/), [GraphQL](https://graphql.org/), [Node.js](https://nodejs.org/en/), [Typescript](https://www.typescriptlang.org/) and  [AWS Serverless](https://aws.amazon.com/serverless). This means that you are building your application on top of the most popular and future-proof technologies available today.

- ** Environmentally friendly ** - Running your website on VMs is like having a car with its engine constantly running in an idle state. Just so you could drive it for the 2h of your daily commute. Webiny runs on serverless infrastructure. There's no idle state, your machines are only running when they have something to do. The rest of the time they don't idle, waste energy and pump out CO2. This means that you are not only saving money, but you are also helping the environment.

In case you don't feel these things are resonating strongly with you, we are probably not the best solution for you. And that's okay. We are not trying to be everything to everyone. We are trying to be the best solution for a specific type of customer. You'll often hear us recommending other solutions to our customers if we feel that they are a better fit for their project.

## Don't let the price model get in the way

Our default pricing model for our enterprise tier is based on several factors, like how many users will be using the system to manage content, how many projects or websites will be built, which features are needed, etc. This works in most cases, but we also understand that some customers prefer a different pricing model. 

Examples like customers having lots of external contributors or customers that deal with user-generated content, where the number of users is not known in advance, are cases where our default pricing model doesn't work. In these cases, we are happy to work with you to find a pricing model that works for both parties.

We can do this because of our self-hosted nature. Our pricing model is not "baked" into our code as you find in most SaaS products. We can easily change it to fit your needs. Our goal is to price based on the value you're getting out of the product. 

