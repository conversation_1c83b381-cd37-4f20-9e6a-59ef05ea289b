---
id: aafeab93
title: Create Form
description: Learn how to create and publish a form in Form Builder.
---

import { Al<PERSON> } from "@/components/Alert";
import createForm from "./assets/create-form/create-form.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to create and publish a form

</Alert>

Webiny allows you to easily design and customize forms to seamlessly gather and organize data from your audience. In this tutorial, we will learn how to create and publish a form in the form builder. We will do this in 4 steps:

- Step 1: Create a form
- Step 2: Embed the form
- Step 3: Test the form
- Step 4: View collected data

<Video src={createForm} controls={true} />

## Step 1: Create a Form

1. From the **Side Menu**, click **Form Builder** > **FORMS** > **Forms**.

   ✔️ The **Forms** screen opens.

2. Click **+ NEW FORM**.

   ✔️ The **New form** screen opens.

3. In the textbox, type **Contact Form**.

4. Click **+ CREATE**.

   ✔️ The form builder opens.

5. Under the **Form Element** section, click **CONTACT INFORMATION**.

   ✔️ The **CONTACT INFORMATION** accordion opens containing commonly used form fields.

6. Drag and Drop the following fields into the form-fields-area (marked with the message **Drop your first field here**) inside the **EDIT** tab:

   a. **FIRST NAME**

   b. **LAST NAME**

   c. **EMAIL**

7. Drag and Drop the **CUSTOM FIELD** into the form-fields-area.

   ✔️ The **Field Settings** screen opens.

   a. Click **Long Text**.

   ✔️ **GENERAL** tab opens.

   b. In the **Label** textbox, type **Message**.

8. Click **PUBLISH**.

   ✔️ The **Forms** screen opens with the message "Your form was published successfully!".

## Step 2: Embed the Form

To use a form, you need to embed it into a page. In this step, we we will embed the form into a page using the Page Builder. If you are not familiar with how to create a page, please follow the [Create Page](/docs/{version}/user-guides/page-builder/essentials/create-page) tutorial.

1. From the **Side Menu**, click **Page Builder** > **PAGES** > **Pages**.

   ✔️ The **Pages** screen opens.

2. Add a new page.

3. Update the title of the page to **Contact Us**.

4. Add an empty block to the page.

5. Click the **ADD ELEMENT** icon.

   ✔️ The elements menu opens.

6. Click **Form**.

   ✔️ The **Form** elements accordion opens.

7. Drag and drop the **FORM** element inside the block.

   ✔️ The **ELEMENT** tab of the **FORM** element opens.

8. In the **Form** dropdown, select **Contact Form**.

   ✔️ The **Revision** dropdown appears.

9. In the **Revision** tab, click **Latest published revision**.

10. Click **Save**.

    ✔️ The **Contact Form** appears in the page editor.

11. Click **PUBLISH**.

    ✔️ The **Pages** screen opens with the message "Your page was published successfully!".

## Step 3: Test the Form

1. On the **Pages** screen, click the **Contact Us** list item in the list of pages.

   ✔️ The **PAGE PREVIEW** tab opens.

2. Click the **kebab menu** icon ( ⁝ ) on the top right of the page.

   ✔️ Menu opens with the options **View** and **Set as homepage**.

3. Click **View**.

   ✔️ The **Contact Us** page opens.

4. In the **First name** textbox, type _your name_ (e.g. **John**).

5. In the **Last name** textbox, type _your name_ (e.g. **Doe**).

6. In the **Email** textbox, type _your email_ (e.g. **<EMAIL>**).

7. In the **Message** textbox, type _your message_ (e.g. **Testing the form.**).

8. Click **SUBMIT**.

   ✔️ The success message appears.

## Step 4: View Collected data

1. From the **Side Menu**, click **Form Builder** > **FORMS** > **Forms**.

   ✔️ The **Forms** screen opens.

2. Click **Contact Form** list item in the list of forms.

   ✔️ The **FORM PREVIEW** tab opens.

3. Click the **SUBMISSIONS** tab.

   ✔️ The **SUBMISSIONS** tab opens.

4. Click the **REFRESH DATA** icon.

You will see the collected data in the **SUBMISSIONS** tab of the form.

**Note**: In the **TRIGGERS** tab inside the form editor, you can set the action that should be taken after the submission of a form. Two actions are available:

- **Redirect**: Sends the user to a specific URL after the successful submission of a form.

- **Webhook**: Makes a POST HTTP request to a specific URL after the successful submission of a form.
