---
id: aafeab94
title: Using reCAPTCHA in Form
description: Learn how to integrate and use reCAPTCHA in a form
---

import { <PERSON><PERSON> } from "@/components/Alert";
import reCAPTCHAInForm from "./assets/using-reCAPTCHA-in-form/form-reCAPTCHA.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to integrate reCAPTCHA in a Webiny instance
- how to use reCAPTCHA in a form

</Alert>

Webiny enables you to effortlessly integrate reCAPTCHA into your web app and leverage its robust features to bolster security and enhance user interactions. In this tutorial, we will learn how to integrate and use reCAPTCHA with a form. We will do this in \_ steps:

- Step 1: Integrate reCAPTCHA in the Webiny instance
- Step 2: Enable reCAPTCHA for the form
- Step 3: Test the form

<Video src={reCAPTCHAInForm} controls={true} />

## Step 1: Integrate reCAPTCHA in the Webiny instance

1. From the **Side Menu**, click **Settings** > **FORM BUILDER** > **reCAPTCHA**.

   ✔️ The **reCAPTCHA Settings** screen opens.

2. Click the **Enable Google reCAPTCHA** button to toggle it on.

3. In the **Domain** textbox, type _your domain_ (e.g. **https://www.webiny.com**).

   **Note**: In this tutorial, we will use the domain where your web app is published. Please note that this domain is different from the one where your Webiny CMS runs. You can find the published web app domain by visiting any published page, such as the **Contact Us** page that we created in the [Create Form](/docs/{version}/user-guides/form-builder/essentials/create-form) tutorial (e.g. https://d3s7islumczq2q.cloudfront.net).

4. Click **Don't have a site key?** below the **Google reCAPTCHA site key** textbox.

   ✔️ The **Register a new site** screen on the **Google reCAPTCHA** website opens.

5. In the **Label** textbox, type _a label for your web app_ (e.g. **webiny.com**).

6. Under **reCAPTCHA type**, click **Challenge (v2)**.

7. Under **Challenge (v2)**, click **"I'm not a robot" tickbox**.

8. Under **Domains**, in the textbox, type _your domain_ (e.g. **webiny.com**).

   **Note**: Ensure that this domain matches the one you entered in **Step 3**, excluding **http://** and **https://** (e.g. **d3s7islumczq2q.cloudfront.net**).

9. Click **SUBMIT**.

   ✔️ The **Adding reCAPTCHA to your site** screen opens.

10. To add **Google reCAPTCHA site key** to your Webiny instance:

    a. On the **Adding reCAPTCHA to your site** screen, click **COPY SITE KEY**.

    b. On the **reCAPTCHA Settings** screen of your Webiny instance, paste the copied key in the **Google reCAPTCHA site key** textbox.

11. To add **Google reCAPTCHA secret key** to your Webiny instance:

    a. On the **Adding reCAPTCHA to your site** screen, click **COPY SECRET KEY**.

    b. On the **reCAPTCHA Settings** screen of your Webiny instance, paste the copied key in the **Google reCAPTCHA secret key** textbox.

12. On the **reCAPTCHA Settings** screen, click **SAVE**.

## Step 2: Enable reCAPTCHA for the form

1. From the **Side Menu**, click **Form Builder** > **FORMS** > **Forms**.

   ✔️ The **Forms** screen opens.

2. On the **Forms** screen, click the **Contact Form** list item in the list of forms that we created in the [Create Form](/docs/{version}/user-guides/form-builder/essentials/create-form) tutorial.

   ✔️ The **FORM PREVIEW** tab opens.

3. In the **FORM PREVIEW** tab, click the **EDIT** icon (✏️).

   ✔️ The form editor screen opens.

4. Click the **SETTINGS** icon (⚙️).

   ✔️ The **Form Settings** screen opens.

5. Click the **ReCAPTCHA** tab.

6. Click the **Enable** button to toggle it on.

7. Click **SAVE SETTINGS**.

   ✔️ The form editor screen opens.

8. Click **PUBLISH CHANGES**.

## Step 3: Test the form

1. From the **Side Menu**, click **Page Builder** > **PAGES** > **Pages**.

   ✔️ The **Pages** screen opens.

2. Click the **Contact Us** list item in the list of pages that we created in the [Create Form](/docs/{version}/user-guides/form-builder/essentials/create-form) tutorial.

   ✔️ The **PAGE PREVIEW** tab opens.

3. Click the **kebab menu** icon ( ⁝ ) on the top right of the page.

   ✔️ Menu opens with the options **View** and **Set as homepage**.

4. Click **View**.

   ✔️ The **Contact Us** page opens.

The reCAPTCHA tickbox will be visibly displayed alongside the form.
