---
id: aafeab84
title: Reference Field
description: Learn how to use reference field in Headless CMS.
---

import { Alert } from "@/components/Alert";
import referenceField from "./assets/reference-field/reference-field.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- what is a reference field
- how to use reference field

</Alert>

The reference field enables users to link one content model to another. For instance, consider two content models, **Product** and **Category**.
A product may belong to multiple categories. In such a scenario, we create a reference field in the **Product** content model that refers to the **Category** model.

In this tutorial, we will learn how to use reference field.
As an example, we will use the **Product** content model that we created in the [Create Content Model](/docs/{version}/user-guides/headless-cms/essentials/create-content-model) tutorial.
We will create another content model **Category**, and give the reference of **Category** in **Product**.
We will do this in four steps:

- Step 1: Create a **Category** content model.
- Step 2: Add content entries to the **Category** content model.
- Step 3: Update the **Product** content model and add a category reference field to it.
- Step 4: Create a content entry in the **Product** content model with reference to the **Category** content model.

<Video src={referenceField} controls={true} />

## Step 1: Create 'Category' content model

We will create a category content model with the following attributes and fields.

If you are not familiar with how to create a content model, please follow the [Create Content Model](/docs/{version}/user-guides/headless-cms/essentials/create-content-model) tutorial.

1. Create a content model with the following attributes:

   | Field       | Value                                                                     |
   | :---------- | :------------------------------------------------------------------------ |
   | Name        | **Category**                                                              |
   | Group       | If you have created a group, select that group, else select **Ungrouped** |
   | Description | **Demo Category content model**                                           |

2. Add the following field to the **Category** content model:

   | Field | Field Type |
   | :---- | :--------- |
   | Name  | Text       |

## Step 2: Create 'Category' content entries

1. From the **Side Menu**, Click **Headless CMS** > **Categories**.

2. Click **+ NEW ENTRY**.

   ✔️ The New Content Entry screen opens.

3. In the **Name** textbox, type **Clothes**.

4. Click **SAVE & PUBLISH**.

**Optional**: Similarly, you can create another **Category** content entry with name **Accessories**.

## Step 3: Update 'Product' Content Model

1. From the **Side Menu**, Click **Headless CMS** > **Models**.

   ✔️ The Content Models screen opens.

2. Hover on the **Product** content model.
3. Click the **EDIT CONTENT MODEL** icon (✏️).

   ✔️ Screen to configure the content model's fields opens.

4. Drag and drop the **REFERENCE** field in the **EDIT** tab.

   ✔️ **Field Settings - Reference** screen opens.

5. In the **Label** textbox, type **Category**.

6. In the **Content Models** dropdown, click **Category**.

7. Click the **Use as a list of references** button.

   **Note**: Since a product can belong to multiple categories, enabling **Use as a list of references** will allow us to associate multiple categories to a product.

8. Click **SAVE FIELD**.

   ✔️ Screen to configure the content model's fields opens.

9. Click **SAVE**.

## Step 4: Create 'Product' content entry with a reference to 'Category'

1. From the **Side Menu**, Click **Headless CMS** > **Products**.

2. Click **+ NEW ENTRY**.

   ✔️ The New Content Entry screen opens.

3. In the **Name** textbox, type **Men's Tiro '21 Pants**.

4. In the **Description** textbox, type **Men's tapered track pants for versatile wear**.

5. In the **Price** textbox, type **28**.

6. In the **Category** dropdown, click **Clothes**.

7. Click **SAVE & PUBLISH**.
