---
id: aafeab81
title: Clone Content Model
description: Learn how to clone a content model in Headless CMS.
---

import { Al<PERSON> } from "@/components/Alert";
import cloneContentModel from "./assets/clone-content-model/clone-content-model.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to clone a content model

</Alert>

Webiny allows you to create a new content model by cloning an existing content model. All the fields, validations, and other settings are copied over from the original content model to the clone content model.

In this tutorial, we will learn how to clone a content model. As an example, we will create a content model with the following attributes by cloning an existing content model:

| Attribute   | Value                                                         |
| :---------- | :------------------------------------------------------------ |
| Name        | **Virtual Product**                                           |
| Group       | **E-Commerce**                                                |
| Description | **Demo Virtual Product Content Model for E-Commerce project** |

<Video src={cloneContentModel} controls={true} />

## Prerequisites

To follow this tutorial, you need the **Product** content model to clone it.
If you don't have the **Product** content model, please follow the [Create Content Model](/docs/{version}/user-guides/headless-cms/essentials/create-content-model) tutorial to create it.

## Clone Content Model

1. From the **Side Menu**, Click **Headless CMS** > **Models**.

2. Hover on the **Product** content model.

3. Click the **CLONE CONTENT MODEL** icon.

   ✔️ The **Clone Content Model** screen opens.

4. In the **Name** textbox, update the text to **Virtual Product**.

5. In the **Content model locale** dropdown, click **Current locale**.

6. In the **Content model group** drop-down, if you have the **E-Commerce** group, select that group; else select **Ungrouped**.

7. In the **Description** textbox, update the text to **Demo Virtual Product Content Model for E-Commerce project**.

8. Click **+ CLONE**.

   ✔️ **Virtual Product** content model is created.
