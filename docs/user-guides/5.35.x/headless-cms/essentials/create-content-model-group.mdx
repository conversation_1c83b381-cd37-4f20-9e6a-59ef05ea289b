---
id: aafeab80
title: Create Content Model Group
description: Learn how to create a content model group in Headless CMS.
---

import { Alert } from "@/components/Alert";
import createContentModelGroup from "./assets/create-content-model-group/create-content-model-group.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- what is content model group and it's uses
- how to create a content model group

</Alert>

Content model groups help in organizing content models. With groups, you can organize your models efficiently.
For example, all the content models related to E-Commerce can be grouped together,
and they will be shown in the side navigation bar under the E-Commerce heading. There are two main uses of
the content model group.

- Organization of content models
- Restrict the content models access scope based on the content model group

In this tutorial, we will learn how to create a content model group.
As an example, we will create a E-Commerce content group with the following values:

| Attribute   | Value                  |
| :---------- | :--------------------- |
| Name        | E-Commerce             |
| Group icon  | 🛒                     |
| Description | E-Commerce Model Group |

<Video src={createContentModelGroup} controls={true} />

1. From the **Side Menu**, Click **Headless CMS** > **Groups**.
2. Click **+ NEW GROUP**.

   ✔️ The New content model group section opens.

3. In the **Name** textbox, type **E-Commerce**.
4. In the **Group icon**, select **shopping-cart** icon (🛒).
5. In the **Description** textbox, type **E-Commerce Model Group**.
6. Click **SAVE CONTENT MODEL GROUP**.
7. Congratulations! You have created your first content model group.
