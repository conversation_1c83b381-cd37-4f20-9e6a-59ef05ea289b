---
id: aafeab7f
title: Content Entry Revisions
description: Learn how to publish a specific version of a content entry, and create a new version of a content entry by deriving from a previous revision.
---

import { Alert } from "@/components/Alert";
import contentEntryRevision from "./assets/content-entry-revisions/content-entry-revisions.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to create multiple revisions of a content entry and publish a specific one
- how to create a new content entry version from a previous revision

</Alert>

In Webiny, each modification in a content entry that is saved or published is stored as a separate revision.
In this tutorial, we will learn how to create multiple revisions of a content entry and publish a specific revision.
We will also learn how to create a new content entry version from a previous revision.
As an example, we will use the **Product** content model that we created in the [Create Content Model](/docs/{version}/user-guides/headless-cms/essentials/create-content-model) tutorial.

<Video src={contentEntryRevision} controls={true} />

We will do this in 3 steps:

- Step 1: Create a content entry in the **Product** content model and make multiple revisions.
- Step 2: Publish a previous revision of the content entry.
- Step 3: Create a new content entry version from a previous revision.

## Step 1: Create a Content Entry and Its Revisions

If you are not familiar with how to create a content entry, please follow the [Create Content Entry](/docs/{version}/user-guides/headless-cms/essentials/create-content-entry) tutorial.

1. Create a content entry in the **Product** content model with the following attributes:

   | Field       | Value                                                             |
   | :---------- | :---------------------------------------------------------------- |
   | Name        | **Men's Blue T-Shirt**                                            |
   | Description | **Half Sleeved, Regular fit, Cotton blend, Blue T-shirt for Men** |
   | Price       | **15**                                                            |
   | Category    | **Clothes**                                                       |

   **Note**: If you haven't added the **Category** field to the **Product** content model, skip it (or add it by following the [Reference Field](/docs/{version}/user-guides/headless-cms/essentials/reference-field) tutorial).

2. Now, update the **Name** and **Description** textboxes with the following values:

   | Field       | Value                                                                    |
   | :---------- | :----------------------------------------------------------------------- |
   | Name        | **Men's Solid Blue T-Shirt**                                             |
   | Description | **Half Sleeved, Regular fit, Cotton blend, Solid, Blue T-shirt for Men** |

3. Click **SAVE & PUBLISH**.

   **Note**: Clicking **SAVE** will create a new version of the content entry with updated field values and save it as a draft.
   Whereas clicking **SAVE & PUBLISH** will create a new version and publish it.

4. Create another revision by updating the **Name** and **Description** textboxes with the following values:

   | Field       | Value                                                                                |
   | :---------- | :----------------------------------------------------------------------------------- |
   | Name        | **Men's Solid Round Neck Blue T-Shirt**                                              |
   | Description | **Round Neck, Half Sleeved, Regular fit, Cotton blend, Solid, Blue T-shirt for Men** |

5. Click **SAVE & PUBLISH**.

## Step 2: Publish a Previous Revision

1. In the **Men's Solid Round Neck Blue T-Shirt** content entry, click the **REVISIONS** tab.

2. To publish the previous **Men's Solid Blue T-Shirt** revision, click the **Kebab Menu** on it ( ⁝ ).

   ✔️ Menu opens with **New from current** and **Publish** option.

3. Click **Publish**.

   ✔️ The **Men's Solid Blue T-Shirt** revision gets published. **CONTENT** tab opens with the message "Successfully published revision #2!".

   **Note**: The dropdown on the top-left corner of the content tab shows **V3 (UNPUBLISHED)**. To open the published entry version, click **V2 (Published)** in the dropdown.

## Step 3: Create a new content entry version from a previous revision

1. In the **Men's Solid Round Neck Blue T-Shirt** content entry, click the **REVISIONS** tab.

2. To create a new entry version from the previous **Men's Blue T-Shirt** revision, click the **Kebab Menu** on it ( ⁝ ).

   ✔️ Menu opens with **New from current** and **Publish** option.

3. Click **New from current**.

   ✔️ **CONTENT** tab opens with a new draft of the previous **Men's Blue T-Shirt** revision.

   **Optional**: Make the desired change(s) in the respective field(s).

4. Click **SAVE & PUBLISH**.

   ✔️ A new content entry revision will be saved and published.
