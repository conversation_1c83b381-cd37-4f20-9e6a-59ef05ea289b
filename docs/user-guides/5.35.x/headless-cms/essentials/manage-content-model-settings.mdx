---
id: aafeab82
title: Manage Content Model Settings
description: Learn how to manage content model settings in Headless CMS.
---

import { Alert } from "@/components/Alert";
import updateContent from "./assets/update-content-model/update-content-model.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to manage content model settings

</Alert>

In this tutorial, we will learn how to manage a content model general settings.
As an example, we will update the description of **Product** content model that we created in the [Create Content Model](/docs/{version}/user-guides/headless-cms/essentials/create-content-model) tutorial.

<Video src={updateContent} controls={true} />

1. From the **Side Menu**, Click **Headless CMS** > **Models**.

   ✔️ The Content Models screen opens.

2. Hover on the **Product** content model.
3. Click the **EDIT CONTENT MODEL** icon (✏️).

   ✔️ Screen to configure the **Product** content model's fields opens.

4. Click the **Settings** icon (⚙️).

   ✔️ The **Content model settings** screen opens.

5. In the **Content model description** textbox, update **Demo Product Content Model** text to **Demo Product Content Model for E-Commerce project**.

   **Note**: Similarly, you can update the **Content model name**, **Content model group**, and **Tags**.

6. Click **SAVE SETTINGS**.

   ✔️ Screen to configure the **Product** content model's fields opens with a message "Content model settings updated successfully."

7. Click **SAVE**.

   ✔️ The message "Your content model was successfully saved!" will be displayed.
