---
id: aafeab85
title: User Creation
description: Learn how to create a user with custom access to Headless CMS.
---

import { Al<PERSON> } from "@/components/Alert";
import userCreation from "./assets/user-creation/user-creation.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to create a user group
- how to define access permissions for a user group
- how to create a user

</Alert>

Webiny allows you to add multiple users with different access permissions to your application. You can define the access permissions by creating a user group.
In this tutorial, we will learn how to create a user group with custom access permission to the Headless CMS. Then we will create a user within that user group.

As an example, we will create a user group with the following access permissions:

- Permission to view and update a specific content model group.
- Permission to view, create, and update all content models inside the respective content model group.
- Permission to view, create, and update the content entries inside the content models.

<Video src={userCreation} controls={true} />

We will do this in 3 steps:

- Step 1: Create a user group.
- Step 2: Define access permissions.
- Step 3: Create user within the user group.

## Step 1: Create a User Group

1. From the **Side Menu**, Click **Settings** > **ACCESS MANAGEMENT** > **Groups**.

2. Click **+ NEW GROUP**.

   ✔️ The form to create a new user group opens.

3. In the **Name** textbox, type **E-Commerce**.

4. In the **Slug** textbox, type **e-commerce**.

5. In the **Description** textbox, type **User group for E-Commerce Managers**.

## Step 2: Define Access Permissions

1. Under the **Permissions** section, in the **Content** accordion, click **All locales**.

2. Under the **Permissions** section, click **Headless CMS**.

   ✔️ The **Headless CMS** accordion opens.

3. In the **Access Level** dropdown, click **Custom access**.

4. Under **GRAPHQL API TYPES**, select all the three checkboxes (**Read**, **Manage**, and **Preview**).

5. Under the **CONTENT MODEL GROUPS** section:

   a. In the **Access Scope** dropdown, click **Only specific groups**.

   ✔️ A list of content model groups in the current locale appears.

   b. Select the **E-Commerce** checkbox.

   c. In the **Primary Actions** dropdown, click **Read, write**.

6. Under the **CONTENT MODELS** section:

   a. In the **Access Scope** dropdown, click **All models**.

   b. In the **Primary Actions** dropdown, click **Read, write**.

7. Under the **CONTENT ENTRIES** section:

   a. In the **Access Scope** dropdown, click **All entries**.

   b. In the **Primary Actions** dropdown, click **Read, write**.

8. Under **PUBLISHING ACTIONS**, check both - **Publish** and **Unpublish** checkboxes.

9. Click **SAVE GROUP**.

   ✔️ The message "Group saved successfully!" displays.

## Step 3: Create User

1. From the **Side Menu**, Click **Settings** > **ADMIN USERS** > **Users**

2. Click **+ NEW USER**.

   ✔️ The **New User** screen opens.

3. In the **First Name** textbox, type _new user's first name_ (e.g. John).

4. In the **Last Name** textbox, type _new user's last name_ (e.g. Doe).

5. In the **Email** textbox, type _new user's email_ (e.g. <EMAIL>).

6. In the **Password** textbox, type _new user's password_.

7. In the **Group** dropdown, click **E-Commerce**.

8. Click **SAVE USER**.

   ✔️ The message "User saved successfully." displays.
