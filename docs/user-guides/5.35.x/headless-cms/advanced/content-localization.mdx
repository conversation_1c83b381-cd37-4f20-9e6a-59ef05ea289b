---
id: aafeab86
title: Content Localization
description: Learn how to create a content model for multiple locales.
---

import { Alert } from "@/components/Alert";
import contentLocalization from "./assets/content-localization/content-localization.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to create a content model for multiple locales

</Alert>

Webiny offers content localization that allows you to adapt your content to different languages by creating multiple locales.
It provides the flexibility to define distinct content schemas for various locales. This means that for each locale, you can create separate content model groups, content models, content entries, etc.

When you create a new locale, you get a clean slate in which you can create new content models, group, and entries. This gives you the flexibility to define variations of content models depending on your locales.

<Video src={contentLocalization} controls={true} />

In this tutorial, we will learn how to create a content model for multiple locales.
We will do this in 4 steps:

- Step 1: Create and select a new locale.
- Step 2: Create a content model group in the new locale.
- Step 3: Clone a content model into the new locale.
- Step 4: Create a content entry in the new locale.

## Prerequisites

To follow this tutorial, you need the **Product** content model to clone it.
If you don't have the **Product** content model, please follow the [Create Content Model](/docs/{version}/user-guides/headless-cms/essentials/create-content-model) tutorial to create it.

## Step 1: Create and Select a New Locale

We will create a new locale for the **Italic** language.

1. From the **Side Menu**, Click **Settings** > **LANGUAGES** > **Locales**.
2. Click **+ NEW LOCALE**.

   ✔️ The **New locale** section opens.

3. In the **Code** dropdown, type **it**.
4. In the available options in the dropdown, click **it-IT**.
5. Click **SAVE LOCALE**.
6. Click on the **LOCALE** dropdown in the header section and select **it-IT** to switch to the Italian locale.

## Step 2: Create a Content Model Group in the New Locale

If you are not familiar with how to create a content model group, please follow the [Create Content Model Group](/docs/{version}/user-guides/headless-cms/essentials/create-content-model-group) tutorial.

1. Create a content model group with the following attributes in the **it-IT** locale:

   | Attribute   | Value                               |
   | :---------- | :---------------------------------- |
   | Name        | **Commercio Elettronico**           |
   | Group icon  | 🛒                                  |
   | Description | **Gruppo di modelli di e-commerce** |

## Step 3: Clone a Content Model into the New Locale

There are a couple of ways to create a content model in a locale. You can either [create a content model](/docs/{version}/user-guides/headless-cms/essentials/create-content-model) from scratch, or you can clone it from another locale. In this step, we will clone the **Product** content model from the **en-US** locale into the **it-IT** locale. We will update the clone content model's attributes with the following values:

| Attribute   | Values                                     |
| :---------- | :----------------------------------------- |
| Name        | **Prodotto**                               |
| Group       | **Commercio Elettronico**                  |
| Description | **Modello di contenuto del prodotto demo** |

1. Click on the **LOCALE** dropdown in the header section and select **en-US**.

2. From the **Side Menu**, Click **Headless CMS** > **Models**.

3. Hover on the **Product** content model.

4. Click the **CLONE CONTENT MODEL** icon.

   ✔️ The **Clone Content Model** screen opens.

5. In the **Content model locale** dropdown, click **it-IT**.

6. In the **Name** textbox, update the text to **Prodotto**.

7. In the **Content model group** dropdown, click **Commercio Elettronico**.

8. In the **Description** textbox, update the text to **Modello di contenuto del prodotto demo**.

9. Click **+ CLONE**.

10. Update the labels of the fields of the **Prodotto** content model with the following values:

    | Old Label   | New Label       |
    | :---------- | :-------------- |
    | Name        | **Nome**        |
    | Description | **Descrizione** |
    | Price       | **Prezzo**      |

## Step 4: Create a Content Entry in the New Locale

If you are not familiar with how to create a content entry, please follow the [Create Content Entry](/docs/{version}/user-guides/headless-cms/essentials/create-content-entry) tutorial.

1. Add a content entry in the **Prodotto** content model with the following attributes:

   | Attribute   | Values                                                                                                                                                                  |
   | :---------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
   | Nome        | **Allegra K Abito Shift Abito Shift Donna**                                                                                                                             |
   | Descrizione | **Girocollo increspato, manica del braccialetto, polsini elastici, chiusura a bottone posteriore, foro della serratura posteriore, completamente foderato, mini abito** |
   | Prezzo      | **55**                                                                                                                                                                  |
