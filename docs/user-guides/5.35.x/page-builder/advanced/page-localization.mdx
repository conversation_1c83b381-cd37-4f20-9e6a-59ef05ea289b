---
id: aafeab91
title: Page Localization (Content Localization)
description: Learn how to create a page for multiple locales.
---

import { Al<PERSON> } from "@/components/Alert";
import pageLocalization from "./assets/page-localization/page-localization.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to create a page for multiple locales

</Alert>

Webiny allows creating different versions of the same page for different locales. In this tutorial, we will learn how to create a page for multiple locales. We will do this in 4 steps:

- Step 1: Create a new locale
- Step 2: Export a page
- Step 3: Create a new category in the new locale
- Step 4: Import the page in the new locale

<Video src={pageLocalization} controls={true} />

## Prerequisites

To follow this tutorial, you need the **The benefits of a Serverless CMS** page. If you don’t have the **The benefits of a Serverless CMS** page, please follow the [Create Page](/docs/{version}/user-guides/page-builder/essentials/create-page) tutorial to create it.

## Step 1: Create a New Locale

We will create a new locale for the **Italic** language.

1. From the **Side Menu**, click **Settings** > **LANGUAGES** > **Locales**.

2. Click **+ NEW LOCALE**.

   ✔️ The **New locale** section opens.

3. In the **Code** dropdown, type **it-IT**.

4. In the available options in the dropdown, click **it-IT**.

5. Click **SAVE LOCALE**.

## Step 2: Export a Page

1. From the **Side Menu**, click **Page Builder** > **PAGES** > **Pages**.

   ✔️ The **Pages** screen opens.

2. Select the checkbox on the **The benefits of a Serverless CMS** list item in the list of pages.

3. Click the **EXPORT** icon.

   ✔️ The **Select Page Revision** screen opens.

4. Click **Published**.

5. Click **CONTINUE**.

   ✔️ A screen appears with the prompt **Your export is now ready!**

6. Click the **COPY** icon to copy the export URL.

7. Click **CLOSE**.

## Step 3: Create a New Category in the New Locale

1. Click on the **LOCALE** dropdown in the header section and select **it-IT** to switch to the Italian locale.

2. From the **Side Menu**, click **Page Builder** > **PAGES** > **Categories**.

   ✔️ The **Categories** screen opens.

3. Click **+ NEW CATEGORY**.

   ✔️ The **New Category** form opens.

4. In the **Name** textbox, type **Static**.

5. In the **Slug** textbox, type **static**.

6. In the **URL** textbox, type **/static/**.

7. In the **Layout** dropdown, select **Static page**.

8. Click **SAVE CATEGORY**.

   ✔️ The message "Category saved successfully." displays.

## Step 4: Import the Page in the New Locale

1. From the **Side Menu**, click **Page Builder** > **PAGES** > **Pages**.

   ✔️ The **Pages** screen opens.

2. Click the **IMPORT** icon.

   ✔️ The **Select a category** screen opens.

3. Click **Static**.

4. Click **PASTE FILE URL** to import the page with the copied URL.

5. Paste the copied URL in the **File URL** textbox and click **CONTINUE**.

   ✔️ The **Import Pages** screen appears with the message "All pages have been imported".

6. Click **CONTINUE**.

   ✔️The **Pages** screen opens with the imported page available in the list of pages.

7. Edit the imported page and update the following values:

   | Field      | Current Value                                                           | Updated Value                                                                    |
   | :--------- | :---------------------------------------------------------------------- | :------------------------------------------------------------------------------- |
   | Page Title | The benefits of a Serverless CMS                                        | **I vantaggi di un CMS Serverless**                                              |
   | H1         | The benefits of a Serverless CMS                                        | **I vantaggi di un CMS Serverless**                                              |
   | H2         | Increase your business resiliency and reduce infrastructure spend       | **Aumenta la tua attività resilienza e ridurre la spesa infrastrutturale**       |
   | H3         | By switching to Webiny these are the quantifiable gains you can expect: | **Passando a Webiny questi sono i guadagni quantificabili che puoi aspettarti:** |

   **Note**: Similarly, you can update other parts of the page. Or you can create an entirely different page for the new locale.

8. Click **PUBLISH CHANGES**.

   ✔️ The **Pages** screen opens with the message "Your page was published successfully!".

Now, when a user from the Italian locale visits the **The benefits of a Serverless CMS** page, they will see the Italian version of the page.
