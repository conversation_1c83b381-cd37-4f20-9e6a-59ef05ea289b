---
id: aafeab8d
title: Create Navigation Menu
description: Learn how to create a navigation menu in Page Builder.
---

import { <PERSON><PERSON> } from "@/components/Alert";
import createNavigationMenu from "./assets/navigation-menu/navigation-menu.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to create navigation menu

</Alert>

In this tutorial, we will learn how to create a navigation menu in the Page Builder.

<Video src={createNavigationMenu} controls={true} />

## Step 1: Create Main Navigation Menu

1. From the **Side Menu**, click **Page Builder** > **PAGES** > **Menus**.

   ✔️ The **Menus** screen opens.

2. Click **Main Menu**.

   ✔️ The **Main Menu** screen opens.

   **Optional**: To update the name of the main menu, update the text in the **name** textbox.

   **Optional**: To update the description about the main menu, update the text in the **description** textbox.

3. Click **+ ADD MENU ITEM**.

   ✔️ A menu opens.

4. Click **Page**.

   The **PAGE MENU ITEM** form opens.

5. In the **Page** dropdown, select the title of page _you want to include in the main navigation menu_ (e.g. **The benefits of a Serverless CMS**).

6. In the **Title** textbox, type the title for the page _you want to be displayed in the main navigation menu_ (e.g. **Benefits**).

7. Click **SAVE MENU ITEM**.

   **Optional**: Repeat the steps 3 to 7 to add more page links to the menu.

8. Click **SAVE MENU**.

To view the menu, you can visit any published page of your website. The menu will be visible at the top of the page.
