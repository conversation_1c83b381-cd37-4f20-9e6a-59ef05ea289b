---
id: aafeab8e
title: Page Revisions
description: Learn how to publish a specific version of a page, and create a new version of a page by deriving from a previous revision.
---

import { Al<PERSON> } from "@/components/Alert";
import pageRevisions from "./assets/page-revisions/page-revisions.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to create multiple revisions of a page and publish a specific one
- how to create a new page version from a previous revision

</Alert>

In Webiny, each modification made in a page (in the Page Builder) that is published is stored as a separate revision. In this tutorial, we will learn how to create multiple revisions of a page and publish a specific revision. We will also learn how to create a new page version from a previous revision.

We will do this in 4 steps:

- Step 1: Create a new page
- Step 2: Make multiple revisions of a page
- Step 3: Publish a specific revision of a page
- Step 4: Create a new page version from a previous revision

<Video src={pageRevisions} controls={true} />

## Step 1: Create a New Page

If you are not familiar with how to create a page, please follow the [Create Page](/docs/{version}/user-guides/page-builder/essentials/create-page) tutorial.

1. From the **Side Menu**, click **Page Builder** > **PAGES** > **Pages**.

   ✔️ The **Pages** screen opens.

2. Add a new page in the **Static** category.

3. Update the title of the page to **Page Revisions**.

4. Add an empty block to the page.

5. Add a heading to the block and update its text to **Page Revisions**.

6. Update the margin-bottom of the heading to **60px**.

7. Add a paragraph below the heading and update its text to:

   **In Webiny, multiple revisions of a page can be made.**

8. Publish the page.

## Step 2: Make Multiple Revisions of a Page

1. On the **Pages** screen, click the **Page Revisions** list item in the list of pages.

   ✔️ The **PAGE PREVIEW** tab opens.

2. In the **PAGE PREVIEW** tab, click the **EDIT** icon (✏️).

   ✔️ The page editor screen opens.

3. Update the paragraph text to:

   **In Webiny, multiple revisions of a page can be made.**

   **A previous revision of a page can also be published.**

4. Click **PUBLISH CHANGES**.

   ✔️ The **Pages** screen opens with the message "Your page was published successfully!".

5. Now, following Steps 2 to 4, again update the paragraph text to:

   **In Webiny, multiple revisions of a page can be made.**

   **A previous revision of a page can also be published.**

   **And, a new version of a page can be made from a previous version.**

6. Publish the changes.

   ✔️ The **Pages** screen opens with the message "Your page was published successfully!".

## Step 3: Publish a Specific Revision of a Page

1. On the **Pages** screen, click the **Page Revisions** list item in the list of pages.

   ✔️ The **PAGE PREVIEW** tab opens.

2. Click the **REVISIONS** tab.

   ✔️ The **PAGE PREVIEW** tab opens.

   **Note**: On the **REVISIONS** tab, all the page's revisions and their status are visible. A version can have one of the following status:

   - **Draft**: A version of page that is not published yet but can be edited and published.
   - **Published**: A version of page that is published and cannot be edited.
   - **Locked**: A version of page that has been published at least once and now cannot be edited but published.

   A publsihed version is locked and remains locked after getting unpublished.

3. To publish the **Page Revisions #2** revision, click the **kebab menu** icon ( ⁝ ) on it.

   ✔️ Menu opens with **New from current**, **Publish**, **Preview**, and **Delete Revision** options.

4. Click **Publish**.

   ✔️ The message "Successfully published revision #2!" displays.

## Step 4: Create a New Page Version From a Previous Revision

1. Open the **REVISIONS** tab of the **Page Revisions** page.

2. To create a new page version from the previous **Page Revisions #1** revision, click the **kebab menu** icon ( ⁝ ) on it.

   ✔️ Menu opens with **New from current**, **Publish**, **Preview**, and **Delete Revision** options.

3. Click **New from current**.

   ✔️ The page editor screen opens.

4. Update the paragraph text from **In Webiny, multiple revisions of a page can be made.** to:

   **In Webiny, multiple revisions of a page can be made. A previous revision of a page can also be published. And, a new version of a page can be made from a previous revision.**

5. Click **PUBLISH CHANGES**.

   ✔️ The **Pages** screen opens with the message "Your page was published successfully!".
