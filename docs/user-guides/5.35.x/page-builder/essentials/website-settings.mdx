---
id: aafeab8c
title: Website Settings
description: Learn how to manage website settings.
---

import { Alert } from "@/components/Alert";
import websiteSettings from "./assets/website-settings/website-settings.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to edit website settings

</Alert>

In this tutorial, we will learn how to manage website settings like website's title, default home page, social media links, etc.

We will do this in 5 steps:

- Step 1: Edit basic website settings
- Step 2: Edit default pages
- Step 3: Edit favicon & logo
- Step 4: Edit social media links
- Step 5: Edit HTML tags

<Video src={websiteSettings} controls={true} />

## Step 1: Edit Basic Website Settings

In this step, we will edit the title of the website.

1. From the **Side Menu**, click **Settings** > **PAGE BUILDER** > **Website**.

   ✔️ The **Website Settings** screen opens.

2. Under the **Website Settings** section, in the **Website name** textbox, update the text to _your website's title_ (e.g. **Webiny**).

## Step 2: Edit Default Pages

In this step, we will edit the default **homepage** and **not found (404) page** of the website.

1. Under the **Default Pages** section, in the **Homepage** dropdown, select the page _you want as the homepage_ (e.g. **The benefits of a Serverless CMS** - the page we created in the [Create Page](/docs/{version}/user-guides/page-builder/essentials/create-page) tutorial).

2. Under the **Default Pages** section, in the **Not found (404) page** dropdown, select the page _you want to be displayed_ when a user visits a path URL on your website that does not exist (e.g. **Not Found**).

## Step 3: Edit Favicon and Logo

In this step, we will edit the logo of the website.

1. Under the **Favicon and Logo** section, click the **Logo** image selector.

   ✔️ The media manager screen opens.

2. Click the image _you want to set as the website's logo_ (or upload the image if it isn't already uploaded).

The other input field(s) in the **Favicon and Logo** section have the following use cases:

| Field   | Use Case                                                                                                        |
| :------ | :-------------------------------------------------------------------------------------------------------------- |
| Favicon | Selected icon will be displayed in the borwser's tab or address bar when a user visits any page on your website |

## Step 4: Edit Social Media Links

In this step, we will add a twiter profile's link to the website.

1. Under the **Social Media** section, in the **Twitter** textbox, type _your twitter profile's link_ (e.g. **https://twitter.com/WebinyCMS**).

The other input field(s) in the **Social Media** section have the following use cases:

| Field                    | Use Case                                                                                                      |
| :----------------------- | :------------------------------------------------------------------------------------------------------------ |
| Facebook                 | Link to your Facebook page/profile                                                                            |
| Instagram                | Link to your Instagram profile                                                                                |
| Default Open Graph Image | Selected image will be displayed as the preview image when a link of your website is shared over social media |

## Step 5: Edit HTML Tags

In this step, we will add an script tag in the header and footer of the website.

### Header tags

1. Under the **HTML Tags** section, in the **Header tags** text-area, type the following code:

```
<script>
  // Add your header scripts here
</script>
```

**Note:** HTML tags and scripts that are added in **Header tags** will be get added before
the closing `</head>` tag in HTML.
In Header tags, the supported tags are `<script>` and `<meta>`.

### Footer tags

2. Under the **HTML Tags** section, in the **Footer tags** text-area, type the following code:

```
<script>
  // Add your footer scripts here
</script>
```

**Note:** HTML tags and scripts that are added in **Footer tags** will be get added before
the closing `</body>` tag in HTML.
In Footer tags, the supported tag is `<script>`.

3. Click **SAVE**.

   ✔️ The messagge "Settings updated successfully." displays.
