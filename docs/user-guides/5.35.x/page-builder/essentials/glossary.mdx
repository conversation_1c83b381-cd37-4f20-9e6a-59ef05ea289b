---
id: aafeab87
title: Page Builder Glossary
description: Learn about the Page Builder terminologies.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- Page Builder terminologies, and their meanings.

</Alert>

## Grid Component

Grid component is the fundamental building block on a page. When you create a page, the first thing to add is a grid block.
It serves as the parent container for all the page elements. You can learn more about Grid component in this [tutorial](https://youtu.be/a5WyEg0ucN0?list=PL9HlKSQaEuXQdyCQDH_w7VQQcZbc67cPU).

## Page Element

Page Builder application comes with a plethora of ready-made page elements like Heading, Paragraph, Quote, Image, etc.
These are the building blocks of a page.
