---
id: aafea9f9
title: Import/Export Content Models
description: Learn how to import and export content models in Webiny Headless CMS.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to export content models
- how to import content models

</Alert>

Webiny Headless CMS allows you to export all your content models as a JSON file. This is useful if you want to migrate your content models to another project or if you want to share them with other developers.

Similarly, you can use the generated JSON file to import those same content models into your project.
This tutorial will guide you through the process of exporting and importing content models.

## Exporting Content Models

To export one or more content models navigate to your Content Models view inside the Headless CMS application.

1. From the **Side Menu**, Click **Headless CMS** > **Models**.  
   ✔️ The Content Models screen opens.

2. Click the **kebab menu** icon ( ⁝ ) on the top right of the page.  
   ✔️ Menu opens with the options **Export all models** and **Import models**.
   ![Headless CMS - Export Content Models](./assets/import-export-content-models/export-content-models-step-1.png)

3. Click **Export all models**.  
   ✔️ The export action will generate a JSON file containing all your content models.
   ![Headless CMS - Export Content Models](./assets/import-export-content-models/export-content-models-step-2.png)

## Importing Content Models

To import content models, navigate to your Content Models view inside the Headless CMS application.

1. From the **Side Menu**, Click **Headless CMS** > **Models**.  
   ✔️ The Content Models screen opens.

2. Click the **kebab menu** icon ( ⁝ ) on the top right of the page.  
   ✔️ Menu opens with the options **Export all models** and **Import models**.
   ![Headless CMS - Export Content Models](./assets/import-export-content-models/import-content-models.png)

3. Click **Import models**.  
   ✔️ The system will ask you to provide an export file.

4. Select the file you want to import and click the **Validate file** button.
   ![Headless CMS - Import Content Models](./assets/import-export-content-models/import-content-models-step-1.png)
   ✔️ The validation action will check the file and display a list of content models that can be imported.

5. Choose the content model you want to import. From the list you can choose which content model you want to import and which ones you want to skip.
   ![Headless CMS - Import Content Models](./assets/import-export-content-models/import-content-models-step-2.png)

<Alert type="success" title="Auto select referenced models">

Note that if you select to import a specific content model that references one or more content models that are also part of the export file, the system will automatically select those referenced content models as well. If you wish to skip them, you can just go back and exclude them from the import.

</Alert>

![Headless CMS - Import Content Models](./assets/import-export-content-models/import-content-models-step-3.png)

All the content models are grouped in their respective content model groups. Those groups will be automatically created for you upon importing the file if such a group already doesn't exist.

Inside the group section, you will have a list of individual content models that belong to that group. You can select which content models you want to import and which ones you want to skip by clicking the button on the right-hand side.

Also, underneath the content model name you will have a status for the content model. This status tells you if this content model will be `updated` (meaning the content model already exists) or `created` (meaning the content model currently doesn't exist).

6. Finally, once you have selected which content models you want to import. Click the **Import** button to start the import process.

![Headless CMS - Import Content Models](./assets/import-export-content-models/import-content-models-step-4.png)

✔️ The import process can take a few seconds to finish as it needs to validate all the information. Once the process is done you will see the status screen with the results of the import process.

From the same status screen, you can proceed to import additional content models just by selecting them and clicking the **Import** button again. In case you want to finish the import process, just click outside the content model dialog.
