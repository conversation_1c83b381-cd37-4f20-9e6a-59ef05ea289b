---
id: 75e36f87
title: Advanced Publishing Workflow
description: A workflow driven publishing process.
---

import { <PERSON><PERSON> } from "@/components/Alert";
import YouTube from "@/components/YouTube";

<Alert type="success" title="WHAT YOU'LL LEARN">

- Advanced Publishing Workflow feature overview

</Alert>

<YouTube id="NNRghN1yY_g" />

The Advanced Publishing Workflow (APW) is an application that runs on top of the Page Builder and the Headless CMS. By default, those applications come with a simplified publishing workflow. For example, users can either publish content or set it in a `review requested` state. The actual review is done outside the system, and there's no collaboration platform that's provided to manage this activity.
This is where the APW comes in. Within the APW, you are able to:

1. **Define Publishing Workflows**

    - Easily configure and customize publishing workflows tailored to your specific content creation and review requirements.
    - Define sequential steps and approval stages to ensure a structured and efficient review process.

2. **Overview of Pending Reviews**

    - Access a centralized dashboard providing a comprehensive overview of all pending reviews requiring your input.
    - Streamline your workflow by quickly identifying and prioritizing tasks that need your attention.

3. **Daily Digest of Pending Activities**

    - Receive a daily summary of pending activities, ensuring you stay informed about ongoing reviews without the need for constant monitoring.
    - Stay organized and proactive with a concise summary of tasks requiring your action.

4. **Collaborate with Multiple Peers**

    - Foster collaboration by involving multiple peers at different organizational levels in a structured peer review process.
    - Leverage the platform to facilitate communication and coordination among team members during the review and approval stages.

5. **Scheduled Publishing and Unpublishing**

    - Plan and organize content releases with the ability to schedule page publishing and unpublishing activities.
    - Ensure timely publication of content while maintaining control over the visibility and availability of your pages.

These benefits collectively contribute to a more streamlined and effective content creation and review process, enhancing collaboration, organization, and overall efficiency within your Webiny application.

You can have multiple publishing workflows, where each workflow has different steps, members, and rules when it applies. You can define workflows that apply to:

**Page Builder:**
- All pages
- Pages inside specific categories
- One or more individual pages

**Headless CMS:**
- All entries
- Entries inside a particular group
- Specific entries

In practice, you can say have a 2-step workflow that applies every time someone wants to publish a new blog post article. Those steps might be a peer review from a copywriter and a peer review from a marketing person. Then you can have another workflow that has 4-steps, and it only applies to when someone wants to change the homepage. In that case, you might want to get a peer review from someone in the legal department, marketing, sales, the CEO themself.

To each workflow step, you assign users that can provide a signoff and define steps as blocking or non-blocking.

The actual peer review is done by reviewers requesting one or more changes. All changes need to be resolved before a reviewer can provide a signoff for that step.

APW is a powerful utility that brings quality control and structured workflows. It ensures the content an organization publishes is of top quality and free of mistakes. Most of all, it streamlines the communication between multiple members involved in this process.
