---
id: 75e36f86
title: Submit Record for Review
description: Learn how to submit a record for review in a Publishing Workflow.
---

import { <PERSON><PERSON> } from "@/components/Alert";
import submitRecordForReviewVideo from "./assets/submit-record-for-review/apw-submit-record-for-review.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to submit a record for review

</Alert>

In this tutorial, we will learn how you can submit a record for review in a Publishing Workflow.

## Prerequisites

To follow this tutorial: 

- You need the **Content** content model group and **Blog** content model. If you don't have these, please follow the [Define Workflow](/docs/{version}/user-guides/apw/essentials/define-workflow) tutorial to create them.

- You need to have the **John Author** user account in your Webiny instance. If you don't have it, please follow the [Create Users](/docs/{version}/user-guides/apw/essentials/apw-user-guides-outline-users-creation) tutorial to create it.

<Video src={submitRecordForReviewVideo} controls={true}/>

1. Log in with the **John Author** user account.

2. From the **Side Menu**, click **Headless CMS** > **CONTENT** > **Blog**.

    ✔️ The **Blog** screen opens.

3. Click **+ NEW ENTRY**.

    ✔️ The **CONTENT** tab for the new entry opens.

4. In the **Title** textbox, type **Elevate Your Style: 5 Stylish Ways to Wear a Black T-Shirt for Men**.

5. In the **Slug** textbox, type **elevate-your-style-5-stylish-ways-to-wear-a-black-t-shirt-for-men**.

6. In the body of the blog, type the following:

    Fashion for men thrives on versatility, and the black t-shirt stands as a timeless wardrobe essential. Here are five stylish ways to wear a black t-shirt, proving that this simple garment can be the foundation for countless fashionable looks.

    ### **1. Effortless Edge: Monochromatic Mastery**

    Pair your black t-shirt with dark bottoms for a sleek monochromatic look. Whether it's tailored black trousers or black denim, this ensemble exudes confidence. Add a pop of color with statement sneakers or a bold watch to break the monotony. Achieve an effortlessly edgy look suitable for various occasions.

    ### **2. Street-Style Swagger: Tucked Distinction**

    For a casual yet cool vibe, tuck your black t-shirt into distressed jeans. This classic combination creates a laid-back aesthetic perfect for street-style swagger. Complete the look with stylish sneakers or rugged boots. Consider adding a leather bracelet or a beanie for an extra touch of personality.

    ### **3. Relaxed Casual: Knot It Up**

    Give your black t-shirt a relaxed feel by knotting it at the waist. This simple styling trick adds a touch of casual charm. Pair it with joggers or denim shorts for an easygoing vibe. The knotted detail adds visual interest and showcases your laid-back style.

    ### **4. Smart-Casual Sophistication: Blazer Elegance**

    Transform your black t-shirt into a sophisticated ensemble by layering it under a tailored blazer. This combination merges comfort with style, suitable for various occasions. Choose a well-fitted blazer that complements the black hue, creating a sleek appearance. Pair it with tailored chinos or dark denim for a polished finish.

    ### **5. Urban Explorer: Cargo Cool**

    Embrace an urban vibe by pairing your black t-shirt with cargo pants. This combination offers a balance between utility and style. Opt for neutral-colored cargo pants and complete the look with rugged boots or sneakers. Accessorize with a minimalist backpack or a watch for the perfect urban explorer aesthetic.

    In men's fashion, a black t-shirt is a versatile canvas waiting to be styled. Whether you're aiming for monochromatic edge, street-style swagger, relaxed casual, smart-casual sophistication, or urban exploration, the possibilities are vast. Experiment with these stylish ways to wear a black t-shirt, and let your personal style shine through.

7. Click **SAVE & PUBLISH**.

    ✔️The **Request review** screen opens with the message "This content requires peer review approval before it can be published. Do you wish to request a review?"

8. Click **CONFIRM**.

    ✔️ The review screen opens with the message "A peer review is required."