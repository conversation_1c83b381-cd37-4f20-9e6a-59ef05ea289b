---
id: 75e36f89
title: APW User Guides Outline & Users Creation
description: Learn about the APW user guides outline to get maximum out of these tutorials and guides.
---

import { <PERSON><PERSON> } from "@/components/Alert";
import createUsersVideo from "./assets/apw-user-guides-outline-users-creation/apw-create-users.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- structure and outline of APW user guides
- create user accounts to follow through the following tutorials

</Alert>

## APW User Guides Outline

Welcome to Advanced Publishing Workflow (APW) user guides! In this article, you will learn how we have organized the APW user guides and how you can follow them to get the maximum out of them. With APW you can have a workflow-driven publishing process. Whether you prefer a straightforward review and approval process or desire a more intricate workflow with multiple review stages, APW accommodates your publishing needs.

To simplify this APW user guide, we will follow the example below and learn each aspect of APW one by one.  
We will have two users **John Author** and **Adam Reviewer**.
- **<PERSON>** will create a blog.
- And **Adam Reviewer** reviews it and suggests changes.

Here are the steps to follow the APW User Guides:

| Steps                                     | Description |
| :----------------------------------     | :-----------------------------      |
| 1. **[Define a Workflow](/docs/{version}/user-guides/apw/essentials/define-workflow)**                         | In this step, we will define a workflow to be followed in the publishing process. |
| 2. **[Submit Record for Review](/docs/{version}/user-guides/apw/essentials/submit-record-for-review)**         |   **John Author** will create a **Blog** content and will submit it for review. In this step, John will also create a **Blog** content model and **Content** model group. |
| 3. **[Review Record](/docs/{version}/user-guides/apw/essentials/review-record)**                               | **Adam Reviewer** will  review the blog and request a change to update the title of the blog for better SEO. |
| 4. **[Submit Change](/docs/{version}/user-guides/apw/essentials/submit-change)**                               |  **John Author** will change the blog’s title as suggested by **Adam Reviewer** and will submit the changes. |
| 5. **[Provide Sign Off](/docs/{version}/user-guides/apw/essentials/provide-sign-off)**                         | **Adam Reviewer** will review the update change request and provide sign-off to publish the content. |
| 6. **[Schedule Publishing](/docs/{version}/user-guides/apw/essentials/scheduled-publishing)**                   | In this step, **John Author** will schedule a post for publishing. |



## Author & Reviewer Users Creation
As previously mentioned, to proceed with the guides, we require two users: **John Author** and **Adam Reviewer**. Now, let’s create these users first. In this section, we will create the two user accounts. If you are unfamiliar with how to create a user role (formerly called a "group") and a user account, please refer to the [User Creation](/docs/{version}/user-guides/headless-cms/advanced/user-creation) tutorial.

<Video src={createUsersVideo} controls={true}/>

1. Create a user role with the following attributes:

    | Attribute    | Value                                                          |
    | :-------     | :----------------------                                        |
    | Name         | **Headless CMS Access US**                                     |
    | Slug         | **headless-cms-access-us**                                     |
    | Description  | **Grants full access to Headless CMS in en-US locale**         |
    | *Permissions*|                                                                |
    | Content      | Content can be accessed on > **Specific locales** > **en-US**  |
    | Headless CMS | Access Level > **Full access**                                 |

2. Create a user account with the following attributes:

    | Attribute  | Value                     |
    | :-------   | :----------------------   |
    | First Name | **Adam**                  |
    | Last Name  | **Reviewer**              |
    | Email      | *User's email*            |
    | Password   | *User's password*         |
    | Role       | **Headless CMS Access US**|

    **Note**: This user account will act as the reviewer of the blogs in the publishing workflow that we will create in the following tutorials.

3. Create another user account with the following attributes:
    
    | Attribute  | Value                     |
    | :-------   | :----------------------   |
    | First Name | **John**                  |
    | Last Name  | **Author**                |
    | Email      | *User's email*            |
    | Password   | *User's password*         |
    | Role       | **Headless CMS Access US**|

    **Note**: This user account will act as the author of the blogs in the publishing workflow that we will create in the following tutorials.