---
id: 75e36f8c
title: Provide Sign Off
description: Learn how to provide sign off in a Publishing Workflow.
---

import { Al<PERSON> } from "@/components/Alert";
import provideSignOffVideo from "./assets/provide-sign-off/apw-provide-sign-off.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to provide sign off

</Alert>

In this tutorial, we will learn how you can provide sign off to a record under review in a Publishing Workflow.

## Prerequisites

To follow this tutorial:

- You need to have the requested change **Change blog's title for better SEO** resolved by the blog author. If you don't have it, please follow the [Submit Change](/docs/{version}/user-guides/apw/essentials/submit-change) tutorial to resolve it.

- You need to have the **Adam Reviewer** user account in your Webiny instance. If you don't have it, please follow the [Create Users](/docs/{version}/user-guides/apw/essentials/apw-user-guides-outline-users-creation) tutorial to create it.

<Video src={provideSignOffVideo} controls={true}/>

1. Log in with the **Adam Reviewer** user account.

2. From the **Side Menu**, click **Publishing Workflows** > **Content Reviews**.

    ✔️ The **Content review dashboard** screen opens.

2. Click the **Black T-Shirt Styling: 5 Timeless Looks for Men** list item.

    ✔️ The record review screen for the record opens.

3. Click on **SEO Review**.

    ✔️ List of the requested changes in SEO review opens.

4. Click on **Change blog's title for better SEO**.

    ✔️ Full details about the change request appear.

5. Under the **Change blog's title for better SEO** change request's details, click **Mark resolved**.

    ✔️ The message "Successfully marked 'Change blog's title for better SEO' as resolved!" displays.

6. Click **✔️ PROVIDE SIGN OFF**.

    ✔️ The message "Sign off provided successfully!" displays.
