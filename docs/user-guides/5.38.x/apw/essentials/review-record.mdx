---
id: 75e36f88
title: Review Record
description: Learn how to review a record in a Publishing Workflow.
---

import { <PERSON><PERSON> } from "@/components/Alert";
import reviewRecordVideo from "./assets/review-record/apw-review-record.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to review a record
- how to make a change request

</Alert>

In this tutorial, we will learn how you can review a record and make a change request in a Publishing Workflow.

## Prerequisites

To follow this tutorial:

- You need to have the record **Elevate Your Style: 5 Stylish Ways to Wear a Black T-Shirt for Men** submitted for review. If you don't have it, please follow the [Submit Record for Review](/docs/{version}/user-guides/apw/essentials/submit-record-for-review) tutorial to submit the record for review.

- You need to have the **Adam Reviewer** user account in your Webiny instance. If you don't have it, please follow the [Create Users](/docs/{version}/user-guides/apw/essentials/apw-user-guides-outline-users-creation) tutorial to create it.

<Video src={reviewRecordVideo} controls={true}/>

1. Log in with the **Adam Reviewer** user account.

2. From the **Side Menu**, click **Publishing Workflows** > **Content Reviews**.

    ✔️ The **Content review dashboard** screen opens.

3. Click the **Elevate Your Style: 5 Stylish Ways to Wear a Black T-Shirt for Men** list item.

    ✔️ The record review screen for the record opens.

4. In front of the title **Elevate Your Style: 5 Stylish Ways to Wear a Black T-Shirt for Men**, click the **external link** icon.

    ✔️ The **Blog** editor screen opens in a new tab.

    **Note**: If you want to make any changes in the record by yourself, you can make them and save them by clicking **SAVE**.

5. On the record review screen, click **➕ REQUEST CHANGE**.

    ✔️ The **Change request** screen opens.

6. In the **Title** textbox, type **Change blog's title for better SEO**.

7. In the **Body** textbox, type **Change the title of the blog to "Black T-Shirt Styling: 5 Timeless Looks for Men"**.

8. Click **SUBMIT**.

    ✔️ The message "Change request created successfully!" displays.

