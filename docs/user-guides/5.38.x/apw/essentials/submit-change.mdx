---
id: 75e36f8b
title: Submit Change
description: Learn how to submit requested change in a Publishing Workflow.
---

import { Al<PERSON> } from "@/components/Alert";
import submitChangeVideo from "./assets/submit-change/apw-submit-change.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to submit requested change

</Alert>

In this tutorial, we will learn how you can view a requested change in a record, make the change, and submit the change in a Publishing Workflow.

## Prerequisites

To follow this tutorial:

- You need to have the change request **Change blog's title for better SEO** made by a reviewer. If you don't have it, please follow the [Review Record](/docs/{version}/user-guides/apw/essentials/review-record) tutorial to create the change request.

- You need to have the **John Author** user account in your Webiny instance. If you don't have it, please follow the [Create Users](/docs/{version}/user-guides/apw/essentials/apw-user-guides-outline-users-creation) tutorial to create it.
<Video src={submitChangeVideo} controls={true}/>

1. Log in with the **John Author** user account.

2. From the **Side Menu**, click **Publishing Workflows** > **Content Reviews**.

    ✔️ The **Content review dashboard** screen opens.

3. Click the **Elevate Your Style: 5 Stylish Ways to Wear a Black T-Shirt for Men** list item.

    ✔️ The record review screen for the record opens.

4. Click on **SEO Review**.

    ✔️ List of the requested changes in SEO review opens.

5. Click on **Change blog's title for better SEO**.

    ✔️ Full details about the change request appear.

6. In front of the title **Elevate Your Style: 5 Stylish Ways to Wear a Black T-Shirt for Men**, click the **external link** icon.

    ✔️ The **Blog** editor screen opens in a new tab.

7. In the **Title** textbox, update the text to **Black T-Shirt Styling: 5 Timeless Looks for Men**.

8. In the **Slug** textbox, update the text to **black-t-shirt-styling-5-timeless-looks-for-men**.

9. Click **SAVE**.

    ✔️ The message **Content saved successfully.** displays.

10. On the record review screen, under the **Change blog's title for better SEO** change request's details, in the message texbox with the placeholder **Type something to send...**, type **Requested change made**.

11. Click the **SEND** icon.

    ✔️ The message gets sent and logged under the change request's details. 

