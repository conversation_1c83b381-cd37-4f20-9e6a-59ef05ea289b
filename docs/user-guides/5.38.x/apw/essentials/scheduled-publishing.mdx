---
id: 75e36f8d
title: Scheduled Publishing
description: Learn how to schedule a content to be published at a certain time and date.
---

import { <PERSON><PERSON> } from "@/components/Alert";
import schedulePublishingVideo from "./assets/schedule-publishing/apw-schedule-publishing.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to schedule a content to be published

</Alert>

## Prerequisites

To follow this tutorial:

- You need content that will be scheduled. If you don’t have any content, please follow [this tutorial](/docs/{version}/user-guides/apw/essentials/provide-sign-off) to have Black T-Shirt Styling: 5 Timeless Looks for Men blog.

- You need to have the **John Author** user account in your Webiny instance. If you don't have it, please follow the [Create Users](/docs/{version}/user-guides/apw/essentials/apw-user-guides-outline-users-creation) tutorial to create it.

<Video src={schedulePublishingVideo} controls={true}/>

1. Log in with the **John Author** user account.

2. From the **Side Menu**, click **Publishing Workflows** > **Content Reviews**.

    ✔️ The **Content review dashboard** screen opens.

3. Click the **Black T-Shirt Styling: 5 Timeless Looks for Men** list item.

    ✔️ The content review screen opens.

4. Click **PUBLISH CONTENT**.

    ✔️ The **Publish Content** screen opens.

5. Click **SCHEDULE PUBLISH**.

    ✔️ The **Set Schedule** screen opens.

6. In the **Date** field, select/type the *publishing date* (e.g. 01-01-2024).

7. In the **Time** field, select/type the *publishing time* (e.g. 19:00).

8. Click **SCHEDULE**.

    ✔️ The message "Content published successfully!" displays.
