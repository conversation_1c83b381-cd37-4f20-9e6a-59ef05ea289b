---
id: 75e36f8a
title: Define Workflow
description: Learn how to define a workflow in the Advanced Publishing Workflow (APW).
---

import { Al<PERSON> } from "@/components/Alert";
import defineWorkflowVideo from "./assets/define-workflow/apw-define-workflow.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to define a publishing workflow

</Alert>

Webiny APW enables you to easily create a customize publishing workflows tailored to your specific content creation and review requirements. You can add as many review steps as necessary and define the scope of the workflow to suit your requirements. In this tutorial, we will learn how to define a workflow. We will do this in 3 steps:

- Step 1: Create a content model group and a content model
- Step 2: Create a publishing workflow
- Step 3: Define the workflow

## Prerequisites

To follow this tutorial, you need to have the **Adam Reviewer** user account in your Webiny instance. If you don't have it, please follow the [Create Users](/docs/{version}/user-guides/apw/essentials/apw-user-guides-outline-users-creation) tutorial to create it.

<Video src={defineWorkflowVideo} controls={true}/>

## Step 1: Create a Content Model Group and a Content Model

If you are not familiar with how to create a content model group and a content model, please follow the [Create Content Model Group](/docs/{version}/user-guides/headless-cms/essentials/create-content-model-group) and [Create Content Model](/docs/{version}/user-guides/headless-cms/essentials/create-content-model) tutorials.

1. Create a content model group with the following attributes:

    | Attribute   | Value                                                                |
    | :-------    | :----------------------                                              |
    | Name        | **Content**                                                          |
    | Group icon  | **✏️**                                                               |
    | Description | **Group for all types of content to be published in the application**|

2. Create a content model with the following attributes:

    | Attribute            | Value                                       |
    | :-------             | :----------------------                     |
    | Name                 | **Blog**                                    |
    | Singular API Name    | **Blog**                                    |
    | Plural API Name      | **Blogs**                                   |
    | Content model group  | **Content**                                 |
    | Icon                 | **📰**                                      |
    | Description          | **Blogs to be published in the application**|

3. Add the following fields with the given attributes to the **Blog** content model:

    | Field     | Label     | Field ID |
    | :-------  | :---------| :------  |
    | TEXT      | **Title** | title    |
    | TEXT      | **Slug**  | slug     |
    | RICH TEXT | **Body**  | body     |


## Step 2: Create a Publishing Workflow

1. From the **Side Menu**, click **Publishing Workflows** > **Workflows**.

    ✔️ The **Publishing Workflows** screen opens.

2. In front of **Headless CMS**, click **+ NEW WORKFLOW**.

    ✔️ **Untitled** form opens.

3. To rename the workflow, click the workflows's title **Untitled**.

    ✔️ A textbox appears.

4. In the textbox, update the text to **Blog Publishing Workflow**.

5. Click anywhere outside the textbox.


## Step 3: Define the Workflow

1. Inside the **Workflow steps** accordion, under **Step 1**, in the **Title** textbox, type **SEO Review**

2. In the **Type** dropdown, click **Mandatory, non-blocking**.

    **Note**: There are three options in the **Type** dropdown:

    - **Mandatory, blocking**: The review process cannot proceed until this step is completed. It serves as a mandatory and blocking phase.
    - **Mandatory, non-blocking**: Other steps are unlocked in the review process, but item publication awaits completion of all mandatory, non-blocking steps.
    - **Optional**: This step doesn't impede the review process, and the item can be published regardless of sign off. Choose this for steps that are supplementary or non-essential to the overall workflow. 
    

3. In the **Reviewers** section, select the **Adam Reviewer** checkbox.

    **Note**: You can add more steps to your workflow as per your requirements by clicking **+ ADD STEP** and following the Steps 1 to 3.

4. Click the **Scope** accordion.

    ✔️ The **Scope** accordion opens.

5. Inside the **Scope** accordion, in the **Type** dropdown, click **Specific models and entries**.

    ✔️ The **CMS MODELS** tab appears.

6. In the **CMS MODELS** tab, select the **Blog** checkbox.

7. Click **SAVE**.

    ✔️ The message "Workflow saved successfully." displays.



    