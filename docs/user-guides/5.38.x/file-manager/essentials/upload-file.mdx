---
id: 2f7200c2
title: File Upload
description: Learn how to upload a file in File Manager.
---

import { Al<PERSON> } from "@/components/Alert";
import uploadFileExplorerVideo from "./assets/upload-file/upload-file-file-explorer.mp4";
import uploadFileDragDropVideo from "./assets/upload-file/upload-file-drag-and-drop.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to upload a file

</Alert>

Webiny allows you to effortlessly upload files into the File Manager that you can use across your application. In this tutorial, we will learn how to upload a file into the File Manager. As an example, we will upload the image shown below. Please save it on your computer to upload later.

![Men's Red Tshirt](./assets/upload-file/red-mens-tshirt.png)

There are two ways to upload a file in File Manager; let's look at each approach one by one.

## Approach 1: Through file explorer

<Video src={uploadFileExplorerVideo} controls={true}/>

1. From the **Side Menu**, click **File Manager**.

    ✔️ The **File Manager Home** screen opens.

2. Click **UPLOAD...**.

    ✔️ The file explorer screen opens.

3. From the file explorer, upload the image.

    ✔️ The message "File upload complete." displays.


## Approach 2: Drag and drop

<Video src={uploadFileDragDropVideo} controls={true}/>

1. From the **Side Menu**, click **File Manager**.

    ✔️ The **File Manager Home** screen opens.

2. Drag and drop the image from your computer's file explorer to the **Home** screen.

    ✔️ The message "File upload complete." displays.