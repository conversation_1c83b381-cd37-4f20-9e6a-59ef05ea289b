---
id: 
title: Create a User
description: Learn how to create a user.
---
import { Alert } from "@/components/Alert";
import userCreationVideo from "./assets/user-creation/create-user.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to create a user

</Alert>

Webiny users can access the admin application. You can assign users to specific roles or teams to grant them different levels of permissions and access within the application.
In this guide, we will learn how to create a user, and here are additional guides that explain more about [Roles](/docs/{version}/user-guides/security/essentials/role-creation) and [Teams](/docs/{version}/user-guides/security/essentials/team-creation).

<Video src={userCreationVideo} controls={true}/>

1. From the **Side Menu**, click **Settings** > **ADMIN USERS** > **Users**

2. Click **+ NEW USER**.

   ✔️ The **New User** screen opens.

3. In the **First Name** textbox, type _new user's first name_ (e.g. <PERSON>).

4. In the **Last Name** textbox, type _new user's last name_ (e.g. <PERSON><PERSON>).

5. In the **Email** textbox, type _new user's email_ (e.g. <EMAIL>).

6. In the **Password** textbox, type _new user's password_.

7. Click **SAVE USER**.

   ✔️ The message "User saved successfully." displays.