---
id: 440cef17
title: Team Creation
description: Learn how to create a Team.
---

import { <PERSON><PERSON> } from "@/components/Alert";
import teamCreationVideo from "./assets/team-creation/create-team.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to create a team

</Alert>

Webiny enables you to define a team for a group of users in your application. You can provide the access of multiple user roles to the group by creating a Team. In this tutorial, we will learn how to create a Team.

<Alert type="info">

  Note that the Teams feature is part of our enterprise offering. To learn more, please check out the [Teams](/docs/enterprise/aacl/teams) article.

</Alert>

## Prerequisites

To follow this tutorial, you need to have the **E-commerce** user role in your Webiny instance. If you don't have it, please follow the [Creation of Role](/docs/{version}/user-guides/security/essentials/role-creation) tutorial to create it.

<Video src={teamCreationVideo} controls={true}/>

1. From the **Side Menu**, click **Settings** > **ACCESS MANAGEMENT** > **Teams**.

2. Click **+ NEW TEAM**.

   ✔️ The form to create a new team opens.

3. In the **Name** textbox, type **E-commerce L2 Managers**.

4. In the **Slug** textbox, type **e-commerce-l2-managers**.

5. In the **Description** textbox, type **Team of level-2 E-Commerce Managers**.

6. In the **Roles** dropdown, select **E-Commerce**.

    **Note**: You can select multiple roles in the **Roles** dropdown.

7. Click **SAVE TEAM**
    
    ✔️ The message "Team saved successfully!" displays.
