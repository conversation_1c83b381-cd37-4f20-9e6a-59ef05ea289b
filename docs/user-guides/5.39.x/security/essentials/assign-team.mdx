---
id: 440cee2e
title: Assign Team to a User 
description: Learn how to assign a team to a user.
---

import { Alert } from "@/components/Alert";
import assignTeamVideo from "./assets/assign-team/assign-team-to-user.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to assign a team to a user

</Alert>

In this tutorial, we will learn how to assign a team to a user.

<Alert type="info">

  Note that the Teams feature is part of our enterprise offering. To learn more, please check out the [Teams](/docs/enterprise/aacl/teams) article.

</Alert>

## Prerequisites

To follow this tutorial, you need to have the **E-commerce L2 Managers** team and the **<PERSON>** user in your Webiny instance. If you don't have them, please follow the [Creation of Team](/docs/{version}/user-guides/security/essentials/team-creation) and [Creation of User](/docs/{version}/user-guides/security/essentials/user-creation) tutorials to create it.

<Video src={assignTeamVideo} controls={true}/>

1. From the **Side Menu**, click **Settings** > **ADMIN USERS** > **Users**

2. Click the **<PERSON>** in the list of Users.

3. In the **Team** dropdown, click **E-commerce L2 Managers**.

4. Click **SAVE USER**.

   ✔️ The message "User saved successfully." displays.
