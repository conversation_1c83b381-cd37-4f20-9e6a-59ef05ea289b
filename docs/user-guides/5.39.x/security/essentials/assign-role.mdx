---
id: 
title: Assign Role to a User
description: Learn how to assign a role to a user.
---

import { Al<PERSON> } from "@/components/Alert";
import assignRoleVideo from "./assets/assign-role/assign-role-to-user.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to assign a role to a user

</Alert>

In this tutorial, we will learn how to assign a role to a user.

## Prerequisites

To follow this tutorial, you need to have the **E-commerce** role and the **<PERSON>** user in your Webiny instance. If you don't have them, please follow the [Creation of Role](/docs/{version}/user-guides/security/essentials/role-creation) and [Creation of User](/docs/{version}/user-guides/security/essentials/user-creation) tutorials to create it.

<Video src={assignRoleVideo} controls={true}/>

1. From the **Side Menu**, click **Settings** > **ADMIN USERS** > **Users**

2. Click the **<PERSON>** in the list of Users.

3. In the **Role** dropdown, click **E-commerce**.

4. Click **SAVE USER**.

   ✔️ The message "User saved successfully." displays.