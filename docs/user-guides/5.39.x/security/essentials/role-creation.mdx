---
id: 
title: Create a Role
description: Learn how to create a Role.
---

import { <PERSON><PERSON> } from "@/components/Alert";
import roleCreationVideo from "./assets/role-creation/create-user-role.mp4";

<Alert type="success" title="WHAT YOU'LL LEARN">

- how to create a Role
- how to define access permissions for a Role

</Alert>


<Alert type="info" title="Can I use this?">


In the Open Source version, you'll have basic roles and permissions, where users either have full access or no access to a particular application.  
To access the advanced roles and permissions feature, you need a Webiny Enterprise or Webiny Business license.

</Alert>

You can manage access permissions for users with Roles. In a Role, you define various access permissions, and then assign this Role to users.
In this tutorial, we will learn how to create a Role with custom access permission to the Headless CMS. We will do this in 2 steps:

- Step 1: Create a Role.
- Step 2: Define access permissions.

As an example, we will create a Role with the following access permissions:

- Permission to view and update a specific content model group.
- Permission to view, create, and update all content models inside the content model group.
- Permission to view, create, and update the content entries inside the content models.

## Prerequisites

To follow this tutorial, you need to have a content model group named **E-commerce** in your Webiny instance. If you don't have it, please follow the [Create Content Model Group](/docs/{version}/user-guides/headless-cms/essentials/create-content-model-group) tutorial to create it.

<Video src={roleCreationVideo} controls={true}/>

## Step 1: Create a Role

1. From the **Side Menu**, click **Settings** > **ACCESS MANAGEMENT** > **Roles**.

2. Click **+ NEW ROLE**.

   ✔️ The form to create a new Role opens.

3. In the **Name** textbox, type **E-Commerce**.

4. In the **Slug** textbox, type **e-commerce**.

5. In the **Description** textbox, type **User Role for E-Commerce Managers**.


## Step 2: Define Access Permissions

1. Under the **Permissions** section, in the **Content** accordion, click **All locales**.

2. Under the **Permissions** section, click **Headless CMS**.

   ✔️ The **Headless CMS** accordion opens.

3. In the **Access Level** dropdown, click **Custom access**.

4. Under **GRAPHQL API TYPES**, select all the three checkboxes - **Read**, **Manage**, and **Preview**.

5. Under the **CONTENT MODEL GROUPS** section:

   a. In the **Access Scope** dropdown, click **Only specific groups**.

   ✔️ A list of content model groups in the current locale appears.

   b. Select the **E-Commerce** checkbox.

   c. In the **Primary Actions** dropdown, click **Read, write**.

6. Under the **CONTENT MODELS** section:

   a. In the **Access Scope** dropdown, click **All models**.

   b. In the **Primary Actions** dropdown, click **Read, write**.

7. Under the **CONTENT ENTRIES** section:

   a. In the **Access Scope** dropdown, click **All entries**.

   b. In the **Primary Actions** dropdown, click **Read, write**.

8. Under **PUBLISHING ACTIONS**, select **Publish** and **Unpublish** checkboxes.

9. Click **SAVE ROLE**.

   ✔️ The message "Role saved successfully!" displays.
