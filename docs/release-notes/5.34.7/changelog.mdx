---
id: changelog
title: Webiny 5.34.7 Changelog
description: See what's new in Webiny version 5.34.7.
---

## Development

### Improved Error Reporting ([#3122](https://github.com/webiny/webiny-js/pull/3122))

Prior to this release, runtime errors occurring within the deployed **API** project application would get incorrectly logged. Instead of being logged as errors, they would be logged as regular info messages.

Because of this, implementing observability tools and even basic application monitoring on top of an existing Webiny project would not be as easy as it should be. Users would manually have to decide which logs should be considered as errors, and which shouldn't be.

By revisiting existing logs and updating their types accordingly, this should be a much more straightforward task.
