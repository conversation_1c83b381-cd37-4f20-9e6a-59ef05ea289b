---
id: aafea9ae
title: Upgrade from 5.33.5 to 5.34.0
description: Learn how to upgrade Webiny from 5.33.5 to 5.34.0.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.33.0 to 5.34.0

</Alert>

<Alert type="info">

Make sure to check out the [5.34.0 changelog](/docs/release-notes/5.34.0/changelog) to get familiar with the changes introduced in this release.

</Alert>

## 1. Upgrade Webiny Packages

Upgrade all `@webiny/*` packages by running the following command:

```bash
yarn up "@webiny/*@5.34.0"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return `5.34.0`.

## 2. Run the Upgrade Command

The next step is to run the project upgrade:

```bash
yarn webiny upgrade
```

<Alert type="info">
  This script will create backups of some files, so make sure you review the changes, and manually
  apply any changes you might have in those files to the new files.
</Alert>

<Alert type="warning">
  If the upgrade command ends in error "<strong><PERSON><PERSON><PERSON> does not exist.</strong>", please clear your
  npx cache. You can run <strong>npx clear-npx-cache</strong> to clear it, or just delete it
  manually from your npx directory.
</Alert>

## 3. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

Once deployed, access your Admin app to finalize the Page Builder upgrade, which will migrate your existing page blocks to the new Block Manager.

<Alert type="warning">

Make sure you deploy the entire system using the command shown above! You have to deploy all apps before using the system. Partial deploys may cause the upgrade to be applied incorrectly.

</Alert>

<Alert type="warning">

As stated in the [Upgrade Webiny](/docs/release-notes/upgrade-webiny#pre-production-environments-first) section, we recommend that you first deploy your changes into one of your pre-production environments, like `dev` or `staging`.

</Alert>

<Alert type="info">

Learn more about different deployment environments in the [CI/CD / Environments](/docs/core-development-concepts/ci-cd/environments) key topic.

</Alert>

## 4. Final Words

Due to the vast amount of potential customizations in the user land, it is impossible to create a 100% reliable upgrade process to satisfy everyone. If you run into any build issues, Typescript errors, etc., don't hesitate to reach out on our [Slack Community](https://webiny.com/slack) with details of your issue (post text, screenshots, whatever you have), and we'll help you out with your upgrade.
