---
id: aafea9dd
title: Upgrade from 5.11.0 to 5.11.1
description: Learn how to upgrade Webiny from 5.11.0 to 5.11.1.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.11.0 to 5.11.1

</Alert>

<Alert type="danger">

Before continuing, make sure to take the necessary precautions, listed in the [Overview](/docs/release-notes/upgrade-webiny#-precaution-measures) section.

</Alert>

<Alert type="info">

Make sure to check out the [5.11.1 changelog](/docs/release-notes/5.11.1/changelog) to get familiar with all the changes introduced in this release.

</Alert>

## 1. Upgrade Webiny Packages

Upgrade all `@webiny/*` packages by running the following command:

```bash
yarn up "@webiny/*@5.11.1"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return `5.11.1`.

## 2. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

<Alert type="warning">

As stated in the [Upgrade Webiny](/docs/release-notes/upgrade-webiny#pre-production-environments-first) section, we recommend that you first deploy your changes into one of your pre-production environments, like `dev` or `staging`.

</Alert>

<Alert type="success">

Learn more about different deployment environments in the [CI/CD / Environments](/docs/core-development-concepts/ci-cd/environments) key topic.

</Alert>

## 3. Open Admin Area App

Finally, open the Admin Area app and execute the upgrade script when prompted. The upgrade process will remove the `password` attribute from all user records in your DynamoDB (for details, see the [changelog](/docs/release-notes/5.11.1/changelog#remove-password-attribute-from-dynamodb-1842)).
