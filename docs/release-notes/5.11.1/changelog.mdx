---
id: aafea9dc
title: Webiny 5.11.1 Changelog
description: See what's new in Webiny version 5.11.1.
---

import { <PERSON>ert } from "@/components/Alert";

# Changes

This document highlights the most important fixes, improvements, and features, that were introduced in Webiny `5.11.1`.

<Alert type="info" title="How To Upgrade?">

Please check the [Webiny 5.11.1 migration guide](/docs/release-notes/5.11.1/upgrade-guide) for the upgrade steps.

</Alert>

## Security

### Remove Password Attribute from DynamoDB ([#1842](https://github.com/webiny/webiny-js/pull/1842))

Recently, we introduced a `UserPlugin`, which you can use in the main Webiny GraphQL API to hook into user's lifecycle hooks, like `beforeCreate`, `afterDelete`, etc. Cognito plugin uses this `UserPlugin` to hook into user creation process and synchronizes users with the appropriate Cognito User Pool. During this refactor, we failed to unset the `password` field once it's been consumed by the Cognito plugin, and so, it ended up being stored to DynamoDB, alongside other data that belongs to user.

Our philosophy is to _never_ handle passwords ourselves, never store them into our database, or any other storage. Passwords should be handled by identity providers, exclusively.

This bug was introduced with Webiny `v5.9.0` and has been present through `v5.10.0`, and `v5.11.0`. If you're using one of these versions, we _strongly recommend_ upgrading to the latest `v5.11.1`.
