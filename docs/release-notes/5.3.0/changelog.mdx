---
id: aafea9ed
title: Webiny 5.3.0 Changelog
description: See what's new in Webiny version 5.3.0.
---

# Changes

This document highlights the most important fixes, improvements, and features, that were introduced in Webiny `5.3.0`.

## Headless CMS

### Improved the `contains` GraphQL Operator [(#1531)](https://github.com/webiny/webiny-js/pull/1531)

While querying data via the [Headless CMS GraphQL API](/docs/headless-cms/basics/graphql-api), and using the `contains` query operator on a **text** field, [reserved ElasticSearch characters](https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-query-string-query.html#_reserved_characters) are now properly escaped. Without it, users could receive unexpected results or even errors, thrown by the ElasticSearch itself.

### Fixed Search Bar [(#1532)](https://github.com/webiny/webiny-js/pull/1532)

A couple of fixes were implemented on the search bar, located in the content entries view:

- if a search term was typed into the search bar's input, switching to a different content entries section via the main menu no longer causes an infinite redirect loop in the browser
- results don't disappear when clearing up the search bar's input

![Headless CMS - Fixed Search Bar](./search-bar-cms.png)

## Webiny CLI

### Improved Error Reporting [(#1526)](https://github.com/webiny/webiny-js/pull/1526)

We've noticed that, in certain cases, it can get a bit hard to understand the error that occurred in the terminal. A typical example is the `SubscriptionRequiredException` error, which can happen upon deploying your project, and which, without doing some googling, means nothing to the user.

So, from now on, whenever there's a chance to provide additional information about the received error, we provide it, along with a link to a helpful resource, like an article on our docs website. For example:

![Webiny CLI - Improved Error Reporting](./cli-reporting.png)

## `create-webiny-project`

### Added the `--no-cleanup` flag [(#1526)](https://github.com/webiny/webiny-js/pull/1526)

Upon creating a new Webiny project with the `create-webiny-project` tool, sometimes, users experience errors. And when that happens, the tool would, be default, automatically cleanup all of the files that were generated in the process.

With the newly added `--no-cleanup` flag, this can now be prevented, allowing the user to check the contents of generated files, and with that, maybe provide more info while reporting and debugging these errors.

The `--no-cleanup` flag can be used as follows:

```bash
npx create-webiny-project my-project-folder --no-cleanup
```

# How To Upgrade

If you are already using Webiny version 5.2.x, there are no special migration steps that need to be taken. So, if you are ready to upgrade, in your terminal of choice, simply run the following commands:

- `yarn up "@webiny/*@5.3.0"`
- `yarn webiny deploy`

On the other hand, if you’re coming with an older version of Webiny, before upgrading, make sure to check our [How To Upgrade](/docs/release-notes/upgrade-webiny) guide.
