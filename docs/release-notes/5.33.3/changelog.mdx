---
id: changelog
title: Webiny 5.33.3 Changelog
description: See what's new in Webiny version 5.33.3.
---

import objectAppearance from "./assets/object-appearance-tab.png";
import collapsedObject from "./assets/collapsed-objects.png";
import expandedObject from "./assets/expanded-objects.png";
import fieldRenderer from "./assets/object-renderer-name.png";

## Development

### Webiny Production Mode - All AWS Lambda Functions Are Now Assigned to VPC ([#2798](https://github.com/webiny/webiny-js/pull/2798))

When deploying your Webiny project in [production mode](/docs/architecture/introduction#development-and-production-deployment-modes), for better security posture, all AWS Lambda functions are deployed into a [Virtual Private Cloud (VPC)](https://aws.amazon.com/vpc/) with private subnets.

And while our initial intention was to have this cloud infrastructure configuration automatically be applied when a project is being [deployed](/docs/core-development-concepts/basics/project-deployment#the-deploy-command) into the production environment, due to a issue in an internal Webiny package, that just wasn't the case.

With this release, the issue has been resolved. You can expect all the relevant AWS Lambda functions to be correctly deployed into a VPC when deploying your project into the production environment.

<Alert type="info">

To learn more about development and production deployment modes, check out the [Deployment Modes](/docs/architecture/deployment-modes/introduction) section.

</Alert>

### Add Support for Accordion Object Field Renderer ([#2807](https://github.com/webiny/webiny-js/pull/2807))

Recently, we've seen Webiny Headless CMS being used to build a rather massive content model, with over 500 fields, and a huge amount of multi-level object fields. To make such content models usable for content writers, we've introduced a new renderer for the `object` field, which allows you to render `object` fields within an accordion. This greatly improves the form layout and meaningful separation and grouping of fields.

<Image title="Object Field Renderers" src={objectAppearance} />
<Image title={"Collapsed Accordion"} src={collapsedObject} />
<Image title={"Expanded Accordion"} src={expandedObject} />

### Disable "ripple" Effect and Minor UI Optimizations ([#2799](https://github.com/webiny/webiny-js/pull/2799))

This change is related to the previous changelog item. We mentioned that some users create massive content models, and as a consequence, a huge amount of UI elements get rendered on the screen. One of such forms can heavily slow down your browser, to a point where the browser hangs for a long time (up to a minute). We did in-depth investigation, and we were surprised to narrow down the root cause of the problem to two seemingly irrelevant things: the "ripple" effect (a Material Design interaction eye candy), and input "autofocus".

Turns out, the "ripple" effect is very resource intensive. Simply turning it off can make your application a lot more lively, especially on UI heavy views. We've permanently disabled the "ripple" effect for Webiny, and if this poses a problem for you, let us know on Slack or via Github issues.

The "autofocus" on inputs was a bit more of a mystery. For some unexplained reason, the last object field in the form would trigger the `onBlur` callbacks because all those inputs have `autoFocus` set to `true`. That would trigger input validation, and update of form's internal state, which in turn, re-renders all the fields in the form since they're connected through React Context. We still don't know why that was happening, so we removed the `autoFocus` from inputs in the content entry form to bypass the issue for the time being.

### Report Invalid Part of the GraphQL Schema ([#2801](https://github.com/webiny/webiny-js/pull/2801))

GraphQL schema in Webiny is constructed from dozens of plugins, where each plugin provides a portion of the entire schema, and those portions are then glued together. When a syntax error is encountered, it is hard to debug, because the error message is very obscure, and contains no helpful information, especially because your schema is not located in one single file, but is spread across packages.

To make debugging of such cases a bit easier, we've added schema error handling to the `@webiny/api-headless-cms` package, which is very often modified via plugins and programmatic content models, and as such, is often under risk of causing syntax errors. The error that is caught will now be logged to your AWS CloudWatch, and will show you a human-readable snippet of schema, which should give you a good idea of where to start looking for a problem. The snippet will look something like the following one:

```
2022-11-23T11:39:51.099Z	aea8478b-1fde-4f35-a00e-2e00a2e276fd	INFO
   1432 |             type Test_Config {
   1433 |
 > 1434 |             }
        |             ^
   1435 |
```

In this particular example, you will immediately notice that this GraphQL type is missing type fields, which is not allowed by GraphQL.

### Show Content Model Field Renderer Name

When a content model contains dozens, or worse, hundreds of fields, it becomes hard to verify what renderer is assigned to each individual field. To resolve this problem, we've added the name of the active field renderer to the field widget itself, so it is very clear what renderer will be used in the content entry form.

<Image title="Assigned Field Renderer" src={fieldRenderer} />
