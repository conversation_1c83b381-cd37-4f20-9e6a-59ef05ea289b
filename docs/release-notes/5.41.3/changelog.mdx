---
id: 8048ee96
title: Webiny 5.41.3 Changelog
description: See what's new in Webiny version 5.41.3
---

<GithubRelease version={"5.41.3"} />

## Headless CMS

### Added a Missing Dynamic Zone Storage Transform ([#4399](https://github.com/webiny/webiny-js/pull/4399)

With this patch, we added a missing storage transform was added for the `dynamicZone` field type. A missing storage transform was manifesting an error when trying to save a `dynamicZone` field with a `datetime` field with `without timezone` setting.

Now the system stores the `datetime` field as expected.

## Deployments

### Amazon Cloudfront Distributions - Further Ignoring Changes In `staging` Property ([#4414](https://github.com/webiny/webiny-js/pull/4414))

With the 5.41.2 release, we released the [Amazon Cloudfront Distributions - Ignore Changes in staging Property](/docs/release-notes/5.41.2/changelog#website-cloudfront-distributions-ignore-changes-in-staging-property-4401) fix. 

However, we missed a few places where the `staging` property was not being ignored. The initial fix was only applying the fix for Amazon CloudFront distributions that are deployed as part of the Website project application. API and Admin CloudFront distributions were not covered by the initial fix.

With this patch, we are now ignoring the `staging` property for all CloudFront distributions.

<Alert>

  For more details on the actual issue and the fix, please refer to the original [Website Amazon Cloudfront Distributions - Ignore Changes in staging Property](/docs/release-notes/5.41.2/changelog#website-cloudfront-distributions-ignore-changes-in-staging-property-4401) changelog item.

</Alert>
