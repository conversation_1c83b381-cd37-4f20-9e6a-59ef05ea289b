---
id: changelog
title: Webiny 5.37.0 Changelog
description: See what's new in Webiny version 5.37.0.
---

import { Gallery } from "@/components/Image";
import pageElementAccordion from "./assets/page-element-accordion.png";
import pageElementTab from "./assets/page-element-tab.png";
import pageElementCarousel from "./assets/page-element-carousel.png";
import aaclCustomPermissions from "./assets/aacl-custom-access.png";
import aaclCustomAccess from "./assets/aacl-custom-access.png";
import aaclCustomAccessUpgrade from "./assets/aacl-custom-access-upgrade.png";
import aaclTeams from "./assets/aacl-teams.png";
import aaclRolesRename from "./assets/aacl-roles-rename.png";
import fbDateTimeField from "./assets/fb-date-time-field.png";
import fbGaTrigger from "./assets/fb-ga-trigger.png";
import pbFixesButtonCursor from "./assets/pb-fixes-button-cursor.png";
import pbFixesRevisionsList from "./assets/pb-fixes-revisions-list.png";
import pbFixesLinkedIn from "./assets/pb-fixes-linked-in.png";
import pbCellMirroring from "./assets/pb-cell-mirroring.png";
import pbFixesElementTypesHumanReadable from "./assets/pb-fixes-element-types-human-readable.png";
import pbFixesDndImprovedMessages from "./assets/pb-fixes-dnd-improved-messages.png";
import pbFixesUnlinkingTpl from "./assets/pb-fixes-unlinking-tpl.png";
import hcmsAcoOverview from "./assets/hcms-aco-overview.png";
import hcmsActions from "./assets/hcms-actions.png";
import hcmsFilters from "./assets/hcms-filters.png";
import hcmslexicalEditor from "./assets/hcms-lexical-editor.png";
import hcmsLegacyRteLook from "./assets/hcms-legacy-rte-look.png";
import fmGridView from "./assets/fm-grid-view.png";
import fmTableView from "./assets/fm-table-view.png";
import fmFileDetails from "./assets/fm-file-details.png";

<GithubRelease version={"5.37.0"} />

## File Manager

This release marks a major milestone for our File Manager app. We've made significant UI improvements, and exposed various developer-oriented APIs. The following are some of the improvements that were introduced:

- advanced file organization using folders
- ability to add custom filters (via code plugins)
- ability to extend file data with custom fields (and use them for filtering and searching)
- ability to download the original file
- redesigned file details view
- support for `webp` files
- filtering using tags using `Match all` or `Match any` mode
- programmatic API to interact with the File Manager from your React plugins

<Gallery>
  <Image title={"Grid View"} src={fmGridView} />
  <Image title={"Table View"} src={fmTableView} />
  <Image title={"File Details"} src={fmFileDetails} />
</Gallery>

All of these improvements take our File Manager to the next level. But the big change that enabled us to do many of the things listed above, is under the hood of it all: File Manager is now powered by our own Headless CMS! Each file record is actually a content entry under the hood, which means that File Manager API supports all the same querying capabilities as our Headless CMS.

<Alert type="info" title={"Files Migration Included!"}>
  This release comes with a data migration which will automatically migrate all of your existing
  files to CMS records, which will immediately make them available in the new File Manager!
</Alert>

## Headless CMS

### Introducing Advanced Content Organization for Headless CMS ([#3356](https://github.com/webiny/webiny-js/pull/3356))

The Advanced Content Organization (ACO) for Headless CMS is a new feature that allows users to organize content in a practical, easy-to-manage way. The ACO enables users to add entries in separate folders and rearrange content as needed, making it easier to find what they are looking for.

<Image src={hcmsAcoOverview} title={"Advanced Content Organization for Headless CMS Overview"} />

**With the new Headless CMS, users can:**

1. Create and update a multi-level folder structure.
2. Sort folders and entries based on "Name" and "Last Modified". This feature makes it easier for users to find specific entries and folders quickly.
3. Move entries across the hierarchy tree.

### Headless CMS Entry List and Editor Customisation ([#3391](https://github.com/webiny/webiny-js/pull/3391))

The Headless CMS offers various default features that developers may want to tweak, eliminate, or add new ones to enhance the user experience and cater to the specific requirements of their users.

With this release, we present developers with a fresh approach to engage with a few of the Headless CMS's core features:

1. [Entry List Filters](/docs/headless-cms/extending/customize-entry-list-filters)
2. [Entry Editor Actions](/docs/headless-cms/extending/customize-entry-editor-actions)

<Gallery>
  <Image title={"Entry List default Filter"} src={hcmsFilters} />
  <Image title={"Entry Editor default Actions"} src={hcmsActions} />
</Gallery>

### Introducing Lexical Editor For the Rich Text Field ([#3368](https://github.com/webiny/webiny-js/pull/3368))

With this release, we are introducing the new Lexical Editor for the Headless CMS's rich text field. We are bringing the same rich text editing experience that you are used to from the Page Builder application, to the Headless CMS.

<Image src={hcmslexicalEditor} title={"Lexical editor in action"} />

We believe the new editor will significantly improve the content editing experience for content editors. It is also a great step towards our goal of unifying the content editing experience across all of Webiny's applications.

<Alert type={"info"}>

Released with [Webiny 5.35.0](/docs/release-notes/5.35.0/changelog#introducing-a-brand-new-text-editor-3147), the Lexical rich text editor was initially introduced into Webiny's Page Builder application.

</Alert>

Note that with the introduction to the new Lexical editor, the old rich text editor (based on [Editor.js](https://editorjs.io/)) will become deprecated. This means the following:

- users will no longer be able to use the legacy editor with new content models and Rich Text fields
- we will no longer be maintaining or developing features for this editor
- the legacy editor will only appear when the content was initially created with it

<Image src={hcmsLegacyRteLook} title={"The Legacy Rich Text Editor"} />

## Page Builder

### Carousel Page Element ([#3276](https://github.com/webiny/webiny-js/pull/3276))

With this release, we've introduced the new **Carousel** page element, which allows users to create a carousel of images, videos, or any other content you want to display in a carousel.

To add a carousel to your page, simply drag and drop the Carousel element from the left-side menu to the desired location on your page.

<Image src={pageElementCarousel} title={"Carousel Page Element"} />

### Tabs Page Element ([#3184](https://github.com/webiny/webiny-js/pull/3184))

With this release, we've introduced the new **Tabs** page element, which allows users to create a tabbed content section on your page.

To add tabs to your page, simply drag and drop the Tabs element from the left-side menu to the desired location on your page.

<Image src={pageElementTab} title={"Tabs Page Element"} />

### Accordion Page Element ([#3175](https://github.com/webiny/webiny-js/pull/3175))

With this release, we've introduced the new **Accordion** page element. As the name suggests, it allows users to create an accordion section on your page.

To add an accordion to your page, simply drag and drop the Accordion element from the left-side menu to the desired location on your page.

<Image src={pageElementAccordion} title={"Accordion Page Element"} />

### Cell Mirroring ([#3145](https://github.com/webiny/webiny-js/pull/3145))

With this release, we've introduced the new **Cell Mirroring** feature, which allows users to mirror the content of a cell to another cell. This is useful when you want to quickly duplicate the content of a cell to another cell, without having to copy and paste the content manually.

<Image src={pbCellMirroring} title={"Cell Mirroring"} />

### Multiple Minor Fixes and Improvements

The following is a list of minor fixes and improvements that were made to the Page Builder app in this release.

#### Addressed Cursor Positioning Issue (Button Label Input) ([#3146](https://github.com/webiny/webiny-js/pull/3146))

Prior to this release, in some cases, when typing a label for a button, the cursor would jump to the end of the input field after each keystroke, preventing users from editing the label efficiently. This issue has now been addressed.

<Image
  src={pbFixesButtonCursor}
  title={"Addressed Cursor Positioning Issue (Button Label Input)"}
/>

#### Ability to Provide a LinkedIn Profile URL For Your Website ([#3362](https://github.com/webiny/webiny-js/pull/3362))

With the already existing Facebook, Instagram, and Twitter URL fields in the Website Settings section, users can now also provide a LinkedIn profile URL for their website.

<Image src={pbFixesLinkedIn} title={"Ability to Provide a LinkedIn Profile URL"} shadow={false} />

#### Responsive Styles For Typography ([#3405](https://github.com/webiny/webiny-js/pull/3405))

Via the [theme object](/docs/{exact:5.37.x}/page-builder/theming/theme-object), it's now possible to define responsive styles for typography. The following example shows how we can have different font size for the heading on different screen sizes.

```diff-ts apps/theme/theme.ts
// ...

export const breakpoints = {
  desktop: "@media (max-width: 4000px)",
  tablet: "@media (max-width: 991px)",
  "mobile-landscape": "@media (max-width: 767px)",
  "mobile-portrait": "@media (max-width: 478px)"
};

// ...

// Typography.
const headings = {
  fontFamily: fonts.font2,
  color: colors.color3,
  WebkitFontSmoothing: "antialiased"
};

export const typography = {
  // Six heading variants (levels of headings).
  headings: [
    {
      id: "heading1",
      name: "Heading 1",
      tag: "h1",
      styles: {
+       // Note the use of breakpoint keys (defined at the top of
+       // this file, via the `breakpoints` constant).
+       desktop: { ...headings, fontWeight: "bold", fontSize: 48 },
+       tablet: { fontSize: 36 },
+       "mobile-landscape": { fontSize: 24 },
+       "mobile-portrait": { fontSize: 20 }
      }
    },
    // ...
  ]
  // ...
};

// ...
```

#### Fixed Unlinking Pages From Page Templates ([#3403](https://github.com/webiny/webiny-js/pull/3403))

Unlinking a page from a page template is now working as expected. Previously, unlinking a page would result in an error, preventing users from unlinking pages from page templates.

<Image title={"Fixed Unlinking Pages From Page Templates"} src={pbFixesUnlinkingTpl} />

#### Improved Messaging In the Page Builder Editor ([#3403](https://github.com/webiny/webiny-js/pull/3403))

We've introduced a couple of improvements to the messaging in the page editor.

First, the element name that is displayed when the element is hovered or activated (in the top right corner), is now shown as a human-readable name. For example, instead of **paragraph**, the name will now be **Paragraph**. Also, instead of **pages-list**, the name will now be **List of Pages**. This also applies to custom elements.

Second, if an element is dropped onto an existing element that cannot accept the dropped element, a message will now be displayed, informing the user that the drop action is not allowed. The message will include both elements that were involved in the drop action.

<Gallery>
  <Image title={"Human-readable Element Types"} src={pbFixesElementTypesHumanReadable} />
  <Image title={"Drop Action Not Allowed"} src={pbFixesDndImprovedMessages} />
</Gallery>

#### Page Builder - Fixed Two Drag and Drop-related Issues ([#3402](https://github.com/webiny/webiny-js/pull/3402))

We've fixed two issues related to dragging and dropping of elements in the page editor.

##### Cannot Interact With New Page Elements

Once a user created a new page and started dragging and dropping elements to it, they would be unable to interact with the dropped elements. Only after a full page refresh would the elements become interactive. This issue has now been addressed.

##### Page Elements Disappearing After Dragging and Dropping

In some cases, moving an existing page element to a new location on the page would cause the element to disappear. This issue has now been addressed.

#### Added Line Height To Default Heading Styles ([#3363](https://github.com/webiny/webiny-js/pull/3363))

In the [`apps/theme/theme.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/common/apps/theme/theme.ts#L36) file, the default heading styles now also include a line height property, set to 150% of the font size. This change was made to improve the readability of the text.

Note that this change will only affect new projects created with this release. If you want to apply this change to an existing project, you'll need to manually update the [`apps/theme/theme.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/common/apps/theme/theme.ts#L36) file.

#### Block Editor - Addressed Back Button's Navigation Issue ([#3361](https://github.com/webiny/webiny-js/pull/3361))

Prior to this release, the back button in the block editor (located in the top-left corner of the screen) would sometimes navigate users to the wrong page. This issue has now been addressed.

#### Fixed Icon In the Revisions Dropdown Menu ([#3361](https://github.com/webiny/webiny-js/pull/3361))

Prior to this release, the icon in the revisions dropdown menu was not aligned properly. This issue has now been addressed.

#### Removed Width Control For Blocks ([#3364](https://github.com/webiny/webiny-js/pull/3364))

In the page editor, when dropping a block onto the page canvas, the block's width is now fixed to 100% of the parent container. This change was made to simplify the process of adding blocks to the page and ensure that the page layout is consistent across all devices. Manually setting the width of the block is no longer possible.

#### List of Revisions - Revisions Now Shown From Latest to Oldest ([#3379](https://github.com/webiny/webiny-js/pull/3379))

Prior to this release, the list of revisions in the revisions tab would show the revisions from oldest to newest. This has now been changed, and the revisions are now shown from newest to oldest.

<Image
  src={pbFixesRevisionsList}
  title={"List of Revisions - Revisions Now Shown From Latest to Oldest"}
/>

#### Fixed Setting Background Image on Page Elements ([#3378](https://github.com/webiny/webiny-js/pull/3378))

In the page editor, setting a background image on a page element would not work properly in some cases. This issue has now been addressed.

#### Fixed URL in Page Preview Drawer ([#3355](https://github.com/webiny/webiny-js/pull/3355))

We addressed a visual bug encountered within the page preview drawer, specifically when the website has not been deployed yet: in this specific case the full page URL contained `undefined` due to the absence of a domain. This issue has now been addressed, by showing the page's relative path instead of the full URL.

#### Remove Characters Length Validation for Folder Fields ([#3399](https://github.com/webiny/webiny-js/pull/3399))

With this release, we removed any length validations rule applied to `title` and `slug` folder fields. These fields remain `required`.

### Deprecating Legacy Page Rendering Engine

Earlier this year, with the [5.35.0 release](/docs/release-notes/5.34.0/changelog), we've introduced [a new page rendering engine](/docs/release-notes/5.34.0/changelog#page-elements-a-brand-new-page-rendering-engine-2898) called Page Elements.

At that point, we've decided to keep the old page rendering engine in place, in order to give users enough time to migrate their existing project to the new engine, while also notifying them about the upcoming deprecation of the old engine.

This release marks the end of the deprecation period, and the old page rendering engine has been removed from the codebase. This means that, from now on, all versions of Webiny will only support the new page rendering engine.

If you're still using the old page rendering engine, we strongly recommend you to migrate your existing pages to the new engine as soon as possible. For more information on how to do this, please refer to the [Page Builder - Page Rendering Engine Migration](/docs/release-notes/5.34.0/page-builder-pe-rendering-engine-migration) guide.

## Security

### Introducing Advanced Access Control Layer (AACL) ([#2576](https://github.com/webiny/webiny-js/pull/2576))

The Advanced Access Control Layer (AACL) is the next step in the evolution of Webiny's existing security layer, which is now divided into three tiers. The higher the tier, the more features are available.

All Webiny project start with the **Open Source** tier. The tier is free to use, but, from now on, will be limited when it comes to defining fine-grained permissions, allowing only the **No Access** and **Full Access** to be selected when defining permissions for individual Webiny apps.

Trying to select **Custom Access** will result in an alert message being shown, informing the user that the feature is only available with the Advanced Access Control Layer (AACL), which is available on the **Business** and **Enterprise** tiers.

<Image src={aaclCustomAccessUpgrade} title={"Selecting Custom Access Level on Open Source Tier"} />

To upgrade to **Business** tier, users [link their project](/docs/wcp/link-a-project) with [Webiny Control Panel (WCP)](/docs/wcp/overview), from where they can activate the Advanced Access Control Layer (AACL) for their project. By doing this, users will be able to define fine-grained permissions for individual Webiny apps.

<Image
  src={aaclCustomAccess}
  title={"Selecting Custom Access with Advanced Access Control Layer (AACL) Enabled"}
/>

Finally, for the most advanced use cases, users can upgrade to the **Enterprise** tier. On top of the features available with the first two tiers, the Enterprise tier introduces the brand new **Teams** feature, enabling users to group users into teams and easily have them linked with one or more roles.

<Image src={aaclTeams} title={"The Brand New Teams Feature"} />

This feature is especially useful for larger organizations, where it's common to have multiple teams working on different projects. Also, it's a great way to simplify the process of managing permissions for multiple users, as you can simply assign a role to a team, instead of assigning it to each individual user.

For more information on the Teams, please refer to the newly added [Teams](/docs/enterprise/aacl/teams) section of the Webiny Enterprise documentation.

#### A Note On Backwards Compatibility

The Advanced Access Control Layer (AACL) and the three tiers outlined above are a new concept introduced with this release. As such, it's important to note existing projects will not be affected by this change in any way.

More specifically, Webiny instances that were deployed prior to this release will still be able to rely on the mentioned **Custom Access** option upon defining permissions for individual Webiny apps, without having to upgrade to Business tier.

On the other side, unless the Business or Enterprise tier has been enabled, all newly deployed Webiny instances will automatically start with the **Open Source** tier. This means the **Custom Access** option will not be available upon defining permissions for individual Webiny apps. To enable this feature, users will have to upgrade to the Business or Enterprise tier.

### Renaming Security Groups to Security Roles

With this release, we've renamed **Groups** to **Roles**. Essentially, this change was made in order for Webiny to be more aligned with industry standards.

Note that this change is purely cosmetic and does not affect the functionality of the feature in any way. On levels such as the application code or Webiny's GraphQL API schema, the feature is still referred to as **Groups** and no breaking changes were introduced.

<Image src={aaclRolesRename} title={"Renaming Security Groups to Security Roles"} />

## Form Builder

### New Date/Time Field ([#3209](https://github.com/webiny/webiny-js/pull/3209))

With this release, we've introduced a brand new Date/Time form field, allowing users to select a date and time from a calendar and time picker. The field supports four different formats:

1. Date only
2. Time only
3. Date and time with time zone
4. Date and time without time zone

<Image src={fbDateTimeField} title={"New Date/Time Field"} />

### Google Analytics Trigger ([#3125](https://github.com/webiny/webiny-js/pull/3125))

With the newly introduced Google Analytics trigger, users can easily send events to Google Analytics when a form is submitted.

<Image src={fbGaTrigger} title={"Google Analytics Trigger"} />

As visible in the screenshot above, the trigger can be configured via the Triggers tab, allowing users to attach as many parameters as they as needed.

Note that this trigger requires the user to manually set up Google Analytics on their website. For more information on how to do this, please refer to the [Google Analytics - Getting Started](https://support.google.com/analytics/answer/10269537) guide.

### Form Submissions Now Also Include Query Parameters ([#3124](https://github.com/webiny/webiny-js/pull/3124))

Previously, when a form was submitted, the query parameters were not included in the submission data. With this release, we've changed this behavior.

The query parameters that were present upon submitting the form will now also be included in the form submission data. This means that, for example, users can now easily track the source of the form submission, by simply adding the `utm_source` query parameter when linking to a page that contains the form.

Note that this information is also available when exporting form submissions, allowing users to further analyze their form submission data. The following example shows the resulting JSON export of a form submission (note the `meta.url` object):

```diff-json
{
  "firstName": "Tom",
  "lastName": "D.",
  "email": "<EMAIL>",
  "meta": {
    "submittedOn": "2023-07-04T20:03:59.028Z",
+   "url": {
+     "location": "http://mysite.com/some-page?utm_source=something&another-query-param=some-value",
+     "query": {
+       "another-query-param": "some-value",
+       "utm_source": "something"
+     }
    }
  }
}
```

## Development

### Add "onTenantAfterInstall" Lifecycle Event ([#3377](https://github.com/webiny/webiny-js/pull/3377))

We've introduced a new lifecycle event which you should use when you need to hook into the tenant installation process. This event is published when all applications have finished installing, so you don't have to think about the order of application installation.

More and more of our business and enterprise users are utilizing the programmatic capabilities of Webiny. They're creating data propagation mechanisms, default data setup scripts, etc., and they need a reliable way to know when a tenant is ready to be interacted with. This lifecycle event gives you a reliable entry point for this type of things.

An example of such a plugin would look like this:

```ts
new ContextPlugin(context => {
  context.tenancy.onTenantAfterInstall.subscribe(() => {
    // Here you can reliably access Webiny apps, create data, etc.
  });
});
```
