---
id: c46ec70b
title: Webiny 5.39.5 Changelog
description: See what's new in Webiny version 5.39.5.
---

import { GithubRelease } from "@/components/GithubRelease";

<GithubRelease version={"5.39.5"} />

## Page Builder

### Addressed Public Website Rendering Issue ([#4094](https://github.com/webiny/webiny-js/pull/4094))

Over the last couple of days, we've received multiple reports of Page Builder's page rendering functionality not working. Basically, the user would publish a page, but the page would never be visible on the public website side. 

Ultimately, every report was accompanied by the following error (thrown during the page rendering process): 

```text
Protocol error: Connection closed. Most likely the page has been closed.
```

After some testing on our side, we've discovered that updating versions of relevant NPM packages resolves the issue. So, with this release, we've updated the versions of the following NPM packages:

1. [`puppeteer-core`](https://github.com/puppeteer/puppeteer/tree/main#readme)
2. [`@sparticuz/chromium`](https://github.com/Sparticuz/chromium) 

Note that, when deployed, <PERSON>iny consumes the `@sparticuz/chromium` package via an [AWS Lambda layer](https://github.com/Sparticuz/chromium?tab=readme-ov-file#aws-lambda-layer), which means [we've had to update](https://github.com/webiny/webiny-js/commit/5d42fef1eae20789e4bd132a5f9e89cbc301afeb#diff-ae6ed698fef0d85294a2b06d25ac0865c0ab6056404eb2ebee23698cdc7ac2aaR21-R36) its version as well. Note that the version change will be seen in the deployment logs, upon deploying the **Website** project application.

All in all, with all of the versions upgraded, the Page Builder's page rendering functionality should again be working as expected.
