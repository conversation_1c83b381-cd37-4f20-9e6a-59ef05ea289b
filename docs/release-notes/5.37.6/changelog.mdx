---
id: changelog
title: Webiny 5.37.6 Changelog
description: See what's new in Webiny version 5.37.6.
---

import cognitoFederation from "./assets/cognito-federation.png";

<GithubRelease version={"5.37.6"} />

## Cognito

### Add Support for Federated Identity Providers ([#3605](https://github.com/webiny/webiny-js/pull/3605))

With this release, we've enabled developers to configure federated identity providers on the Cognito setup used for Admin app login. We now support logging in via the following public identity providers: Google, Facebook, Amazon, and Apple. Custom OIDC providers are not yet supported.

<Image src={cognitoFederation} title={"Using Federated IdPs to Sign Into the Admin App"} />

<Alert type="warning" title={"Update Your AWS Permissions"}>

If you plan to use this feature, and you used our Cloudformation template to setup your deployment
user, make sure you update the IAM permissions. [The CF
template](https://webiny-public.s3.us-east-2.amazonaws.com/cloudformation/DEPLOY_WEBINY_PROJECT_CF_TEMPLATE.yaml)
was updated with the permissions to manage user pool domains and identity providers.

</Alert>

## Lexical Editor

### Remount LexicalComposer when Nodes in the Configuration Change ([#3603](https://github.com/webiny/webiny-js/pull/3603))

`LexicalComposer` React component has one peculiar behaviour, it caches the config object passed to it on first mount, and never updates it. This turned out to be a problem for third party plugins that register new Lexical node classes. With this release, we've addressed this problem, and we now remount the React component when node classes change in the config.

## Page Builder

### Add Safeguards for Invalid DB Records ([#3602](https://github.com/webiny/webiny-js/pull/3602))

This issue is pretty unique, and so far has only happened to one of our users, and we were unable to reproduce it, no matter how hard we tried. But, the symptoms of the issue are that you can't publish a draft page, because the database records somehow end up in an invalid state.

To make this issue go away, we've added some extra safeguards to our storage layer, and even if the database records _somehow_ become invalid, the publishing and unpublishing of pages will know how to correctly handle the situation.

## Elasticsearch

### Fix `startsWith` and `notStartsWith` Filtering ([#3591](https://github.com/webiny/webiny-js/pull/3591))

For DDB+Elasticsearch projects, we've addressed an issue of Elasticsearch not being able to perform filtering on `startsWith` and `notStartsWith` operators paired with `""` (empty string) as an input value.
