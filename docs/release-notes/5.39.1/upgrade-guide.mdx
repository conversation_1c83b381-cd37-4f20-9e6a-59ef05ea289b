---
id: 809a794e
title: Upgrade from 5.38.x to 5.39.1
description: Learn how to upgrade Webiny from 5.38.x to 5.39.1.
---

import { Alert } from "@/components/Alert";
import { AdditionalNotes } from "@/components/upgrade/AdditionalNotes";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.38.x to 5.39.1

</Alert>

<Alert type="info">

Make sure to check out the [5.39.1 changelog](./changelog) to get familiar with the changes introduced in this
release.

</Alert>

## Step-by-Step Guide

### 1. Upgrade Webiny Packages

Upgrade all Webiny NPM packages by running the following command:

```bash
yarn up "@webiny/*@5.39.1"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return **5.39.1**.

### 2. Run the Upgrade Command (If Upgrading from 5.38.x)

In case you are upgrading to **5.39.1** from **5.38.x**, you must upgrade your project. Depending on your project, you might also need to perform a couple of manual steps.

Before proceeding with the next step, make sure to check the [2. Run the Upgrade Command](/docs/release-notes/5.39.0/upgrade-guide#2-run-the-upgrade-command) section of the [Upgrade From 5.38.x to 5.39.0](/docs/release-notes/5.39.0/upgrade-guide) upgrade guide.

### 3. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

<AdditionalNotes />
