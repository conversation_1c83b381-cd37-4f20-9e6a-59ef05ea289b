---
id: aafea9e6
title: Webiny 5.7.0 Changelog
description: See what's new in Webiny version 5.7.0.
---

import { <PERSON><PERSON> } from "@/components/Alert";

# Changes

This document highlights the most important fixes, improvements, and features, that were introduced in Webiny `5.7.0`.

<Alert type="info" title="How To Upgrade?">

Please check the [Webiny 5.7.0 migration guide](/docs/release-notes/5.7.0/upgrade-guide) for the upgrade steps.

</Alert>

## Webiny CLI

### Brand new CI/CD Scaffold ([#1633](https://github.com/webiny/webiny-js/pull/1633))

With this release, we're introducing a brand new CI/CD scaffold, which will set up a CI/CD workflow for your Webiny project in no time!

![New CI/CD Scaffold](./ci-cd-scaffold.gif)

Currently, you can set up your CI/CD via [GitHub Actions](https://github.com/features/actions) only. But note that in the future, we would certainly like to bring support for more providers, depending on the interest from the community.

For more information on how to use the new scaffold, make sure to check out the also new [Set up CI/CD](/docs/core-development-concepts/ci-cd/setup) how-to guide.

<Alert type="success">

If you plan to use a different CI/CD provider, you can still create your workflows manually, by following all of the recommendations outlined in the new [CI/CD](/docs/core-development-concepts/ci-cd/introduction) key topics section. Also, if you think your provider should be supported out of the box, please [open an issue](https://github.com/webiny/webiny-js/issues/new) or [contact us](https://www.webiny.com/slack).

</Alert>

### Improved `--debug` Argument (`webiny deploy` and `webiny destroy` Commands) ([#1624](https://github.com/webiny/webiny-js/pull/1624), [@ankurvr](https://github.com/ankurvr))

From this release, passing the `--debug` flag upon running the [`webiny deploy`](/docs/core-development-concepts/basics/project-deployment) and [`webiny destroy`](/docs/infrastructure/basics/destroy-cloud-infrastructure) commands will also include additional deployment-related information and logs.

The following example shows how you can use it with the `webiny deploy` command:

```bash
yarn webiny deploy api --env dev --debug
```

This improvement will be very useful because without it, upon experiencing deployment-related errors, users simply wouldn't receive enough information, which would make debugging much harder and in certain cases, even almost impossible.

### Improved API Error Output ([#1622](https://github.com/webiny/webiny-js/pull/1622))

From now on, when `DEBUG=true` is set in your `<projectRoot>/.env` file, React apps will be able to render a proper error overlay with details about GraphQL operation and actual error that was thrown.

![GraphQL Error Overlay](./error-overlay.png)

## Other

### ApolloDynamicLink

With addition of the error overlay Apollo link, to handle API errors, we noticed that the amount of custom Apollo links is starting to grow, and it's becoming a bit crowded in the app setup code.

We introduced a new ApolloLink - [ApolloDynamicLink](https://github.com/webiny/webiny-js/blob/next/packages/app/src/plugins/ApolloDynamicLink.ts), to be the only custom link Webiny needs in the Apollo Client setup. This new link will look for plugins of type `apollo-link` on every request and allow you to modify the operation (headers, query, etc.). This also solves the problem of `apollo-link` plugins being lazy loaded, in which case they wouldn't even be part of the ApolloClient request processing.

<Alert type="info">

This change, requires some little tweaks to your existing projects. Find all the necessary instructions in the [Webiny 5.7.0 migration guide](/docs/release-notes/5.7.0/upgrade-guide).

</Alert>

### Extend Page Data and Queries/Mutations

You can now also extend Page Builder Page and its settings with custom fields, in both the GraphQL API and `admin` app. How-to guides will be published in the upcoming days, where we'll guide you through the plugins you need to use to make this work.

### Plugins as Class Instances

The previous two upgrades led us through an interesting discovery process. We realized that we can, in some cases, greatly improve the DX by providing not just the TS interface for plugin implementation, but actual plugin classes with default logic.

Starting with this `5.7.0` release, we'll be experimenting more and more in this field, and will be rolling out more plugins which you'll be able to `just use`. We're very excited about this so stay tuned for more information on our journey to better and faster development.

## Documentation

### New CI/CD Key Topics

We've just published the brand new [CI/CD](/docs/core-development-concepts/ci-cd/introduction) key topics section, which, as you might've already guessed, covers everything that there is to know about setting up a CI/CD workflow for your Webiny projects.

### Security Framework Key Topics

We've finally put together several articles explaining what [Webiny Security Framework](/docs/core-development-concepts/security-framework/introduction) is and how everything is tied together on both API and React side.

### Multi-Tenancy Key Topic

Learn about [multi-tenancy](/docs/overview/features/multi-tenancy) in Webiny. Note that this feature requires an Enterprise License. If you're a company interested in building multi-tenancy applications, [get in touch](https://calendly.com/webiny/30min).
