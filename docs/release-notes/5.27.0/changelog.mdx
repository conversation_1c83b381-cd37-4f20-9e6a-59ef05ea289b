---
id: changelog
title: Webiny 5.27.0 Changelog
description: See what's new in Webiny version 5.27.0.
---

import richTextEditorMenu from "./richTextEditorMenu.png";

# Changes

## Headless CMS

### Removed Unnecessary Variables Passed Into Storage Operations ([#2403](https://github.com/webiny/webiny-js/pull/2403))

We removed `latestEntry` and `latestStorageEntry` from being passed into the storage operations because they are not required for storage operations to function. These objects were used for debugging purposes only. While using storage operations programmatically, we realized that it's confusing, and causes unnecessary friction while developing plugins, so these are now gone.

### Improve Rich Text Editor Menu Positioning ([#2419](https://github.com/webiny/webiny-js/pull/2419))

We gave some love to our `RichTextEditor` component, and polished out the menu positioning and visibility. The underlying [editorjs](https://editorjs.io/) is now also upgraded to the latest version, so you'll notice a nicer layout of tools in the menu.

<Image src={richTextEditorMenu} title="Rich Text Editor Tools" />

## Development

### Require Node >=14 to Create a Webiny Project ([#2420](https://github.com/webiny/webiny-js/pull/2420))

Until now, we were very strict about what versions of Node you can use to create a new Webiny project. Starting with this release, we made the requirement less strict, and we now allow you to create new projects with any version of Node that is &gt;=14. This means, 14, 15, 16, etc. Node 12 is no longer supported, as we're using some newer Node features, and to be able to enjoy the new stuff, we need to stop supporting older versions of Node.

To understand our support of Node versions better, take a look at this [official diagram](https://nodejs.org/en/about/releases/):

<Image
  src="https://raw.githubusercontent.com/nodejs/Release/master/schedule.svg?sanitize=true"
  title="Node Releases"
/>

We'll try to follow Node's lifecycles, as that's what the rest of the ecosystem is doing, and we'll be primarily focusing on supporting LTS releases. If unstable (odd release numbers like 15, 17, etc.) releases work for you, that's great! Do let us know that they work for you on our [Community Slack](https://www.webiny.com/slack), but the core team will be mainly focusing on supporting LTS versions.
