---
id: 83f56832
title: Webiny 5.40.3 Changelog
description: See what's new in Webiny version 5.40.3.
---

<GithubRelease version={"5.40.3"} />

## Fixes and Improvements

### Fix Several Issues in the Lexical Editor

We've fixed several issues related to the Lexical editor.

One is a bug introduced by upgrading Lexical to v0.16 in one of the recent releases, where a deprecated `LexicalErrorBoundary` default import caused issues with value updates at runtime.

Another one was related to using a different digital asset manager (replacing our File Manager), which prevented users from inserting images into the Lexical editor. This is now fixed, and you can use any DAM to insert images.

And lastly, when inserting images into the Lexical editor, the File Manager will now only show assets that match the `image/*` mime type, meaning, there won't be any other files shown to the user that are not images.

### Fix Form Values When Binding to a Non-Scalar Value

When a form value is an object, sometimes the changes to the form state would not update the UI. This is now fixed, and changes on non-scalar values are correctly propagated to the UI. In particular, this affects values like Lexical editor state in the Headless CMS, where we store it as a raw object.

### Use the Passed Entry Object in the Content Entry Form

When doing customizations to the `ContentEntryForm` in the Headless CMS, our implementation was a bit too restrictive, and would discard any values passed through props, unless they contained an `id` property. This is now fixed, and you can decorate the `ContentEntryForm` component, and pass whatever `entry` object you wish, which will be treated as the entry the form should work with.

### Fix WebsiteSettingsConfig to Support Multiple Query Selections

We've fixed a bug in `WebsiteSettingsConfig` which ignored GraphQL query selections when adding multiple settings groups. Thhis is now fixed, and each group's GraphQL selection will be included in the final GraphQL query.

### Improve Handling of Title, Description, and Image Field in Content Models

We've improved the behavior of title, description, and image fields in the Headless CMS content models, and made the system a bit smarter by always looking for an alternative, if the given field is not applicable or doesn't exist. This means that there will no longer be errors thrown at the user, if they accidentally delete a field which is currently being used as "title", "description", or "image".

For "title" field, the system will look for an applicable field of type `text`, ultimately falling back to using the entry `id`, if no `text` fields exist in the model.

### Fix Component Name Generation when Linking Extensions

When linking extensions, we generate component names using the extension folder name. Your preferences for folder names may vary; camelCase, kebab-case... With this release we make sure that no matter the naming convention, we always generate a valid component name to be used in React.

### Page Builder Tabs Element Active and Default State

In the Page Builder editor, the Tabs element now has a dedicated class assigned to active Tab element label, which improves the UX by providing better visual feedback.
