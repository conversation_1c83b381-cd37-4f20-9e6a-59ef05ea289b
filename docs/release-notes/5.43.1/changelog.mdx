---
id: 0ea53f89
title: Webiny 5.43.1 Changelog
description: See what's new in Webiny version 5.43.1
---

<GithubRelease version={"5.43.1"} />

## Page Builder

### Removed CloudFront Cache Invalidation ([#4652](https://github.com/webiny/webiny-js/pull/4652))

For a very long time, Webiny's Page Builder has been using [CloudFront cache invalidation](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/Invalidation.html) to ensure that the latest changes made to the page are reflected on the public website. 

However, this approach has proven to be not required. This is simply because, by default, when served to actual users, all published pages are cached on the CloudFront CDN for only 30 seconds. This means that any changes made to the page will be reflected in the frontend within 30 seconds, without the need for cache invalidation.

With this approach, we can also avoid the costs associated with CloudFront cache invalidation requests, which can add up quickly if you have a lot of pages.

## Development

### `webiny watch` - Fixed Backend Development Issue ([#4668](https://github.com/webiny/webiny-js/pull/4658)]

With the [5.43.0 release](/docs/release-notes/5.43.0/changelog), we introduced a regression within the `webiny watch` command, when used for backend development. 

Basically, despite the fact that the backend code would get locally recompiled, the changes would not be reflected when trying to rerun the code. On top of that, recompilation would also take longer than usual.

This has now been fixed, and the `webiny watch` command should work as expected again.

<Alert type="info">

  With the [5.41.0 release](/docs/release-notes/5.41.0/changelog), we've introduced local AWS Lambda development — a major update to the webiny watch command and the overall backend development experience. 
  
  This feature is currently in beta, but if you're interested in trying it out or learning more, check out the [related changelog entry](/docs/release-notes/5.41.0/changelog#introducing-the-new-watch-command-local-aws-lambda-development-4185).

  </Alert>
