---
id: ca039c85
title: Webiny 5.39.11 Changelog
description: See what's new in Webiny version 5.39.11.
---

import { GithubRelease } from "@/components/GithubRelease";

<GithubRelease version={"5.39.11"} />

## Headless CMS

### Custom Model Field Renderers in File Manager ([#4231](https://github.com/webiny/webiny-js/pull/4231))

With the [5.39.0 release](/docs/release-notes/5.39.0/changelog), a regression was introduced where a user could not use a custom CMS model field renderers both in CMS (content model entry form) and File Manager (file details drawer). The only way to have the renderer work in both places was to have two almost identical renderers, with a slight difference in the `bind` prop that was passed to the renderer. 

With this release, we've fixed this issue. Now, users can again use the same custom renderer in both CMS and File Manager.

<Alert type="info">

  For more technical information on this change, please refer to the [original pull request](https://github.com/webiny/webiny-js/pull/4231).

</Alert>

## Data Migrations

### 5.39.6-001 Data Migration - Improved Handling of Published CMS Entries ([#4234](https://github.com/webiny/webiny-js/pull/4234))

With this release, we had to make one additional adjustment to the `5.39.6-001` data migration. 

In short, the migration was improved to handle a case where the "latest" DynamoDB record, that belonged to a published CMS entry, would not have its `status` and `locked` fields set to `published` and `true`, respectively. Although this should not happen, we did encounter this in one of our user's project. 

While addressing this, we've actually also addressed an issue where the migration would not properly handle CMS entries where the latest revision was not published.  

Ultimately, the migration has been improved to handle all possible scenarios, further ensuring that all CMS entries are properly migrated.

<Alert type={"info"}>

  For more technical information on the improved `5.39.6-001` data migration, please refer to the original pull request ([#4154](https://github.com/webiny/webiny-js/pull/4154)).

</Alert>
