---
id: removing-the-headless-cms-lambda-function
title: Removing the Headless CMS AWS Lambda function
description: Steps to remove the Headless CMS AWS Lambda function from your project.
---

import { Alert } from "@/components/Alert";

<Alert type={"warning"}>

Before starting with the migration, make sure you're using Webiny version 5.35.0 or greater.

</Alert>

## Introduction

Prior to 5.35.0 version of Webiny, remote GraphQL API HTTP requests were handled by two separate AWS Lambda functions. One function was responsible for serving the Default GraphQL API (accessible via the `/graphql` endpoint), while the other was responsible for serving the [Headless CMS GraphQL API](/docs/headless-cms/basics/graphql-api) (accessible via the `/cms/{type}/{locale}` endpoint).

Starting with 5.35.0, all remote GraphQL API HTTP requests are handled by a single AWS Lambda function. In other words, the function is responsible for serving both the Default and Headless CMS GraphQL APIs. Note that the mentioned `/graphql` and `/cms/{type}/{locale}` endpoints remain unchanged.

## Prerequisites

Before upgrading to 5.35.0 and continuing with this migration guide, please ensure your Webiny project is using the [new project setup](/docs/release-notes/5.29.0/changelog#brand-new-project-setup), introduced with Webiny [5.29.0](/docs/release-notes/5.29.0/changelog). In case you did not already migrate, please visit the [migration guide](/docs/release-notes/5.29.0/project-setup-migration).

## What Is the Reason for This Change?

The main reason behind this change is the simplification of project development and deployment. For example, prior to `5.35.0`, users would have two separate folders in their `apps/api` folder:

1. `apps/api/graphql` - Default GraphQL API
2. `apps/api/headlessCMS` - Headless CMS GraphQL API

This means that, upon extending the GraphQL APIs via various plugins, in some cases users would need to understand within which folder the plugins would need to be registered.

With both GraphQL APIs now consolidated under a single AWS Lambda function, the two folders were reduced to a single `apps/api/graphql` folder. Meaning, all GraphQL API-related plugins are now registered within the same folder, making it easier for developers to introduce customizations.

## Why Is There No Automated Migration?

Essentially, because users might already have a number of different GraphQL API-related customizations in place, at this time, we cannot provide an automatic migration.

To migrate, users need to perform the migration manually, which consists of a couple of relatively simple steps. More on this in the following section.

## Do I Need to Do Anything?

If you have custom application code or plugins within the `apps/api/headlessCMS` folder, then the answer is yes, there are migration steps to take.

Also, if you have changed the `apps/api/webiny.application.ts` file, and you are referencing the `ApiHeadlessCMS` class, you will need to change it to the `ApiGraphql` class.

If none of the above has been performed within your Webiny project, then you can skip this article.

## How to Migrate?

The migration consists of a couple of relatively simple steps:

1. Move any custom application code and imported plugins from the `apps/api/headlessCMS/src/index.ts` to `apps/api/graphql/src/index.ts`.
2. Move any custom code application and imported plugins from the `apps/api/headlessCMS/src/security.ts` to `apps/api/graphql/src/security.ts`.
3. If you added any GraphQL Schema plugins, which are concerning the Headless CMS API customizations, use the `CmsGraphQLSchemaPlugin` (`import { CmsGraphQLSchemaPlugin } from "@webiny/api-headless-cms";`) class instead of the `GraphQLSchemaPlugin` class or interface, depending on which one you used to define the custom GraphQL Schema.
4. If you are using the import `import { ApiHeadlessCMS } from "@webiny/pulumi-aws"` in the `apps/api/webiny.application.ts`, point it to the `import { ApiGraphql } from "@webiny/pulumi-aws"`, and change the code accordingly,

## Cleanup

After you have migrated all your plugins and other related application code, deployed and tested the project, feel free to delete the `apps/api/headlessCMS` folder.

## Conclusion

Despite the fact that the removal of Headless CMS AWS Lambda Function could be considered as a breaking change, we still decided to keep the major version of Webiny unchanged. Once migrated, users can still continue to use their Webiny project, as if there were no changes introduced whatsoever.

<Alert type={"info"}>

For additional questions or any other type of feedback, feel free to reach out to us on our [Community Slack](https://webiny-community.slack.com/).

</Alert>
