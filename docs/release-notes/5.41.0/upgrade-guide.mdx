---
id: bad5c115
title: Upgrade from 5.40.x to 5.41.0
description: Learn how to upgrade Webiny from 5.40.x to 5.41.0.
---

import { Alert } from "@/components/Alert";
import { AdditionalNotes } from "@/components/upgrade/AdditionalNotes";

<Alert type="success" title="What you’ll learn">

  - how to upgrade Webiny from 5.40.x to 5.41.x

</Alert>

<Alert type="info">

  Make sure to check out the [5.41.0 changelog](./changelog) to get familiar with the changes introduced in this
  release.

</Alert>

## Step-by-Step Guide

### 1. Upgrade Webiny Packages

Upgrade all Webiny NPM packages by running the following command:

```bash
yarn up "@webiny/*@5.41.4"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return **5.41.4**.

### 2. Run the Upgrade Command

The next step is to run the project upgrade:

```bash
yarn webiny upgrade
```

### 3. Breaking Changes❗

This release contains a couple of breaking changes that might affect your project.

#### 3.1 Updated Node.js to the Version 20

We have updated the `Node.js` version, which runs the code which gets deployed, to `v20`.

Users should update their development machines and CI deployments to use `Node.js` `v20`.

`Node.js` `v22` is not supported as AWS Lambda runtime, so we did not go that far yet. As soon as AWS Lambda supports `Node.js` `v22`, we will update the runtime to that version, and users will get notified about it in the upgrade docs.

We have also updated the `engines` field in the `package.json` file to reflect this change.

```diff-json
+  "engines": {
+    "node": "~20.0.0"
+  }
```

#### 3.2 Updated Typescript to the Version 4.9.5

We have updated the `Typescript` version to `v4.9.5`.

We also removed the `typescript` package from the `package.json` `resolutions`, so we are not forcing all the packages to use the same version.

```diff-json
  "resolutions": {
    ....
-    "typescript": "4.7.4",
    ....
  }
```

If you notice any issues with your project because of that, feel free to add version `4.9.5` into the `resolutions` field in your `package.json` file.

<Alert type="warning" title="Check and Update Your Code">
  Users should check their code for the potential breaking changes and update their code
  accordingly.
</Alert>

Unfortunately, updating the Typescript to the `v5+` is a bit problematic because we need to change our build process. It will probably be done for Webiny `v5.42.0` release.

#### 3.3 Multiple Roles and Teams Assignments - Changes on the `Identity` Interface

With the introduction of the [Multiple Roles and Teams Assignments (#4198)](/docs/release-notes/5.41.0/changelog#multiple-roles-and-teams-assignments-4198), we've done the following changes to the [`Identity`](https://github.com/webiny/webiny-js/blob/next/packages/api-authentication/src/types.ts#L28-L41) interface:

- `group` property was deprecated, users should use `groups` instead
- `team` property was deprecated, users should use `teams` instead

These changes were done because a Webiny user can now be assigned to multiple roles and teams. Which is also why the new properties accept an array of strings (instead of a single string, as it was before).

Note that the outlined changes are essentially only relevant for users who are using custom IdP implementations, like [Okta](/docs/enterprise/okta-integration) or [Auth0](/docs/enterprise/auth0-integration). This is because the [`Identity`](https://github.com/webiny/webiny-js/blob/next/packages/api-authentication/src/types.ts#L28-L41) interface and its `group` and `team` properties are only utilized in the context of custom IdP implementations. So, in case you're using a custom IdP implementation, make sure to update your code accordingly. For updated code examples, revisit the [Okta](/docs/enterprise/okta-integration) or [Auth0](/docs/enterprise/auth0-integration) integration articles.

<Alert>

  Note that if you continue using the old `group` and `team` properties, your code will still work. However, we recommend updating your code to use the new `groups` and `teams` properties, as the old properties will be removed in a future release.

</Alert>

### 4. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

<AdditionalNotes />
