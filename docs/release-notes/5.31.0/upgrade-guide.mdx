---
id: aafea9b6
title: Upgrade from 5.30.0 to 5.31.0
description: Learn how to upgrade Webiny from 5.30.0 to 5.31.0.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.30.0 to 5.31.0

</Alert>

<Alert type="info">

Make sure to check out the [5.31.0 changelog](/docs/release-notes/5.31.0/changelog) to get familiar with the changes introduced in this release.

</Alert>

## 1. Upgrade Webiny Packages

Upgrade all `@webiny/*` packages by running the following command:

```bash
yarn up "@webiny/*@5.31.0"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return `5.31.0`.

<Alert type="info">

Before moving on, make sure you commit all your changes.

</Alert>

## 2. Run the Upgrade Command

The next step is to run the project upgrade:

```bash
yarn webiny upgrade
```

<Alert type="warning">
  If the upgrade command ends in error "<strong>Script does not exist.</strong>", please clear your
  npx cache. You can run <strong>npx clear-npx-cache</strong> to clear it, or just delete it
  manually from your npx directory.
</Alert>

## 3. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

<Alert type="warning">

Make sure you deploy the entire system using the command shown above! You have to deploy all apps before using the system. Partial deploys may cause the upgrade to be applied incorrectly.

</Alert>

<Alert type="warning">

As stated in the [Upgrade Webiny](/docs/release-notes/upgrade-webiny#pre-production-environments-first) section, we recommend that you first deploy your changes into one of your pre-production environments, like `dev` or `staging`.

</Alert>

<Alert type="info">

Learn more about different deployment environments in the [CI/CD / Environments](/docs/core-development-concepts/ci-cd/environments) key topic.

</Alert>
