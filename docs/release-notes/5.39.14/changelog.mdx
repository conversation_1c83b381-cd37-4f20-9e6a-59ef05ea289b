---
id: a849914f
title: Webiny 5.39.14 Changelog
description: See what's new in Webiny version 5.39.14.
---

import { GithubRelease } from "@/components/GithubRelease";
import contentTab from "./assets/content-tab.png";
import revisionsTab from "./assets/revisions-tab.png";

<GithubRelease version={"5.39.14"} />

## Headless CMS

### Add a Default Tie-Breaker for Sorting When Using Elasticsearch/OpenSearch ([#4278](https://github.com/webiny/webiny-js/pull/4278))

Elasticsearch needs a default sorting field to be able to paginate correctly. If a user was sorting by fields which are not unique on the record (or a combination of non-unique fields), they would get inconsistent results. By adding a tie-breaker field to the sorters, namely the record's `id`, we help Elasticsearch/OpenSearch correctly sort records.

### Improve Entry/Revision Action Labels ([#4280](https://github.com/webiny/webiny-js/pull/4280))

This is a tiny UX improvement in the Admin app, related to Headless CMS actions, to make entry and revision actions be very clear and explicit. There was some confusion around particular actions, and we've improved the labels to clearly state the intent, since those actions can be applied either to the whole entry, or to a particular entry revision.

<Gallery>
  <Image src={contentTab} title={"Delete Entry Action"} />
  <Image src={revisionsTab} title={"Revisions List Actions"} />
</Gallery>

## Asset Delivery

### Fix Delivery of Large Assets ([#4281](https://github.com/webiny/webiny-js/pull/4281))

We've fixed some issues with the delivery of large files (both images and videos), but also improved time to first byte when assets are requested for the first time (and are not yet cached on the CDN). We've also added more logging to the delivery Lambda function, to get more visibility into the asset resolution, processing, and output.
