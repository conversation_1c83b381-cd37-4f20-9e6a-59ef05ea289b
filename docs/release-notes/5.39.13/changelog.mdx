---
id: 94533ab9
title: Webiny 5.39.13 Changelog
description: See what's new in Webiny version 5.39.13.
---

import { GithubRelease } from "@/components/GithubRelease";

<GithubRelease version={"5.39.13"} />

## Headless CMS

### Ensure Published Entries Are Fully Deleted ([#4265](https://github.com/webiny/webiny-js/pull/4265))

With this release, we've resolved an issue where deleting a published revision of a content entry would not make the entry disappear when listing content entries via [Headless CMS's READ GraphQL API](/docs/headless-cms/basics/graphql-api#read). In other words, the entry would still remain visible in the list of entries, even though it was deleted.

Now, when a published entry is deleted, it will no longer be visible in the list of entries returned by the READ API.
