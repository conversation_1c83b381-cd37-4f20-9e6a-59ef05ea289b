---
id: 5b449062
title: Webiny 5.42.2 Changelog
description: See what's new in Webiny version 5.42.2
---

import validationIndicators from "./assets/validation-indicators.png";
import autoExpand from "./assets/auto-expand.gif";
import addValue<PERSON>abel from "./assets/add-value-label.png";
import hcmsDecoratableComponents from "./assets/hcms-decoratable-components.png";

<GithubRelease version={"5.42.2"} />

## Headless CMS

### Improve Content Entry UX ([#4560](https://github.com/webiny/webiny-js/pull/4560))

We've introduced several UX improvements to make content creation a bit more straightforward:

- all multi-value fields now have a new renderer setting which allows you to specify a label for the _Add Value_ button
- new items within the multi-value object field are now automatically expanded
- icons have been normalized across field renderers (delete, move up/down)
- content entry route now updates the browser tab with the entry's title, if it exists
- invalid fields now display visual clues to the user, even on deeply nested fields, so it is easier to find the invalid input

<Gallery>
  <Image src={autoExpand} title={"Auto-Expand New Object Field Items"} />
  <Image src={addValueLabel} title={"Add Value Label"} />
  <Image src={validationIndicators} title={"Validation Indicators"} />
</Gallery>

### More Control and Customization for Content Entry Forms ([#4555](https://github.com/webiny/webiny-js/pull/4555) [#4556](https://github.com/webiny/webiny-js/pull/4556))

This update brings **greater flexibility and customization** to the Content Entry Form, improving both layout control and UI customization.

####  Enhanced Editor Width Control
We are introducing a new `ContentEntryEditorConfig` called `Width`, giving you more flexibility over its layout.

- **Customizable Width** – Define the editor’s width using values like **"100%"** or **"960px"** for a tailored editing experience.  
- **Model-Specific Control** – Apply width settings to specific content models using **model IDs**, ensuring precise customization.

#### New Decoratable Components
We've introduced two new decoratable components for greater flexibility in customizing the Content Entry Form:

- **`ContentEntryForm.Header.Meta`** – Customize the metadata section, including model info and status.  
- **`ContentEntryForm.Header.Title`** – Adjust how entry titles are displayed for a more tailored look.

<Image src={hcmsDecoratableComponents} alt="Headless CMS Content Entry Form - New Decoratable Components" />

## Form Builder

### Fixed Backend Data Validation Issues ([#4567](https://github.com/webiny/webiny-js/pull/4567))

With the [5.42.0 release](/docs/release-notes/5.42.0/changelog), a regression was introduced that caused validation issues when saving changes in Form Builder's form editor and also using the Terms of Service feature.

This issue has been fixed and you can now save changes in the Form Builder without any validation issues.

## Audit Logs

### Audit Logs Menu Item Now Visible in the Sidebar ([#4557](https://github.com/webiny/webiny-js/pull/4557))

Because of a mistake in the 5.42.0 release, the Audit Logs menu item was not visible in the sidebar for users that had this feature enabled.

This issue has been fixed, and the Audit Logs menu item is now visible in the sidebar for users that have this feature enabled.

## Other

### Enhanced Folder Listing and Performance ([#4539](https://github.com/webiny/webiny-js/pull/4539))

We've optimized folder listing for **greater efficiency and scalability**. Here’s what’s new:

- **Supports Large Datasets** – Folder retrieval now handles up to **10,000 records**, a limit set by **Elasticsearch**, ensuring smooth performance even with extensive data.
- **New Optimized GraphQL Query** – Introducing `listFoldersCompressed`, which returns data in **Gzip format** for faster and more efficient retrieval.

These improvements make folder management **faster, more reliable, and highly scalable**.

