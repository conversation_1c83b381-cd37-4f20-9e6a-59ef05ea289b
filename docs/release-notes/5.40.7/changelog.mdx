---
id: 49d22f69
title: Webiny 5.40.7 Changelog
description: See what's new in Webiny version 5.40.7.
---

<GithubRelease version={"5.40.7"} />

## Deployments

### Deployment of Amazon Cloudfront Distributions - Ignoring Changes In `staging` Property ([#4401](https://github.com/webiny/webiny-js/pull/4401))

Upon deploying API, Admin, and Website applications, Webiny deploys Amazon Cloudfront distributions. Once initially deployed, during subsequent deployments, the distributions are only updated with the new changes. They are never recreated from scratch.

<Alert>

  Learn more about Webiny's cloud architecture in the [Cloud Architecture](/docs/architecture/introduction) documentation.

</Alert>

However, with the introduction of the new Pulumi version and because of a change in the way Pulumi handles the [`staging`](https://www.pulumi.com/registry/packages/aws/api-docs/cloudfront/distribution/#staging_nodejs) property, the Cloudfront distributions would actually be recreated on the next deployment, which is certainly not the desired behavior. Especially if a custom domain was already associated with the distribution.

To fix this, we had to internally instruct Pulumi to ignore changes in the `staging` property, which will prevent the distributions from being recreated.


<Alert type={"warning"}>

  To this day, we've neved seen anybody relying on the `stating` property, so we're confident this change won't affect anyone. Still, if you are using it, please do give us a shout via our [community Slack](https://www.webiny.com/slack).

</Alert>
