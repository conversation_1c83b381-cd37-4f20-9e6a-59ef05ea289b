---
id: ********
title: Webiny 5.39.4 Changelog
description: See what's new in Webiny version 5.39.4.
---

import { GithubRelease } from "@/components/GithubRelease";
import htmlTagOption from "./assets/html-tag-option.png";

<GithubRelease version={"5.39.4"} />

## Page Builder

### Addressed Public Website Routing Issue (Multi-tenant Projects) ([#4083](https://github.com/webiny/webiny-js/pull/4083))

With the [5.39.0 release](/docs/release-notes/5.39.0/changelog), a regression was introduced that caused the public website routing in multi-tenant projects not to work as expected.

This issue has now been addressed, and the public website routing in multi-tenant projects should now work as expected.

### Image Page Element - Improved SVG Support ([#4041](https://github.com/webiny/webiny-js/pull/4041))

We've introduced the ability to use the [`object`](https://www.w3schools.com/tags/tag_object.asp) HTML tag when rendering SVG images with the **Image** page element.

Note that, for backwards compatibility, existing SVG images on pages will still be rendered via the `img` HTML tag. But, if needed, users are able to change this via the new **HTML Tag** input:

<Image src={htmlTagOption} alt="HTML Tag Option" />

Furthermore, note that once a Webiny project is upgraded, all newly selected images will have their HTML tag set to "Auto-detect". In other words, if a user selects a file that ends up with the **.svg** extension, the `object` HTML tag will be used. Otherwise, the `img` HTML tag will be used.

## Folder Level Permissions (FLP)

### API Tokens Losing Access To Content ([#4082](https://github.com/webiny/webiny-js/pull/4082))

Prior to this release, in case [folder level permissions](/docs/enterprise/aacl/folder-level-permissions) were assigned on a folder, accessing content in it using an API token as a way of authenticating would no longer work. The user would simply start receiving `NOT_AUTHORIZED` errors, and the only way for the API token to gain back access to the content would be to remove the assigned folder level permissions.

This issue has now been addressed. When accessing content in a folder that has folder level permissions assigned to it, using an API token as a way of authenticating will work as expected.

<Alert type="info">

With this issue now resolved, also note that API tokens are not subject to folder level permissions, and they will
always have access to all content. We've updated the FAQ section of the existing [Folder Level
Permissions](/docs/enterprise/aacl/folder-level-permissions#can-i-use-folder-level-permissions-with-api-tokens)
article to reflect this.

</Alert>
