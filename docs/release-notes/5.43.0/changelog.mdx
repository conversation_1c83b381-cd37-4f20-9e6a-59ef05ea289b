---
id: 6611aca0
title: Webiny 5.43.0 Changelog
description: See what's new in Webiny version 5.43.0.
---

import { GithubRelease } from "@/components/GithubRelease";
import samePackageVersions from "./assets/same-package-versions.png";

<GithubRelease version={"5.43.0"} />

## Breaking Changes❗

### Existing Amazon VPC Feature - Addressed Amazon OpenSearch Service Configuration Issue ([#4532](https://github.com/webiny/webiny-js/pull/4532))

Prior to this release, when deploying a Webiny project with the [existing Amazon VPC](https://www.webiny.com/docs/enterprise/use-existing-amazon-vpc) configuration in place, the Amazon OpenSearch service that Webiny deploys would not get correctly attached to the specified Amazon VPC (related subnets and security groups).

Because of the fact that, with this release, the VPC configuration now gets correctly attached to the Amazon OpenSearch cluster, redeploying an existing Webiny instance will cause the cluster to be **DESTROYED AND RECREATED**️, which means **all data in the existing cluster will be lost**❗

If you're using the existing Amazon VPC configuration in your Webiny project, make sure to check out the [5.43.0 migration guide](/docs/release-notes/5.43.0/upgrade-guide) guide. For more information about the change itself, check out the [dedicated changelog item](#existing-amazon-vpc-feature-addressed-amazon-open-search-service-configuration-issue-4532-1) below.

### Folder Level Permissions - Editors Can Now Manage Folders ([#4615](https://github.com/webiny/webiny-js/pull/4615))

Prior to this release, only users with **Owner** permissions could manage folders. With this update, users with **Editor** permissions can now also create, edit, and delete folders.

This change is a breaking one, as it modifies the existing permissions structure. If you have custom permissions set up, you may need to adjust them to accommodate this new functionality.

Make sure to check out the [5.43.0 migration guide](/docs/release-notes/5.43.0/upgrade-guide) guide in case this change affects you.

### `fastify` 4.29.0 ([#4464](https://github.com/webiny/webiny-js/pull/4464))

While working on the [TypeScript 5.3.3](#type-script-5-3-3-4464) upgrade, the [`fastify`](https://fastify.dev/docs/v4.29.x/) NPM package was also updated to version `4.29.0`.

In case your project contains custom code that directly interacts with `fastify`, you may need to update your code to be compatible with the new version. We recommend checking their official documentation for any breaking changes or updates that may affect your project.

Make sure to check out the [5.43.0 migration guide](/docs/release-notes/5.43.0/upgrade-guide) guide in case this change affects you.

<Alert type="info">

  Webiny uses [`fastify`](https://fastify.dev/docs/v4.29.x/) to power its GraphQL and REST APIs. The `fastify` package
  is a high-performance web framework for Node.js, designed to be lightweight and efficient.

</Alert>

## Advanced Content Organization (ACO)

This release brings a significant upgrade to how folders and folder-level permissions are processed. This is a major step forward in improving the performance and usability of Webiny, especially for projects with a large number of folders and complex permission structures.

In the following sections, we cover all of the key improvements and changes that come with this release.

<Alert type="info">

To learn more about the Folder Level Permissions feature (FLP), please refer to the [Folder Level
Permissions](/docs/enterprise/aacl/folder-level-permissions) documentation.

</Alert>

### Improved Folder Loading Performance ([#4615](https://github.com/webiny/webiny-js/pull/4615))

We've vastly improved the performance of the Advanced Content Organization (ACO) feature, specifically in how folders are loaded and managed.

Essentially, folders are now lazy-loaded, which means that only the folders you need to see are loaded at any given time. This greatly improves performance and scalability, especially for projects with a large number of folders.

We've been testing the new folder loading mechanism with projects that have over 10,000 folders, and the results have been very positive. The new system is designed to handle large numbers of folders efficiently, making it easier to manage complex content structures.

### Overridable Inherited Permissions ([#4630](https://github.com/webiny/webiny-js/pull/4630))

Inherited permissions can now be overridden at any folder level, giving you more granular control over access.

For example, a specific user might be granted **Editor** permissions on a specific folder, while still inheriting **Viewer** permissions from a parent folder. Vice versa, a user can have **Viewer** permissions on a folder but be granted **Editor** permissions on a child folder.

This flexibility allows for more complex permission structures, enabling you to tailor access to specific folders based on your project's needs.

### Editors Can Now Manage Folders ([#4615](https://github.com/webiny/webiny-js/pull/4615))

Prior to this release, only users with **Owner** permissions could manage folders. With this update, users with **Editor** permissions can now also create, edit, and delete folders.

<Alert type={"danger"} title={"Breaking Change"}>

This change is a breaking one, as it modifies the existing permissions structure. If you have custom permissions set
up, you may need to adjust them to accommodate this new functionality.

Make sure to check out the [5.43.0 migration guide](/docs/release-notes/5.43.0/upgrade-guide) guide in case this
change affects you.

</Alert>

### Introducing the "No Access" Permission ([#4624](https://github.com/webiny/webiny-js/pull/4624))

On top of the existing **Viewer**, **Editor**, and **Owner** permission, we have introduced a new **No Access** permission. As the name itself suggests, the new permissions allows users to explicitly deny access to a folder for a specific user or team.

Note that, once set, the **No Access** permission cannot be overridden. In other words, if a user has **No Access** permission on a folder, they will not be able to access it, regardless of any inherited permissions from parent folders. This is also the case for all folders that are children of the folder with **No Access** permission.

### Folders - `path` Property and Enhanced Search Capabilities

ACO's `Folder` entity now also includes a new `path` field, which is a string constructed from the folder's slug and its parent folders' slugs, creating a hierarchical representation of the folder's location within the content structure. This field is automatically generated and updated whenever a folder is created or modified. A couple of examples of the `path` field values are:

- `/folder-1`
- `/folder-1/folder-2`
- `/folder-1/folder-2/folder-3`

Which now also allows for more efficient searching and filtering of folders based on their paths. For example, via the `listFolders` GraphQL query, you can now filter folders based on their `path` value using the following operators:

```
path: string
path_not: string
path_contains: string
path_not_contains: string
path_in: string[]
path_not_in: string[]
path_startsWith: string
path_not_startsWith: string
```

The following example demonstrates how to use the `path` field in a GraphQL query to list all child folders of a specific folder:

```graphql
{
  aco {
    listFolders(where: { type: "some-model", path_startsWith: "folder-1/folder-2" }) {
      data {
        title
      }
    }
  }
}
```

#### Improved `getAncestors` Method

Consequently, the `getAncestors` method has been optimized to leverage the new `path` field. Previously, this method would retrieve all folders to construct the ancestor tree, which could be inefficient for projects with a large number of folders.

Now, it uses the `path` value to directly fetch only the relevant folders, significantly improving performance—especially for systems with a large number of folders.

The following example demonstrates how to use the `getAncestors` method in a context plugin:

```ts
import { createContextPlugin } from "@webiny/api-serverless-cms";

createContextPlugin(async ctx => {
  const someFolder = await ctx.aco.folder.get("some-folder-id");
  const ancestors = await ctx.aco.folder.getAncestors(someFolder);

  // Do something with the ancestors.
  // ...
});
```

## Headless CMS

### Heartbeat-Based Timeout Introduced in Record Locking ([#4583](https://github.com/webiny/webiny-js/pull/4583))

We've updated the Record Locking system to be more reliable and responsive. 

Previously, records were unlocked using lifecycle-based triggers, which could lead to inconsistent behavior—especially if the client disconnected unexpectedly or didn’t clean up properly.

Now, the locking mechanism uses a heartbeat signal to determine if a user is still actively editing a record. If the system doesn't receive a heartbeat for 30 seconds, the record is automatically unlocked and made available to others.

This change makes collaborative editing more robust and helps prevent records from remaining locked due to crashes, lost connections, or idle sessions.

<Alert type="info">

  In case you missed it, with the [5.40.0 release](/docs/release-notes/5.40.0/changelog#introducing-record-locking-mechanism-4065), we introduced a new Record Locking mechanism that allows multiple users to collaborate on the same record without conflicts. This feature is particularly useful for teams working on content-heavy projects, as it prevents overwriting changes made by others.
  
</Alert>

## Deployments

### Existing Amazon VPC Feature - Addressed Amazon OpenSearch Service Configuration Issue ([#4532](https://github.com/webiny/webiny-js/pull/4532))

Prior to this release, when deploying a Webiny project with the [existing Amazon VPC](https://www.webiny.com/docs/enterprise/use-existing-amazon-vpc) configuration in place, the Amazon OpenSearch service that Webiny deploys would not get correctly attached to the specified Amazon VPC (related subnets and security groups).

For example, the following configuration would not work as expected. Contrary to the expectation, the deployed Amazon OpenSearch cluster would not be attached to the specified `OPENSEARCH_PRIVATE_SUBNETS` subnets and `OPENSEARCH_SECURITY_GROUPS` security groups:

```ts apps/core/webiny.application.ts
import { createCoreApp } from "@webiny/serverless-cms-aws/enterprise";

const OPENSEARCH_PRIVATE_SUBNETS = ["private-subnet-id-1", "private-subnet-id-2"];
const OPENSEARCH_SECURITY_GROUPS = ["security-group-id-1"];

const LAMBDA_FUNCTIONS_PRIVATE_SUBNETS = ["private-subnet-id-1", "private-subnet-id-2"];
const LAMBDA_FUNCTIONS_SECURITY_GROUPS = ["security-group-id-1"];

export default createCoreApp({
  pulumiResourceNamePrefix: "wby-",
  openSearch: true,
  vpc: {
    useExistingVpc: {
      elasticSearchDomainVpcConfig: {
        subnetIds: OPENSEARCH_PRIVATE_SUBNETS,
        securityGroupIds: OPENSEARCH_SECURITY_GROUPS
      },
      lambdaFunctionsVpcConfig: {
        subnetIds: LAMBDA_FUNCTIONS_PRIVATE_SUBNETS,
        securityGroupIds: LAMBDA_FUNCTIONS_SECURITY_GROUPS
      }
    }
  }
});
```

With this release, the underlying issue should no longer occur, and the above configuration will work as expected—with one minor change: instead of using `elasticSearchDomainVpcConfig`, users should now use `openSearchDomainVpcConfig`.

For more information on this change and also how to upgrade your existing Webiny project, please refer to the [5.43.0 migration guide](/docs/release-notes/5.43.0/upgrade-guide) guide.

<Alert type={"danger"} title={"Breaking Change"}>

Because of the fact that, with this fix in place, the VPC configuration now gets correctly attached to the Amazon
OpenSearch cluster, redeploying an existing Webiny instance will cause the cluster to be **DESTROYED AND RECREATED**️,
which means **all data in the existing cluster will be lost**❗

If you're using the existing Amazon VPC configuration in your Webiny project, make sure to check out the [5.43.0
migration guide](/docs/release-notes/5.43.0/upgrade-guide) guide.

</Alert>

### Introducing the `webiny refresh` Command ([#4552](https://github.com/webiny/webiny-js/pull/4552))

Multiple times in the past, we've seen users needing to run the [`pulumi refresh`](https://www.pulumi.com/docs/iac/cli/commands/pulumi_refresh/) command in order to refresh the state of their Pulumi stacks.

Up until now, to do this, users had to run the following command:

```bash
yarn webiny pulumi api --env dev -- refresh --yes
```

To make this process a bit more straightforward, instead of the above command, users can now simply run:

```bash
yarn webiny refresh api --env dev
```

Functionally, both commands do the same thing, but the new one is shorter and easier to remember.

## Development

### TypeScript 5.3.3 ([#4464](https://github.com/webiny/webiny-js/pull/4464))

TypeScript has been upgraded from version `4.9.5` to `5.3.3`.

Note that, at the time of this release, the `5.3.3` version is not the latest TypeScript version available. The latest version is `5.8.3`, but we have decided to stay on `5.3.3` for now, as it is the latest version that is compatible with all of our dependencies.

### `fastify` 4.29.0 ([#4464](https://github.com/webiny/webiny-js/pull/4464))

With the [TypeScript upgrade](#type-script-5-3-3-4464), the [`fastify`](https://fastify.dev/docs/v4.29.x/) NPM package was also updated to version `4.29.0`.

In case your project contains custom code that directly interacts with `fastify`, you may need to update your code to be compatible with the new version. We recommend checking their official documentation for any breaking changes or updates that may affect your project.

<Alert type="info">

Webiny uses [`fastify`](https://fastify.dev/docs/v4.29.x/) to power its GraphQL and REST APIs. The `fastify` package
is a high-performance web framework for Node.js, designed to be lightweight and efficient.

</Alert>

### Webiny Packages Versions ([#4628](https://github.com/webiny/webiny-js/pull/4628))

From now on, each time you run the Webiny CLI, it will check if all `@webiny/*` NPM packages in your project are using the same version. This is important because Webiny packages are tightly integrated, and having different versions can lead to unexpected issues.

The following screenshot shows an example of the CLI warning you about a version mismatch:

<Image src={samePackageVersions} alt="Webiny package versions check" />
