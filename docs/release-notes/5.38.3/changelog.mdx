---
id: ec1cd8a9
title: Webiny 5.38.3 Changelog
description: See what's new in Webiny version 5.38.3.
---

<GithubRelease version={"5.38.3"} />

## File Manager

### Fix Append Operation Via The Bulk Edit Feature ([#3777](https://github.com/webiny/webiny-js/pull/3777))

We have successfully resolved an issue that users were facing when trying to append new values to a field without existing data using the File Manager's Bulk Edit feature.

With this release, the identified issue has been addressed. Users can confidently append new data without experiencing any errors.

### Preventing Accidental Closure in Bulk Edit Dialog  ([#3790](https://github.com/webiny/webiny-js/pull/3790))

With this release, we are preventing the user from accidentally closing the “Bulk Edit” dialog by disabling the default behaviour of closing the dialog when clicking on the screen.

## Deployments

### Globally Installed Pulumi CLI No Longer Causes Issues When Deploying ([#3793](https://github.com/webiny/webiny-js/pull/3793))

Previously, if a user already had Pulumi CLI installed on their machine, and then tried to deploy their Webiny project, in some cases the deployment would fail. Essentially, the issue was that the globally installed Pulumi CLI would conflict with the one that is installed locally in the project.

This has been fixed. Now, if a user has Pulumi CLI installed globally, it will not cause any issues when deploying your Webiny project.

### Project Creation - Updated Minimum Required Versions ([#3796](https://github.com/webiny/webiny-js/pull/3796))

We have updated the minimum required versions of the following dependencies Webiny relies on:

- Node.js: 16.0.0
- NPM: 10.0.0
- Yarn: 1.22.21
