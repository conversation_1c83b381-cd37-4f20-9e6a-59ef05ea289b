---
id: upgrade-guide
title: Upgrade from 5.23.0 to 5.23.1
description: Learn how to upgrade Webiny from 5.23.0 to 5.23.1.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.23.0 to 5.23.1

</Alert>

<Alert type="info">

This Webiny upgrade only requires an update of `npm` packages.
Make sure to check out the [5.23.1 changelog](/docs/release-notes/5.23.1/changelog) to get familiar with the changes introduced in this release.

</Alert>

## 1. Upgrade Webiny Packages

Upgrade all `@webiny/*` packages by running the following command:

```bash
yarn up "@webiny/*@5.23.1"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return `5.23.1`.

<Alert type="info">

You don't need to deploy anything, as this release only addresses a problem with the `watch` command, which is only used in local development.

</Alert>
