---
id: aafea9be
title: Webiny 5.23.1 Changelog
description: See what's new in Webiny version 5.23.1.
---

import { <PERSON><PERSON> } from "@/components/Alert";

# Changes

In the 5.23.0 release, we made some internal changes to the bundling process related to the Webiny Control Panel, which we're actively working on. One of the changes managed to sneak past our tests and mess up the `watch` functionality :sob:. This 5.23.1 release addresses the aforementioned issue.

<Alert type="info">

If you're not using the `watch` command in your development workflow, you can completely ignore this patch, because it _only_ affects the `watch` process. Build and deploy process works as usual even without this patch, so you CI/CD pipelines are good!

</Alert>
