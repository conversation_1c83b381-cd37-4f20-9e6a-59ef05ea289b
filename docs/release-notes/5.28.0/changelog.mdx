---
id: changelog
title: Webiny 5.28.0 Changelog
description: See what's new in Webiny version 5.28.0.
---

# Changes

This release contains some important fixes for bugs reported by the community, and features to improve developer's experience.

## Headless CMS

### Fix File Field Renderer ([#2453](https://github.com/webiny/webiny-js/pull/2453))

File field renderer is now correctly handling initial empty value for file field which is configured to accept multiple values (an array of file URLs). Shout out goes to [Sunny Park](https://github.com/sunnythelucky) for reporting this issue! 🙌

### Add Support for Fields and Layout in `createContentModel` Mutation

Until now, if you wanted to create a content model, you had to first create an empty content model, then issue an update to populate it with fields (and field layout). We now added support for model creation with fields in one go. This will be very useful for those of you automating data migrations.

## Page Builder

### Fix Prerendering of "Not Found" Pages ([#2464](https://github.com/webiny/webiny-js/pull/2464))

Prior to this release, the "not found" page had a lot of special meaning associated with it: it was a "special" page, that was not visible in the pages list (and was not returned by GraphQL queries). It was only editable via the Website Settings module, and if you reassigned this page to some other custom page, you basically lost it forever. Apart from not being able to revert to this built-in page, our Prerendering Service was unable to properly handle the newly assigned Not Found page, due to the Website app setup.

With this release, we're removing all special meaning from the Not Found page. It's now just a regular page, like any other page in the system. It's always visible, and returned by the GraphQL API. The Website app is also upgraded to use any page as a Not Found page, if it's configured in Website Settings. A lot of internal code was removed by this simplification, and we're very happy about it.

## Development

### React Composition Package ([#2460](https://github.com/webiny/webiny-js/pull/2460))

Composition of components is not a new thing. Higher order components have been around forever. The issue is, how do you compose components that live in `node_modules`, outside of your control and your source code, and then inject them back into the existing app?

That's what our `@webiny/react-composition` package solves. And you've been using this in Webiny for several months now. Our Admin app heavily relies on this concept. You can learn about this approach in our [existing docs](/docs/admin-area/basics/framework#the-compose-component). We were very careful with pushing this concept into Webiny, so we only implemented it in several parts of the system, to see how it works out. Over time, after building several PoCs and our own apps, we realized `This is it!`, and we now want to gradually push this more into the core of Webiny.

This tiny framework allows you to write pluginable apps, abstract stuff away, and let developers customize your app by composing existing components from outside. In the very near future, we'll also expand this approach onto our Website app, and remove the existing boilerplate code, but still keep the app extendable and configurable.

It's a standalone package, you can use it in your own apps outside Webiny. We'll be publishing more content around this package, how to use it, etc. in the near future.
