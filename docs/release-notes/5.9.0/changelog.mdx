---
id: aafea9e8
title: Webiny 5.9.0 Changelog
description: See what's new in Webiny version 5.9.0.
---

import { <PERSON><PERSON> } from "@/components/Alert";

# Changes

This document highlights the most important fixes, improvements, and features, that were introduced in Webiny `5.9.0`.

<Alert type="info" title="How To Upgrade?">

Please check the [Webiny 5.9.0 migration guide](/docs/release-notes/5.9.0/upgrade-guide) for the upgrade steps.

</Alert>

## Development

### Improved Scaffolding([#1720](https://github.com/webiny/webiny-js/pull/1720))

For this release, we've gone through a complete overhaul of the existing **GraphQL API Package** and **Admin Area Package**. The following are some of the most important changes we made.

![Improved Scaffolding](./scaffolds.gif)

<Alert type="success">

To learn more about the changes we made and the reasoning behind them, check out the dedicated [Webiny 5.9.0 - Improved Scaffolding](https://www.webiny.com/blog/webiny-v5.9.0-improved-scaffolds-and-development) blog post.

</Alert>

<Alert type="info">

To learn more about scaffolding and all of the available scaffolds, check out the [Scaffolding](/docs/core-development-concepts/scaffolding/introduction) how-to guides section.

</Alert>

#### Extend GraphQL API (previously GraphQL API Package)

##### DynamoDB-only Setup

Over time, we've realized that the DynamoDB + (Amazon) ElasticSearch setup, that the **GraphQL API Package** scaffold generated, was just too complex and often not needed.

So, to make things simpler, the new **Extend GraphQL API** scaffold now generates application code that only interacts with DynamoDB, while essentially still maintaining the same feature set that the previous version of the scaffold offered.

Note that in case interaction with ElasticSearch is still needed, for example because a user has a complex data querying requirement, it's still possible to do it. A tutorial on how to utilize ElasticSearch within the generated application code is something we plan to cover in the near future.

##### Improved Application Testing Setup

Another important element of the **Extend GraphQL API** scaffold is the improved application testing setup.

It enables users to write all three fundamental types of tests - unit, integration, and end-to-end (E2E), this time against real **deployed** cloud infrastructure resources.

When compared to the previous approach, in which we were relying on tools like [`jest-dynalite`](https://www.npmjs.com/package/jest-dynalite) and [`@shelf/jest-elasticsearch`](https://www.npmjs.com/package/@shelf/jest-elasticsearch) in order to run needed services locally, this enables users to achieve a higher level of confidence that their application code works as expected.

#### Extend Admin Area (previously Admin Area Package)

##### The Supporting GraphQL API

Previously, the **Admin Area Package** scaffold (now called **Extend Admin Area**) would only generate the necessary Admin Area (React) application code, without the backend GraphQL API to support it. This required users to first run the **GraphQL API Package** scaffold, which, as we've learned, was often something that would confuse them.

To make the whole process easier and more straight-forward, the **Extend Admin Area** will now generate both parts - the necessary Admin Area (React) and the supporting GraphQL API application code.

#### Other

##### No New Packages

Prior to this release, both **GraphQL API Package** and **Admin Area Package** scaffolds were generating new code inside of separate packages, by default in a separate `packages` folder.

In contrast to that, from now on, new code will be generated directly in your existing **API** (`api/code/graphql/src/plugins`) and **Admin Area** (`apps/admin/code/src/plugins`) project applications.

Not only this makes the overall codebase easier to grasp, but also removes all of the extra complexity that monorepos and working with multiple packages bring.

##### No Manual Steps Required

Prior to this release, once the **GraphQL API Package** and **Admin Area Package** scaffolds have finished generating code, users were still required to complete a couple of manual steps, like registering the newly generated plugins in their application code, executing GraphQL (installation) mutations via the deployed GraphQL API, and so on.

This is no longer the case. Everything happens automatically, which we believe will additionally improve the overall development experience.

### Improved AWS Lambda Function Bundling Error Output ([#1693](https://github.com/webiny/webiny-js/pull/1693))

Previously, if Webpack threw an error during AWS Lambda function code builds, you'd get a wall of irrelevant and meaningless stack output, and you'd have to scroll through dozens of lines to get to the top of it, to see the actual error. With this release, AWS Lambda function code builds will only print the relevant error information:

![AWS Lambda Function Build Error](./webpack-error.png)

## Security

### Reorganize Security Packages ([#1693](https://github.com/webiny/webiny-js/pull/1693))

Security layer in Webiny consists of plugins spread across several packages. Up until now, it was difficult to navigate these packages and understand the responsibilities of each one of them. We've restructured and refactored the code into packages with better naming, proper separation of concerns, and we've also introduced several new plugins to make integrations and extending easier.

This is an overview of all new packages. As you can see, it's now easy to follow the API/React counterparts and get an idea of what each one of them does:

![API and React Security Packages](./security-packages.png)

If you're building a custom user signup flow, this will now help you see how the Admin Area users work. Feel free to borrow the ideas for your own apps.

### Fix Handling of Emails That Contain Special Characters ([#1693](https://github.com/webiny/webiny-js/pull/1693))

There was a bug with user's emails containing special characters, like `<EMAIL>`. The `+` would crash the app entirely once it made its way to the URL, like `/users?login=<EMAIL>`. This is now sorted, and email addresses are now properly encoded/decoded.

A shoutout to [@xintellion](https://github.com/xintellion) for spotting and reporting this one! 🙌 🍻

## File Manager

### Fixed Serving of Non-Image Files ([#1710](https://github.com/webiny/webiny-js/pull/1710))

When files are uploaded via [File Manager](https://www.webiny.com/serverless-app/file-manager/), ultimately, they must always be served to users over a CDN ([Amazon Cloudfront](https://aws.amazon.com/cloudfront/)). This was true for images, but in every other case, files would be directly served from S3, making the actual file download less optimal.

This has now been taken care of. Whenever possible, all types of files will be served from a CDN.

## Headless CMS

### Rich-text Field Not Being Compressed ([#1709](https://github.com/webiny/webiny-js/pull/1709))

Fixed an error-case in which the content of the rich-text field was not compressed, before being stored to the database.
