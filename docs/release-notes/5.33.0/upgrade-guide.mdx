---
id: aafea9b5
title: Upgrade from 5.32.0 to 5.33.0
description: Learn how to upgrade Webiny from 5.32.0 to 5.33.0.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.32.0 to 5.33.0

</Alert>

<Alert type="info">

Make sure to check out the [5.33.0 changelog](/docs/release-notes/5.33.0/changelog) to get familiar with the changes introduced in this release.

</Alert>

## 1. Upgrade Webiny Packages

Upgrade all `@webiny/*` packages by running the following command:

```bash
yarn up "@webiny/*@5.33.0"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return `5.33.0`.

## 2. Upgrading CMS Models Via Code Definitions

<Alert type="info">

If you do not have CMS Models defined via code (via `CmsModelPlugin`), you can skip this step.

</Alert>

Since we introduced a new property, `storageId`, into CMS Model Field definition, you must add it to your existing CMS Model Field definitions.
This is only for the CMS models which are created prior to the 5.33.0.
Please read the **Upgrading CMS Model Plugin Definitions** of [this](/docs/headless-cms/extending/content-models-via-code-storage-id#upgrading-cms-model-plugin-definitions) article.

## 3. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

<Alert type="warning">

Make sure you deploy the entire system using the command shown above! You have to deploy all apps before using the system. Partial deploys may cause the upgrade to be applied incorrectly.

</Alert>

## 4. Upgrade in the Admin Area

There is a database upgrade that we execute from the Admin Area. To make sure it starts, clear your browser cache and log in.

When upgrade screen shows, just follow the instructions.

Note that upgrade is done across all the models you have in all tenants and all locales.

<Alert type="warning">

As stated in the [Upgrade Webiny](/docs/release-notes/upgrade-webiny#pre-production-environments-first) section, we recommend that you first deploy your changes into one of your pre-production environments, like `dev` or `staging`.

</Alert>

<Alert type="info">

Learn more about different deployment environments in the [CI/CD / Environments](/docs/core-development-concepts/ci-cd/environments) key topic.

</Alert>
