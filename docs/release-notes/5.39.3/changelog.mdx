---
id: b7ac56ba
title: Webiny 5.39.3 Changelog
description: See what's new in Webiny version 5.39.3.
---

import { GithubRelease } from "@/components/GithubRelease";

<GithubRelease version={"5.39.3"} />

## Page Builder

### Fix Missing Actions in Page Details Drawer ([#3982](https://github.com/webiny/webiny-js/pull/3982))

This update effectively resolves an issue encountered when Advanced Access Control Layer (AACL) is enabled. Specifically, it addresses a situation where pages located within the root folder inadvertently caused certain actions within the Page Details drawer to disappear.

### Export and Import Pages ([#3851](https://github.com/webiny/webiny-js/pull/3851))

In this release, we have improved the export and import of pages functionality. In releases before this one, users could not export more than 16 pages due to [AWS Lambda recursive loop detection](https://docs.aws.amazon.com/lambda/latest/dg/invocation-recursion.html), introduced by AWS recently.

On the export side, we've tested the export for around 500-600 pages, and it takes ~3 minutes to complete. The total size of the pages file, zipped, is around 400MB. Of course, these were our test pages. Users can have larger or smaller pages, so the ZIP file will vary in size.

On the import side, there is a limitation of ~450MB for the ZIP file size, which we will improve in our future releases.

## File Manager

### Enhance Filtering Mechanism ([#3978](https://github.com/webiny/webiny-js/pull/3978))

In this release, we have refined the filtering behaviour within the File Manager. Previously, when the user reset all filters, the files were loaded without specifying the folder location.

Now, with the updated logic, if no filters are set or contain undefined values, entries will be loaded based on their respective locations.

## Other

### Webiny CLI - Updated Internal Dependencies ([#3986](https://github.com/webiny/webiny-js/pull/3986))

We've received multiple reports of new users not being able to build and access **Admin Area** and **Website** frontend apps.

After a quick investigation, we've found that the issue was caused because of a number of updates that happened within [Browserify](https://github.com/browserify/browserify) packages, which Webiny uses internally when building mentioned apps.

The issues have been addressed and both **Admin Area** and **Website** frontend apps should now build and work as expected.
