---
id: 809a794d
title: Webiny 5.38.5 Changelog
description: See what's new in Webiny version 5.38.5.
---

import pagesNotShownInFolder from "./assets/pages-not-shown-in-folder.png";

<GithubRelease version={"5.38.5"} />

## Page Builder

### Pages Not Being Shown In Folders (with Folder Level Permissions Enabled) ([#3838](https://github.com/webiny/webiny-js/pull/3838))

With this release, we've fixed an issue where, with the [Folder Level Permissions](/docs/enterprise/aacl/folder-level-permissions) feature enabled, one or more pages would sometimes not be shown in a folder. 

<Image src={pagesNotShownInFolder} alt={"Pages Incorrectly Not Visible In a Folder"} />

Essentially, this issue was caused by an incorrect underlying database query, whose purpose was to ensure only pages that the user has access to are shown in the folder.

This issue has now been fixed, and the pages should be shown correctly. 

### Replaced JsonPack with GZIP For Page Content Compression ([#3839](https://github.com/webiny/webiny-js/pull/3839))

Upon storing to the database, in order to bypass potential database size limitations, the actual page content is always compressed. Prior to this release, we were using [JsonPack](https://www.npmjs.com/package/jsonpack) for this purpose. With this release, we've replaced JsonPack with [GZIP](https://www.npmjs.com/package/gzip-js).

This change was made because, in some cases, the page content was still too large to be stored in the database. This was especially true for very large pages.

With this change, the page content is now reduced by an extra 20-80%, depending on its original size. Most importantly, this change further reduces the chances of the content being too large to be stored in the database.

#### Backwards Compatibility

Note that no extra changes are required on your end. The page content data is automatically converted to the new format when pages are saved.
