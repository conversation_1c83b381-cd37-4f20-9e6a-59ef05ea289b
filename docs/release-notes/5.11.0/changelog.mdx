---
id: aafea9e0
title: Webiny 5.11.0 Changelog
description: See what's new in Webiny version 5.11.0.
---

import { <PERSON>ert } from "@/components/Alert";

# Changes

This document highlights the most important fixes, improvements, and features, that were introduced in Webiny `5.11.0`.

<Alert type="info" title="How To Upgrade?">

Please check the [Webiny 5.11.0 migration guide](/docs/release-notes/5.11.0/upgrade-guide) for the upgrade steps.

</Alert>

## Headless CMS

### Introduce Object Field ([#1780](https://github.com/webiny/webiny-js/pull/1780))

The new `object` field allows you to build nested data structures and also have those data structures be repeatable (like an array of nested objects). It supports nesting, so you can nest objects within other objects. Object field respects all the rules and behaviors of all the other existing fields (`ref` field, `richText` field, etc.). You also get a full GraphQL Schema auto-complete on your nested fields.

![Model Data Using the Object Field](./objectField.gif)

> NOTE: at this point, we do not support filtering of data based on the values within the object field. However, we do plan to add support for that type of querying in the future.

### Always Load The Latest Published Entry ([#1775](https://github.com/webiny/webiny-js/pull/1775))

Read API now always returns the latest published content entry, no matter which version of the entry was originally linked. This ensures that, no matter how many new versions of content you publish, the reference always points to the latest published version.

## Form Builder

### Pattern Type Validators Missing From UI ([#1778](https://github.com/webiny/webiny-js/pull/1778))

Pattern validators are now visible in the Form Builder editor, when configuring individual form fields. Besides, we also fixed the setup of the `website` app, and those field validators are now working properly when the form is being submitted.

![Pattern Field Validators in the Form Editor](./patternValidators.png)

Shout out to [Karthick Raja](https://github.com/karthick3018) for getting the ball rolling on this issue! &#128640 &#129505

## Security

### Fine-grained AWS policies ([#1741](https://github.com/webiny/webiny-js/pull/1741))

From now on, all new Webiny projects will benefit from improved security, due to fine-grained policies used with AWS Lambda roles (instead of using the default `AdministratorAccess` policy, which was previously the case).

In addition to that, you can also use a fine-grained policy for the [CLI user](/docs/infrastructure/aws/configure-aws-credentials#deploy-webiny-project-aws-cloud-formation-template), which is important for making secure deploys of your Webiny project.

At the moment, for existing projects we don’t offer a migration path, but if you’re interested in applying this improvement to your Webiny project, you can contact us via our community [Slack](https://webiny.com/slack).

## Development

### Pulumi v3 Upgrade ([#1787](https://github.com/webiny/webiny-js/pull/1787))

From now on, all Webiny projects will use [Pulumi](/docs/infrastructure/pulumi-iac/iac-with-pulumi) v3.

For Webiny users, this will make it possible to have access to all of the new features and fixes that the framework will receive in the future.

In terms of existing projects and upgrading, there shouldn't be any major changes or extra steps that you'll need to perform. But, there are a couple of things to have in mind. Be sure to check the [upgrade guide](/docs/release-notes/5.11.0/upgrade-guide) for more information.

### Improved CI/CD Scaffolds ([#1786](https://github.com/webiny/webiny-js/pull/1786))

A couple of useful updates were made to the existing CI/CD scaffold.

<Alert type="success">

Learn more about CI/CD and how to set it up for your Webiny project in our [CI/CD key topics](/docs/core-development-concepts/ci-cd/introduction).

</Alert>

#### Updates to the Pull Requests Workflows

The pull request workflow that gets created during the scaffolding process will now make sure that all of the changes are deployed into a short-lived environment. This makes it possible for the team to check out all of the changes on their own, before the PR is merged. And, once the PR is either merged or simply closed, the short-lived environment will automatically be destroyed.

#### Improved Testing

Prior to this release, with the single `yarn test` command, the created workflows would run all of your application tests immediately - unit, integration, and end-to-end (E2E).

This has now been divided into separate commands, so the three types of tests will be run separately, and most importantly, in the right order. For example, we cannot run integration tests before our project has been deployed.
