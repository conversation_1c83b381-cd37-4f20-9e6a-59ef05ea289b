---
id: changelog
title: Webiny 5.35.1 Changelog
description: See what's new in Webiny version 5.35.1.
---

<Alert type={"info"}>

For the full list of changes, check out the [Webiny 5.35.1](https://github.com/webiny/webiny-js/releases/tag/v5.35.1)
release on GitHub.

</Alert>

import pbEditorEditingValues from "./assets/pb-editor-editing-values.png";

## Security

### Advanced Content Organization (ACO) - Critical Security Fix ([#3296](https://github.com/webiny/webiny-js/pull/3296))

With the [5.35.0](/docs/release-notes/5.35.0/changelog) release, we've introduced the [Advanced Content Organization (ACO)](/docs/release-notes/5.35.0/changelog#introducing-advanced-content-organization-for-page-builder) for Page Builder.

Apart from the new frontend user interface, this new feature also introduced multiple new GraphQL queries and mutations on the backend GraphQL API. Unfortunately, prior to release, these operations were unintentionally left accessible and open to non-authenticated users.

This is no longer the case. From this release, all ACO-related GraphQL queries and mutations are inaccessible to non-authenticated users.

<Alert type="danger">

Because having the mentioned GraphQL operations open to non-authenticated users might pose a data security risk to your organization, we strongly advise everybody to [upgrade to 5.35.1](/docs/release-notes/5.35.1/upgrade-guide) as soon as possible.

</Alert>

## Page Builder

### Editing Template Variables In Page Editor Now Works as Expected ([#3286](https://github.com/webiny/webiny-js/pull/3286))

Previously, when creating a page from a page template, editing values of template variables in the page editor would not work as expected. This has now been addressed.

<Image src={pbEditorEditingValues} title={"Editing Template Variables in Page Editor"} />

### Inconsistent Template Content in Template Editor ([#3286](https://github.com/webiny/webiny-js/pull/3286))

Because of an issue in the related backend GraphQL API query, after doing a full page refresh, Page Builder's template layout editor would intermittently fail to show the latest template content.

This has now been addressed.

## File Manager

### Compare File Extensions in a Case Insensitive Manner ([#3278](https://github.com/webiny/webiny-js/pull/3293))

When uploading files, Webiny checks if the content type matches the extension in the file name, and if not, appends a proper extension, using a mime type catalog. There was a bug with extension checks, where upper case extensions were not matched properly, which resulted in extension duplication. This is now fixed, and extension checks work correctly.

## Development

### Lock User Project Dependencies ([#3301](https://github.com/webiny/webiny-js/pull/3301))

From now on, all dependencies in user's project will be locked to a fixed version, which corresponds to a version that we tested in our CI. This will reduce the risk of some 3rd party dependency affecting the entire project, and breaking the build. There's still a possibility of a transient dependency affecting the project, but that's completely outside our control. We also considered shipping the entire `yarn.lock` file, but that's a very challenging approach, so to have at least a basic safety net, for now, we're locking the dependencies.

### Fix `onKeyDown` Callback Dependencies ([#3302](https://github.com/webiny/webiny-js/pull/3302))

There was a bug with the Input component which would run the outdated callback on `onEnter`. This only manifested itself in the Headless CMS UI, when using multiple text inputs. Hitting `Enter` would cause weird value updates to happen. This is now fixed.

## Deployments

### Website's Lambda@Edge Function Is No Longer Destroyed Automatically ([#3278](https://github.com/webiny/webiny-js/pull/3278))

When Webiny's [multi-tenancy](/docs/enterprise/multi-tenancy) feature is enabled, as part of the **Website** project application, Webiny deploys an additional [Lambda@Edge](https://aws.amazon.com/lambda/edge/) function, which is used for additional multi-tenancy-related routing purposes.

From this release, this function is no longer automatically destroyed when destroying the project application. It must be destroyed manually, either via the [AWS Console](https://aws.amazon.com/console/), or via the [AWS CLI](https://aws.amazon.com/cli/).

This is because of an AWS limitation, which does not allow the immediate deletion of Lambda@Edge functions. Trying to immediately delete the function will result in an error, which is why Webiny does not attempt to delete it automatically.

<Alert type={"info"}>

Note that Lambda@Edge functions are always deployed into `us-east-1` region. Make sure it is selected in the AWS
Console, or via the AWS CLI, before proceeding with the deletion.

</Alert>

<Alert type={"info"}>

Check the [Deleting Lambda@Edge functions and
replicas](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/lambda-edge-delete-replicas.html) article
for more information on how to delete Lambda@Edge functions.

</Alert>

## Documentation

### Updated Page Builder Theming Documentation

The [Page Builder Theming](/docs/page-builder/theming/introduction) section of articles has been completely refreshed and now contains up-to-date information on how to create and use custom themes with websites created with Webiny's Page Builder application.
