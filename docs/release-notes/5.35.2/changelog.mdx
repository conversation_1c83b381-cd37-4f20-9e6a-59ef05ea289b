---
id: changelog
title: Webiny 5.35.2 Changelog
description: See what's new in Webiny version 5.35.2.
---

<GithubRelease version={"5.35.2"} />

## Page Builder

### Add Missing GSI1 Index Definition ([#3313](https://github.com/webiny/webiny-js/pull/3313))

There was a bug specific to DynamoDB + Elasticsearch project setup, which was breaking the `listPageTemplates` query and method calls, because of a missing DynamoDB table index definition. This patch addresses the issue, and listing of page templates now works as expected on Elasticsearch project setups.
