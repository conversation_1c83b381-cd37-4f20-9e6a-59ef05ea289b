---
id: changelog
title: Webiny 5.34.1 Changelog
description: See what's new in Webiny version 5.34.1.
---

## Page Builder

### Page Elements - Minor Fixes ([#2980](https://github.com/webiny/webiny-js/pull/2980), [#2981](https://github.com/webiny/webiny-js/pull/2981))

In case you missed it, with 5.34.0 release, we've released [Page Elements](/docs/release-notes/5.34.0/changelog#page-elements-a-brand-new-page-rendering-engine-2898), a brand new page rendering engine.

But, as it usually goes, no release of a new feature goes without a hiccup or two. So, with this release, we've included a number of smaller fixes that were reported to us by first users that gave the new rendering engine a try.

### Fixed an Issue With Page Rendering In the Admin Application ([#2985](https://github.com/webiny/webiny-js/pull/2985))

In some cases, when entering the Admin application and navigating to the list of pages, the preview of pages would not work (usually, users would only see a blank screen).

With this release, this has been addressed.

### Forms Are Now Rendered Correctly on Published Pages ([#2982](https://github.com/webiny/webiny-js/pull/2982))

We've improved rendering of forms (created with the Form Builder application) on published pages.

From now on, no loading states nor network calls will be present before the form is rendered. Forms on published pages are now displayed immediately to the user, which ultimately improves the user experience.

### Instruct Prerendering Service To Cache GraphQL HTTP API Responses with the New `@ps` GraphQL Directive ([#2982](https://github.com/webiny/webiny-js/pull/2982))

With this release, we've introduced the new `@ps` GraphQL directive.

```diff-graphql
+ query FbGetPublishedForm($revision: ID, $parent: ID) @ps(cache: true) {
        formBuilder {
            getPublishedForm(revision: $revision, parent: $parent) {
                data {
                    // Fields removed from brevity.
                }
            }
        }
    }
```

This new directive is useful when issuing GraphQL API queries within custom Page Builder page elements.

When present in a GraphQL query and its `cache` parameter is set to `true`, during the page prerendering process, the prerendering service will be instructed to cache the received GraphQL API response in the actual page HTML. Ultimately, this ensures no network calls are issued when the page is actually served to real users, which provides faster page load time and overall better user experience.

## Website

### Use Webiny React Application Framework for the Website App ([#2983](https://github.com/webiny/webiny-js/pull/2983))

[Webiny React application framework](/docs/admin-area/basics/framework) is something that's been powering our Admin app for the past year. It allowed us to register plugins as regular React components, by simply dropping them inside the `<Admin/>` element. With this release, we've finally refactored the internals of the Website app, to utilize this same application framework. With that, Website now works in the same manner as the Admin app, it allows developers to hook into the main Website components, add custom React Context providers, etc.

However, the Website app has a different requirement comparing to the Admin app, and that is - **performance**. To optimize for rendering in the browser, and avoid visual glitches and unnecessary component remounts, we've also exposed the `providers` prop on the `<Website>` component. This way, we still control the internals, and your project is free of the boilerplate code, but you have the ability to add custom context providers, which can then feed your Page Builder element plugins, etc.

```tsx apps/website/src/App.tsx
/* Create a custom Higher Order Component */
const MyProvider = Component => {
  return function MyProvider({ children }) {
    /* Add your logic here! */
    return <Component>{children}</Component>;
  };
};

export const App = () => {
  /* Pass your HOC to the Website app! */
  return <Website providers={[MyProvider]} />;
};
```

## Development

### Webiny CLI - Run Commands Against Multiple Apps ([#2974](https://github.com/webiny/webiny-js/pull/2974))

Users can now execute Webiny CLI commands against multiple apps in a single command execution.

This is achieved by supplying names of different apps, separated by comma (**,**) character. For example, the following command deploys **API** and **Admin** apps into the **dev** environment:

```bash
yarn webiny deploy api,admin --env dev
```

This is more convenient than having to type two essentially the same commands, where only the name of the project application is different:

```bash
yarn webiny deploy api --env dev && yarn webiny deploy admin --env dev
```

Note that the commands are run in series. Meaning, in the above example, the `webiny deploy` command will be first run against the **API** project application. Once it's done, then the command will be run against the **Admin** project application. Also, this new feature works will all commands, except the [`webiny watch`](/docs/{exact:5.34.x}/core-development-concepts/basics/watch-command) command. So, that's:

- [`webiny deploy`](/docs/{exact:5.34.x}/core-development-concepts/basics/project-deployment)
- [`webiny destroy`](/docs/{exact:5.34.x}/infrastructure/basics/destroy-cloud-infrastructure)
- [`webiny output`](/docs/{exact:5.34.x}/core-development-concepts/basics/webiny-cli#common-commands)
- [`webiny pulumi`](/docs/{exact:5.34.x}/core-development-concepts/basics/webiny-cli#yarn-webiny-pulumi-folder---env-env----command)

### Extend GraphQL API Scaffold - Addressed Code Generation Issue ([#2984](https://github.com/webiny/webiny-js/pull/2984))

Prior to this release, in some cases, the [Extend GraphQL API](/docs/{exact:5.34.x}/core-development-concepts/scaffolding/extend-graphql-api), and, subsequently, the [Extend Admin Area](/docs/{exact:5.34.x}/admin-area/new-app-tutorial/scaffolding) scaffolds, would end up generating faulty application code. More specifically, the generated GraphQL schemas would be missing some of the required GraphQL fields, for example `createdBy`.

This has now been addressed. The generated application code should now work without additional manual intervention from the user.

## Headless CMS

### Customizable Fields Sorting ([#2986](https://github.com/webiny/webiny-js/pull/2986))

Users can now add custom sort options into the Schema for user defined CMS Models.
The support for custom sorting, we also added the plugins for both DynamoDB and DynamoDB + Elasticsearch systems.

To find out more about custom sorting, please read this [article](/docs/headless-cms/extending/custom-sorting).

### Control Whether a Referenced Field Value Is Populated ([#2987](https://github.com/webiny/webiny-js/pull/2987))

Users can now choose if they want to populate a referenced field value in the Read API.

```graphql
query {
  listArticles {
    data {
      author(populate: false) {
        id
        entryId
        modelId
      }
      category {
        # no need for (populate: true)
        id
        title
      }
    }
  }
}
```

The default behavior is to populate the field.

## I18N

### Fix a tenantId Variable Being Kept After Tenant Change in a Single Request ([#2960](https://github.com/webiny/webiny-js/pull/2960))

When user changed a tenant in the single request, via the code, a locale crud was still using the previous tenantId.
