---
id: 809a794d
title: Webiny 5.38.4 Changelog
description: See what's new in Webiny version 5.38.4.
---

<GithubRelease version={"5.38.4"} />

## Admin Area

### Non-Full-Access Users Can Now Successfully Open the Admin App 

We've addressed an issue where a non-full-access user would not be able to open the Admin app. 

More specifically, if a non-full-access user did not have access to the Admin Users module (granted via a security role or a team), the underlying GraphQL query that pulls the current admin user data would fail. This GraphQL query is issued immediately upon initial load of the Admin Area, and the failure would cause the Admin Area to not load at all.

With this release, this should no longer be an issue.

