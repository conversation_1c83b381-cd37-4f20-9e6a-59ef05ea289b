---
id: ee31cdab
title: Webiny 5.40.1 Changelog
description: See what's new in Webiny version 5.40.1.
---

import otherAdjustColumnWidth from "./assets/other-adjust-column-width.gif";

<GithubRelease version={"5.40.1"} />

## Headless CMS

### Published Records Updated Correctly Upon Calling `createRevisionFrom` ([#4180](https://github.com/webiny/webiny-js/pull/4180))

Prior to this release, when immediately publishing a revision created using the `createRevisionFrom` method, the revision's internal "published" record would not be updated correctly. This would cause the old published revisions to still be returned when performing queries.

This issue has been fixed, and now the published record is updated correctly when calling the `createRevisionFrom` method.

## File Manager

### Do Not Enlarge Images Beyond The Original Size ([#4192](https://github.com/webiny/webiny-js/pull/4192))

When requesting images from our Asset Delivery API, you can specify the desired width of the image to be returned. Until this release, we would simply take your request and resize the image to the requested size. This sometimes caused issues with smaller images being enlarged beyond their original resolution, which caused all kinds of side effects (from blurry image, to image drastically growing in file size).

With this release, we make sure that the image is never enlarged, no matter the requested width.

## Other Improvements

### Webiny Telemetry Service (WTS) - Using NPM Package Instead of a GitHub Reference ([#4183](https://github.com/webiny/webiny-js/pull/4183))

We've updated the way the internal [Webiny Telemetry Service (WTS)](https://github.com/webiny/wts) client is included in a Webiny project. Instead of referencing the client directly from GitHub, we now use the NPM package.

We've made this change because we've seen a couple of users bumping into issues when upgrading their projects. Basically, the client would not be downloaded correctly from GitHub, causing the project to fail to build.

This way, the package will be installed from the NPM registry, which should make the upgrade process smoother.

### Allow editors to adjust the column width within the ACO list ([#4179](https://github.com/webiny/webiny-js/pull/4179))

Prior to this release, editors were unable to adjust the column width, which could be problematic in cases of complex folder structures with multiple levels of nesting.

This has been resolved: editors can now adjust the column width based on their needs. The configuration is saved within the browser's `localStorage`, ensuring that the system remembers latest column width settings for a more personalized and consistent user experience.

<Image src={otherAdjustColumnWidth} title={"Adjusting column widths for a customized view."} />

### Lexical Editor Improvements ([#4190](https://github.com/webiny/webiny-js/pull/4190))

Lexical editor has received some attention, and we've fixed several annoying bugs related to text formatting. Previously, changes of color, typography, font size, etc. would cause the previous formatting to be reset to defaults. This is no longer the case, and you can now correctly apply formatting to text selections, including to links.

Another area of improvement is the color picker tool. We've improved the looks of the widget, and also fixed a few bugs with color selection, added a color indicated to the toolbar, added a marker for active theme color, etc.

<Alert type="warning" title="Lexical Packages Upgraded to 0.16.0!">

Please note that we've upgraded the Lexical library packages to the latest version (0.16.0)! If you're using Lexical packages directly in your project as dependencies, you'll need to upgrade them as well.

</Alert>
