---
id: 0ea53f89
title: Webiny 5.43.2 Changelog
description: See what's new in Webiny version 5.43.2
---

import folderDropConfirmation from "./assets/hcms-folder-drop-confirmation.gif";

<GithubRelease version={"5.43.2"} />

## Advanced Content Organization (ACO)

### Confirmation Dialog for Folder Drag & Drop ([#4662](https://github.com/webiny/webiny-js/pull/4662))

A new **confirmation dialog** has been introduced when users move folders via drag-and-drop. 

By enabling the new `DropConfirmation` configuration under the `Browser.Folder` namespace, users will now be prompted to confirm the action, helping prevent accidental folder moves. 

This feature is available in the [CMS](/docs/headless-cms/extending/confirmation-dialog-for-folder-drag-and-drop), [File Manager](/docs/file-manager/extending/confirmation-dialog-for-folder-drag-and-drop) and [Page Builder](/docs/page-builder/extending/confirmation-dialog-for-folder-drag-and-drop) apps. 

By default, the confirmation is disabled (`false`) and can be turned on by setting `DropConfirmation` to `true` in the relevant app config.

<Image src={folderDropConfirmation} title={"Confirm folder move: prompt appears before completing drag-and-drop."} />

## Folder Level Permissions (FLP)

### Fixed Permission Check on Folder Move ([#4662](https://github.com/webiny/webiny-js/pull/4662))

We've fixed a bug that allowed users to move folders into locations they didn’t have write access to. 

Now, when attempting to drag and drop a folder, the system will check whether the user has sufficient permissions on the target folder. 

If the user has only viewer-level access, the move will be blocked, ensuring proper permission enforcement.

