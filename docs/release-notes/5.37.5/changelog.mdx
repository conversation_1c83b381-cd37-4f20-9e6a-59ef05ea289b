---
id: changelog
title: Webiny 5.37.5 Changelog
description: See what's new in Webiny version 5.37.5.
---

import cloneModelIcon from "./assets/clone-model-icon.png";

<GithubRelease version={"5.37.5"} />

## Page Builder

### Public Website Rerendering ([#3570](https://github.com/webiny/webiny-js/pull/3570))

Prior to this release, the public website would not fully refresh (re-render) after a successful deployment of the **Website** project application. This was happening due to an issue in the underlying website prerendering service.

This issue has been addressed. Now, after a successful deployment, the public website will be fully refreshed.

### Addressed A Block Export/Import Issue ([#3552](https://github.com/webiny/webiny-js/pull/3552))

We've addressed an issue where importing exported page blocks would fail if a block's category was not present in the target system. We've been dealing with this issue in the past, but, as we've found out, it was not fully addressed.

## Headless CMS

### Clone Content Model Button Now Hidden For Users Without Required Permissions ([#3553](https://github.com/webiny/webiny-js/pull/3553))

We've addressed an issue where users without the required security permissions were still able to see the **Clone Content Model** button in the list of all content models.

This button is now hidden for users without the required permissions.

<Image
  src={cloneModelIcon}
  title={"Clone Content Model Button Now Hidden For Users Without Required Permission"}
/>

### Fixed "Name" Column Sorting ([#3554](https://github.com/webiny/webiny-js/pull/3554))

We've addressed an issue with sorting the "Name" column in the Headless CMS list view. Previously, the sorting function would not work correctly if the model definition was missing a field named "title".

Now, we dynamically associate the "Name" column with the field you picked as the title for the content model, so sorting will function as expected regardless of the field name.

## File Manager

### Fixed Listing Issue Beyond Second Pagination ([#3544](https://github.com/webiny/webiny-js/pull/3544))

We have addressed the file manager listing issue that occurred after the second pagination, which previously prevented listing more than 100 files.
