---
id: c46ec70b
title: Webiny 5.39.6 Changelog
description: See what's new in Webiny version 5.39.6.
---

import { GithubRelease } from "@/components/GithubRelease";

<GithubRelease version={"5.39.6"} />

## Headless CMS

### Improving the 5.39.0 Meta Fields Data Migration ([#4115](https://github.com/webiny/webiny-js/pull/4115))

With this release, we've made some improvements to the 5.39.0 data migration script, the one related to the [new CMS entry meta fields](/docs/release-notes/5.39.0/changelog#headless-cms-revamped-date-time-and-identity-on-by-meta-fields) migration.

In order to make the data migration more reliable, we've added an additional check in the migration script, that ensures all of the meta fields were assigned, especially when it comes to values of non-nullable meta fields.

Note that, because of easier maintenance and, ultimately, better performance when performing the migration (less Amazon DynamoDB table scanning), we've decided to retire the existing `5.39.0-001` and `5.39.2-001` data migrations, and consolidate all of the checks and processing in the new `5.39.6-001`.

So, from version 5.39.6 and grater, no matter if `5.39.0-001` and `5.39.2-001` data migrations were previously executed or not, they won't be executed anymore. Only the new `5.39.6-001` will be executed, and data processing will only happen in case there is actually something to process (which should not be the case if the first two migrations were already executed).  
