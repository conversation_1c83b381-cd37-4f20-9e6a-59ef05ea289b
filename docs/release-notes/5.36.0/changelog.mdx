---
id: changelog
title: Webiny 5.36.0 Changelog
description: See what's new in Webiny version 5.36.0.
---

import fmAcoOverview from "./assets/fm-aco-overview.jpg";
import peButtonIconPos from "./assets/pe-button-icon-pos.png";

<GithubRelease version={"5.36.0"} />

## File Manager

### Introducing Advanced Content Organization for File Manager ([#3312](https://github.com/webiny/webiny-js/pull/3312))

The Advanced Content Organization (ACO) for File Manager is a new feature that provides users with the ability to organize content in an effective, easy-to-manage way. The ACO allows users to add files in separate folders and rearrange content as needed, making it easier to find what they are looking for.

<Image src={fmAcoOverview} title={"Advanced Content Organization for File Manager Overview"} />

**With the new File Manager users can:**

1. Create and update a multi-level folder structure.
2. Switch to "Table" mode, having a nice overview on the most important file data, such as "Name", "Type", "Size", "Last modified" and "Author".
3. Sort both folders and files based on "Name" and "Last Modified". This feature makes it easier for users to find specific files and folders quickly.
4. Move a files across the hierarchy tree, and it has no impact on the file URL. The two have been decoupled to ensure users have the flexibility they need without having a negative impact on SEO if they decide to restructure their folder/file organization.

### Introducing multipart upload ([#3232](https://github.com/webiny/webiny-js/pull/3232))

We have significantly improved our file uploading capabilities, enabling support for files up to a whopping 5TB in size. This has been made possible through the implementation of multipart upload and file chunking techniques, ensuring efficient and reliable transfers of large files.
Additionally, this update includes a progress bar widget, providing users with real-time feedback on lengthy operations, ensuring a more interactive and engaging experience.

### Improve extension handling allowing uppercase file extensions ([#3294](https://github.com/webiny/webiny-js/pull/3294))

With this release, we've fixed an issue related to file uploads with upper case file extensions. Prior to this release, when uploading files with upper case file extensions, an extra file extension would be appended to the file name based on the detected mime type. With this fix, files with upper case file extensions can now be uploaded without any issues, ensuring better compatibility and allows for a smoother file upload experience.

## Page Builder

### Fix import pages process and page list refresh ([#3318](https://github.com/webiny/webiny-js/pull/3318))

With this release, we've fixed an issue related to import pages process introduced by 5.35.0 version. After importing pages into Page Builder, the list wasn't showing the newly created page. This behavior has been fixed, now the page list refresh correctly.

### Button Page Element - Icon Position CSS Class ([#3317](https://github.com/webiny/webiny-js/pull/3317))

The `div` element that represents the **Button** page element's icon now also includes an additional CSS class that indicates icon's position. With the base `button-icon` CSS class, the element will now also include one of the following classes: `button-icon-left`, `button-icon-right`, `button-icon-top`, `button-icon-bottom`.

This enables developers to apply different CSS rules based on the position of the icon.

<Image src={peButtonIconPos} title={"Button Page Element - Added Icon Position CSS Class"} />

## Deployments

### Configure a Shared ElasticSearch Domain via `webiny.application.ts` ([#3308](https://github.com/webiny/webiny-js/pull/3308))

Prior to this release, setting up a shared ElasticSearch (OpenSearch) domain was achieved via a pair of environment variables, which showed not to be the most convenient way of doing it. So, with this release, we've introduced the ability to configure a shared ElasticSearch domain via two `webiny.application.ts` files. To learn more, please refer to the updated [Modify Cloud Infrastructure](/docs/{exact:5.36.x}/infrastructure/basics/modify-cloud-infrastructure#using-a-shared-amazon-elasticsearch-open-search-domain) article.
