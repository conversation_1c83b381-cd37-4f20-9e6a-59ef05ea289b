---
id: aafea9a2
title: Upgrade from 5.35.x to 5.36.0
description: Learn how to upgrade Webiny from 5.35.x to 5.36.0.
---

import { <PERSON><PERSON> } from "@/components/Alert";

import fmSettings from "./assets/fm-settings.png";

<Alert type={"danger"}>

Before upgrading to `5.36.0`, please ensure your Webiny project is using the [new project
setup](/docs/release-notes/5.29.0/changelog#brand-new-project-setup), introduced with Webiny
[5.29.0](/docs/release-notes/5.29.0/changelog). In case you did not already migrate, please visit the [migration
guide](/docs/release-notes/5.29.0/project-setup-migration).

</Alert>

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.35.x to 5.36.0

</Alert>

<Alert type="info">

Make sure to check out the [5.36.0 changelog](/docs/release-notes/5.36.0/changelog) to get familiar with the changes introduced in this
release.

</Alert>

## Step-by-Step Guide

The following steps will guide you through the upgrade process. Please note that this guide assumes you are using the [new project setup](/docs/release-notes/5.29.0/changelog#brand-new-project-setup), introduced with Webiny [5.29.0](/docs/release-notes/5.29.0/changelog). In case you did not already migrate, please visit the [migration guide](/docs/release-notes/5.29.0/project-setup-migration).

### 1. Upgrade Webiny Packages

Upgrade all `@webiny/*` packages by running the following command:

```bash
yarn up "@webiny/*@5.36.0"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return `5.36.0`.

### 2. Run the Upgrade Command

The next step is to run the project upgrade:

```bash
yarn webiny upgrade
```

### 3. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

### Additional Notes

With this release, we significantly improved our file uploading capabilities, enabling support for files up to a whopping 5TB in size.
Remember to review the default **Maximum file upload size** limit you find within the File Manager Settings page (**Main Navigation -> Settings -> File Manager**).

<Image src={fmSettings} title={"File Manager Settings - Maximum file upload size"} />

<Alert type="warning">

Make sure you deploy the entire system using the command shown above! You have to deploy all apps before using the
system. Partial deploys may cause the upgrade to be applied incorrectly.

</Alert>

<Alert type="warning">

As stated in the [Upgrade Webiny](/docs/release-notes/upgrade-webiny#pre-production-environments-first) section, we recommend that you
first deploy your changes into one of your pre-production environments, like `dev` or `staging`.

</Alert>

<Alert type="info">

Learn more about different deployment environments in the [CI/CD /
Environments](/docs/core-development-concepts/ci-cd/environments) key topic.

</Alert>
