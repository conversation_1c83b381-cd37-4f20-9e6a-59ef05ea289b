---
id: changelog
title: Webiny 5.37.4 Changelog
description: See what's new in Webiny version 5.37.4.
---

import acoRecordsBug from "./assets/aco-records-bug.mp4";
import fmConfiguration from "./assets/fm-configuration.mp4";
import booleanDefaultValue from "./assets/boolean-default-value.png";

<GithubRelease version={"5.37.4"} />

## Page Builder

### Fixed Deletion and Unpublishing of Pages (Multi-Tenant Projects) ([#3528](https://github.com/webiny/webiny-js/pull/3528))

Prior to this release, in multi-tenant projects, when deleting or unpublishing a page, the page would not be fully deleted. More specifically, despite the fact that the page would not be visible in the Admin app, it would still be visible on the public website.

This has now been addressed. When deleting a page, it will be fully deleted, meaning the page will no longer be visible in the Admin app nor on the public website.

### Use the Right Tenant in ACO When Switching Tenants Programmatically ([#3538](https://github.com/webiny/webiny-js/pull/3538))

When using the system programmatically, in multi-tenant systems, creating Pages via code would result in Page links to be stored in the tenant that initiated the API call. If you initiated the request, for example, as a `root` tenant, subsequent switching of tenants or locales would not get propagated to the storage model, resulting in search records being stored in the wrong tenant. From the user's perspective, pages would be "lost", while in reality, pages themselves were correctly stored in the corresponding tenant. It's the link to the page that was pointing to the wrong tenant.

This is now resolved, and the underlying storage model is correctly applying the current tenant and locale.

## Advanced Content Organization

### Show All Folders in the Folder Tree Component ([#3534](https://github.com/webiny/webiny-js/pull/3534))

There was a bug on the client side, where the `<FolderTree/>` component was only reading 50 folders from the GraphQL API. If you had more than 50 folders in the system, those folders were simply not being displayed. This simple yet very annoying bug is now fixed.

### Reset Table Data on Empty Response from the API ([#3529](https://github.com/webiny/webiny-js/pull/3529))

We've fixed a little bug with data not being correctly reset when the API returned an empty response. The following video displays the bug. This is now resolved, and data is correctly reset on empty API responses.

<Video src={acoRecordsBug} controls={true} />

## Headless CMS

### Escape Slashes in Elasticsearch Queries ([#3515](https://github.com/webiny/webiny-js/pull/3515))

When using slashes in search queries with Elasticsearch, those slashes were incorrectly escaped, causing parsing errors. This is now fixed and slashes are safe to use in search queries. For full details on the issues, please visit [this PR](https://github.com/webiny/webiny-js/pull/3515) on Github.

### Ensure \_templateId Exists on Create/Update Mutation Return Value ([#3532](https://github.com/webiny/webiny-js/pull/3532))

In 5.37.1, we've introduced a regression on Dynamic Zone handling, specifically for create/update mutations on content entries, **when triggered from the client side (React)**. Only these 2 mutations were affected, and their field selection was breaking the GraphQL response. The data, however, was all correctly stored to the database, and all subsequent data reading was returning correct data.

This bug is now fixed, and we've added proper tests to our API packages.

Shout out to our community member, **Omar Ebaid**, for reporting the issue! ❤️

### Boolean Field Default Value ([#3526](https://github.com/webiny/webiny-js/pull/3526))

We've improved handling of boolean field default value, by replacing the dropdown component with radio buttons. This fixes the issue of handling `false` values. The dropdown component we used previously, didn't play well with `false` as an option value, which in turn caused various weird glitches.

<Image src={booleanDefaultValue} title={"Boolean Field Default Value"} />

## File Manager

### Expose Configuration for Basic Fields and Tags Widget ([#3525](https://github.com/webiny/webiny-js/pull/3525))

We've added some new configuration components to the `<FileManagerConfig>`, which allow you to show/hide the `Filter by tag` widget in the browser sidebar, as well as control each of the basic fields in the File Details drawer.

<Video src={fmConfiguration} controls={true} />

### Add Missing Headless CMS Types to the Main GraphQL Schema ([#3519](https://github.com/webiny/webiny-js/pull/3519))

We've fixed an issue with File Manager field extensions which was caused by missing Headless CMS GraphQL type scalars. When adding a `ref` field to your File extensions, the generated schema needs a `RefField` and `RefFieldInput` to work. These types are specific to Headless CMS, and were not present in the main GraphQL schema.

### Process Extension Fields Data Using CMS Field Plugins ([#3518](https://github.com/webiny/webiny-js/pull/3518))

Some Headless CMS field plugins convert data before it is being sent from the client to the GraphQL API (for example, the `datetime` field). Since File Manager extensions are essentially Headless CMS fields, we also need to process that data before File Details form is sent to the API. This processing is now added, and all Headless CMS fields should now behave correctly when used in File extensions.
