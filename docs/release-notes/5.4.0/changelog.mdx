---
id: aafea9f0
title: Webiny 5.4.0 Changelog
description: See what's new in Webiny version 5.4.0.
---

import { Alert } from "@/components/Alert";

# Changes

This document highlights the most important fixes, improvements, and features, that were introduced in Webiny `5.4.0`.

<Alert type="info" title="How To Upgrade?">

Please check the [Webiny 5.4.0 migration guide](/docs/release-notes/5.4.0/upgrade-guide) for the upgrade steps.

</Alert>

## Headless CMS

### Reference Field - Added Item Reordering Control ([#1555](https://github.com/webiny/webiny-js/pull/1555))

The reference field can be used to reference one or more published content entries. But, up until now, once multiple entries were picked, there wasn't a way to reorder them.

This is now possible with the new reorder button (<svg style={{ verticalAlign: "middle"}} xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><path d="M0 0h24v24H0z" fill="none"/><path d="M3 15h18v-2H3v2zm0 4h18v-2H3v2zm0-8h18V9H3v2zm0-6v2h18V5H3z"/></svg>), located on the left side of each item in the list of all entries. Once clicked, a simple number input will be shown, into which a new order number of the active entry is typed. To complete the reordering, you can either press **Enter** to confirm the new order, or **Escape** to cancel.

![Reference Field - Added Item Reordering Control](./reorder-ui.gif)

### Fixed Data Filtering Issue on the Headless CMS GraphQL API

In a couple of different scenarios, performing data filtering on boolean and number fields would return incorrect results. This has now been resolved, so, once you upgrade, you should be able to perform queries using these field types.

<Alert type="info" title="Headless CMS GraphQL API">

Learn more about the Headless CMS GraphQL API in the [Headless CMS GraphQL API](/docs/headless-cms/basics/graphql-api) key topic.

</Alert>

## Other

### Improved Deployment Process for the Website Project Application ([#1557](https://github.com/webiny/webiny-js/pull/1557))

We are introducing a couple of improvements to the way we deploy the **Website** project application.

#### No More Broken Pre-rendered Pages

Prior to this release, deploying a new production build of your **Website** React application would replace all static assets (JS, CSS, images) that were uploaded as part of the previous deployment. This means that all of the pre-rendered pages would immediately become broken, as static assets, to which they were linking to, don't exist anymore.

With this release, all of the static assets that you upload as part of the **Website** deployment process won't have any affect on the previously uploaded files, meaning, all of the pre-rendered pages will still work correctly.

<Alert type="info">

Learn more about the **Website** project application on the cloud infrastructure level in the [Cloud Infrastructure](/docs/architecture/website/overview) key topics section.

</Alert>

#### Re-rendering the Complete Website on New Deployments

Another issue we address in this release is having stale pages (old code) - even after a new deployment of the **Website** project application was just performed.

The reason behind this is that, although a new production build has just been uploaded, nothing actually happened with all of the previously pre-rendered pages. So, for example, if we made a small change to the way our website's main menu behaves, we wouldn't be able to see the change on any page, until something triggered its re-render (for example - publishing a new page revision).

So, from now on, after the **Website** project application's React application has been successfully deployed, a separate background process will be triggered, that will ensure all of the existing pages are re-rendered.

Once successfully deployed, please note that it can take a couple of minutes for the website to be fully updated. This is mainly because of the [Amazon Cloudfront](https://aws.amazon.com/cloudfront/) and its caching settings.
