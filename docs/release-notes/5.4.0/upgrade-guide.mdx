---
id: aafea9f1
title: Upgrade from 5.3.0 to 5.4.0
description: Learn how to upgrade Webiny from 5.3.0 to 5.4.0.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.3.0 to 5.4.0

</Alert>

<Alert type="danger">

Before continuing, make sure to take the necessary precautions, listed in the [Overview](/docs/release-notes/upgrade-webiny#-precaution-measures) section.

</Alert>

<Alert type="info">

Make sure to check out the [5.4.0 changelog](/docs/release-notes/5.4.0/changelog) to get familiar with all of the changes introduced with this release.

</Alert>

## Upgrade Webiny Packages

The first step is to upgrade all `@webiny/*` packages, which can be done by running the following command in the root of your project:

```bash
# Execute in your project root.
yarn up "@webiny/*@5.4.0"
```

## Update Website Project Application

This release includes a couple of smaller changes in the **API** (`api/pulumi`) and **Website** (`apps/website/pulumi`) project applications' cloud infrastructure code.

### 1. Adjust Cloud Infrastructure Settings (API Project Application)

In your [`api/pulumi/prod/index.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/api/pulumi/prod/index.ts#L106-L107) and [`api/pulumi/dev/index.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/api/pulumi/dev/index.ts#L106-L107) files, at the end of the file, add the following two lines:

```diff-js api/pulumi/(dev|prod)/index.ts:99
(...)
    return {
        region: process.env.AWS_REGION,
        apiUrl: cloudfront.cloudfront.domainName.apply(value => `https://${value}`),
        cognitoUserPoolId: cognito.userPool.id,
        cognitoAppClientId: cognito.userPoolClient.id,
        updatePbSettingsFunction: pageBuilder.functions.updateSettings.arn,
-       psQueueAdd: prerenderingService.functions.queue.add.arn,
-       psQueueProcess: prerenderingService.functions.queue.process.arn
    };
};
```

This will expose the [ARNs](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html) of Lambda functions that are part of the Prerendering Service, enabling us to execute them upon deploying the **Website** project application (happen automatically).

### 2. Adjust Cloud Infrastructure Settings (Website Project Application)

#### Adjust Delivery CloudFront Distribution's `pathPattern` Property

Open your [`apps/website/pulumi/delivery.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/apps/website/pulumi/delivery.ts#L53) and replace the `pathPattern: "/static/*"` with `pathPattern: "/static-*"`:

```diff-js apps/website/pulumi/delivery.ts
(...)
    orderedCacheBehaviors: [
        {
            compress: true,
            allowedMethods: ["GET", "HEAD", "OPTIONS"],
            cachedMethods: ["GET", "HEAD", "OPTIONS"],
            forwardedValues: {
                cookies: {
                    forward: "none"
                },
                headers: [],
                queryString: false
            },
-            pathPattern: "/static-*",
            viewerProtocolPolicy: "allow-all",
            targetOriginId: appS3Bucket.arn,
            // MinTTL <= DefaultTTL <= MaxTTL
            minTtl: 0,
            defaultTtl: 2592000, // 30 days
            maxTtl: 2592000
        }
    ]
(...)
```

This change is here because, from this release, upon generating a production builds of React applications in your Webiny project, all static assets (JS, CSS, images) will be generated in the `static-{RANDOM_ID}` folder (previously `static`). This is mainly for better caching purposes.

<Alert type="info">

As it was always the case, upon deploying **Website** and **Admin Area** project applications, all previously uploaded React production build files are removed. So, once the deployment is complete, in your Amazon S3 buckets, expect to have the `static` folder removed, and a new `static-{RANDOM_ID}` to be created.

</Alert>

<Alert type="info">

Due to website prerendering improvements that the 5.4.0 release brings, in the Amazon S3 bucket that's deployed as part of the **Website** project application, expect to have multiple `static-{RANDOM_ID}` folders - one for each deploy. If you're deploying frequently and you end up having a lot of folders, you might want to schedule a cleanup task, that will delete folders that are no longer in use.

Check out the [Cloud Infrastructure](/docs/architecture/website/prerendering-pages) key topics section to learn more about all of the resources included in the process.

</Alert>

#### Remove Static Assets Upload and Set `min`, `default`, and `max` TTLs to Zero

Open your [`apps/website/pulumi/app.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/apps/website/pulumi/app.ts) and remove everything related to the upload of the **Website** project application's React production build files.

Another thing you'll also need to adjust are the [TTL properties](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/apps/website/pulumi/app.ts#L43-L45), which all need to be set to zero.

To make it a bit easier, you can follow the differences that were made in the actual [commit](https://github.com/webiny/webiny-js/commit/4428beb44cf0a3404c72ef3a8b24efcd87a93a01#diff-917fae1f5a3076f89332067ea7ccf503e4a70ca7ed8bff3aff122be3c4685658). Note that if you didn't make any changes to this file, you're free to just copy and paste [the new version](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/apps/website/pulumi/app.ts) of the code.
