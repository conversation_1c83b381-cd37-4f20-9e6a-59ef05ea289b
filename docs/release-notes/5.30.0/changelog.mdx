---
id: changelog
title: Webiny 5.30.0 Changelog
description: See what's new in Webiny version 5.30.0.
---

import reorderObjects from "./assets/reorder-objects.mp4";
import clickToAdd from "./assets/click-to-add.mp4";

## Admin Area

### Upgrade to RMWC v7 ([#2553](https://github.com/webiny/webiny-js/pull/2553))

Webiny uses https://rmwc.io/ as a UI library. For a long time, we were stuck at v5, for various reasons. We're happy to announce that we've finally upgraded to RMWC v7. This will allow us to finally upgrade our React to v17, and maybe even v18, in the upcoming releases.

## Headless CMS

### Enable Object Reordering ([#2542](https://github.com/webiny/webiny-js/pull/2542))

You can now reorder objects in Headless CMS UI, when creating content entries with lists of object fields. Sometimes it can be difficult to track were the record you're moving actually moved, so we also added a highlight to show you the exact position of the object.

<Video src={reorderObjects} />

## Page Builder

### "Click to Add" Elements ([#2534](https://github.com/webiny/webiny-js/pull/2534))

This PR fixes an existing feature that was broken some time during migration to `recoil` state management. The feature allows you to activate a dropzone, and then simply click an element in the sidebar to insert, without using drag&drop.

<Video src={clickToAdd} />

### Extract Editor Core ([#2536](https://github.com/webiny/webiny-js/pull/2536))

In preparation for some new features that we'll announce very soon, we had a need to reuse the core mechanics of our Page Editor and make it more abstract. This PR separates the core editor mechanics from any notion of a page, related events, plugins, and state. It is now possible to use this editor core, and develop custom editors which follow the same drag&drop mechanics, elements, element settings, but give it your own context, state, events, and even UI elements, all by using plugins 😎

Moreover, your custom editors can be mounted on any route you desire, and the "save", "go back", and similar behaviours are now entirely pluginable. If you're building a custom editor and would like to know more, please get in touch with us.

## Development

### New Projects Now Use Typescript 4.7.4 ([#2527](https://github.com/webiny/webiny-js/pull/2527))

We've upgraded Webiny to use Typescript 4.7.4. This change will only apply to the new projects created starting with version 5.30.0. Existing projects remain intact, and will work just fine.

### Amending the Infrastructure (Pulumi) Code Now Works as Expected ([#2516](https://github.com/webiny/webiny-js/pull/2516))

In case you missed it, with the [5.29.0](/docs/release-notes/5.29.0/changelog) release, we've introduced a new concept called [Pulumi Apps](/docs/release-notes/5.29.0/changelog#pulumi-infrastructure-code-replaced-with-pulumi-apps). From the changelog:

> The main purpose of a Pulumi app is to define and encapsulate Pulumi infrastructure code, and, equally important, have it easily shipped as part of every Webiny project, while still maintaining the full ability for the developer to perform further customizations to existing resources or even introduce new ones.

With this release, we've fixed a Pulumi Apps-related bug that would prevent users from amending the default infrastructure (Pulumi) code.

So, from now on, trying to do something like the following now will work as expected:

```ts apps/website/webiny.application.ts
import { createWebsiteApp } from "@webiny/serverless-cms-aws";

export default createWebsiteApp({
  pulumi: app => {
    app.resources.delivery.cloudfront.config.comment(
      "Let's update the distribution's description..."
    );
  }
});
```

### `webiny watch` Command - Redeploying Now Works Correctly ([#2538](https://github.com/webiny/webiny-js/pull/2538))

With the [5.29.0](/docs/release-notes/5.29.0/changelog) release, a regression was introduced to the [`webiny watch`](/docs/core-development-concepts/basics/watch-command) command.

Instead of redeploying the AWS Lambda function code once after a source code change has been applied, the command would redeploy it twice.

This has now been addressed.

### Set Common ENV Variables on all Lambda Functions ([#2540](https://github.com/webiny/webiny-js/pull/2540))

Often, we need to apply custom environment variables to all Lambda functions within a certain app. More often than not, it's the **API** app, containing all the business logic and Lambda functions. This is now possible using the following utility:

```ts apps/api/webiny.application.ts
import { createApiApp } from "@webiny/serverless-cms-aws";

export default createApiApp({
  pulumi(app) {
    app.setCommonLambdaEnvVariables({
      MY_CUSTOM_VAR: "any-value",
      OKTA_ISSUER: "dev-123",
      AUTH0_TENANT: "6352"
    });
  }
});
```

### Hook Into Any Pulumi Resource ([#2540](https://github.com/webiny/webiny-js/pull/2540))

We've added an ability to hook into any resource that is being instantiated as part of the Pulumi app. This allows you to target resources by type, and modify them. Using the `isResourceOfType` utility will give you perfect TS auto-complete, because it works as a type predicate.

```ts apps/api/webiny.application.ts
import * as aws from "@pulumi/aws";
import { createApiApp } from "@webiny/serverless-cms-aws";
import { isResourceOfType } from "@webiny/pulumi";

export default createApiApp({
  pulumi(app) {
    app.onResource(resource => {
      // Change the name of the Elasticsearch domain.
      if (isResourceOfType(resource, aws.elasticsearch.Domain)) {
        resource.config.domainName("my-new-name");
      }
      // Set a new timeout on all Lambda functions that are created as part of this Pulumi app.
      if (isResourceOfType(resource, aws.lambda.Function)) {
        resource.config.timeout(10000);
      }
    });
  }
});
```
