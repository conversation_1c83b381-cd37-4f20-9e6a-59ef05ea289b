---
id: changelog
title: Webiny 5.36.1 Changelog
description: See what's new in Webiny version 5.36.1.
---

import themeSelector from "./assets/theme-selector.png";
import buttonLinkUrl from "./assets/button-link-url.png";

<GithubRelease version={"5.36.1"} />

## Page Builder

### Content Not Being Displayed with the new Lexical Editor ([#3325](https://github.com/webiny/webiny-js/pull/3325))

With the 5.36.0 release, we've addressed a couple of Lexical-related usability issues. Unfortunately, we've also introduced an issue that would cause content created with the previous version of the editor to not get displayed.

With this release, the Lexical content that was created prior to 5.36.0 version of Webiny should displayed as expected.

### Website Settings - Fixed Theme Selector ([#3331](https://github.com/webiny/webiny-js/pull/3331))

In a multi-theme setup, at the end of the Website Settings form, the select component that would enable users to pick the theme for the current tenant wasn't working as expected. On page load, the select component would be simply empty and not showing the value that was previously selected.

<Image src={themeSelector} alt="Website Settings - Fixed Theme Selector" />

As seen in the screenshot, the select component now correctly shows the currently selected theme.

### Intermittent Errors When Refreshing the Page Builder ([#3331](https://github.com/webiny/webiny-js/pull/3331))

Intermittently, when refreshing the Page Builder's page editor, users might experience the editor crashing. This issue has been addressed with this release.

### Button Page Element Crashing On Empty Link URL ([#3331](https://github.com/webiny/webiny-js/pull/3331))

When creating a button page element, in some cases, if the user would leave the link URL field empty, the editor would crash. This issue has been addressed with this release.

<Image src={buttonLinkUrl} alt="Button Page Element Crashing On Empty Link URL" />

### Folder Creation Issue ([#3328](https://github.com/webiny/webiny-js/pull/3328))

The 5.36.0 version introduced a bug that affected the folder creation process when using the "Create folder" button in the top right corner. After completing the folder creation process, the folder was not saved with the correct `parentId`, and not showing in the folder tree.

With this bug fix, folders will now be properly saved with the appropriate `parentId`.

### Folder Tree Performance Improvements ([#3332](https://github.com/webiny/webiny-js/pull/3332))

We experienced some performance issues when creating 8 or more nested folders: this has been solved and the tested creating more than 50 nested folders.

## File Manager

### Migration Failure in Locale without File Entries ([#3333](https://github.com/webiny/webiny-js/pull/3333))

With 5.36.0 version we introduced the [Advanced Content Organisation for File Manager](https://www.webiny.com/docs/release-notes/5.36.0/changelog#introducing-advanced-content-organization-for-file-manager-3312): in order to work a data migration was needed and executed via the [Data Migration Lambda Function](https://www.webiny.com/docs/release-notes/5.35.0/changelog#data-migration-lambda-function-3079-3150). When dealing with a locale that lacked file entries, the migration process would fail.

We have resolved this migration bug. The fix ensures that in cases where a locale does not have any file entries, the migration process no longer encounters errors and proceeds smoothly.

<Alert type="warning">

The fix specifically targets instances where the migration failed due to this particular scenario. In case you experienced a migration failure, this has been marked as failed and will be re-run during Webiny project deploy.

</Alert>

## Development

### `webiny info` Command Now Works As Expected ([#3331](https://github.com/webiny/webiny-js/pull/3331))

Prior to this release, the [`webiny info`](/docs/core-development-concepts/basics/webiny-cli#common-commands) command would not work if an environment wasn't passed (in which case the command should internally retrieve a list of all environments).

This issue has now been addressed.
