---
id: ca039c85
title: Webiny 5.39.10 Changelog
description: See what's new in Webiny version 5.39.10.
---

import { GithubRelease } from "@/components/GithubRelease";

<GithubRelease version={"5.39.10"} />

## Data Migrations

### 5.39.6-001 Data Migration - Retrying READ Operations ([#4225](https://github.com/webiny/webiny-js/pull/4225))

With this release, we've addressed an issue with the 5.39.6-001 data migration, where some READ operations that are happening during the migration process were failing due to the `InternalServerError` error. Most probably, this was happening due to the fact that the DynamoDB table was not ready to serve the requests, even though the migration process was trying to read data from it. 

To address this issue, we've added a retry mechanism to the migration process. This way, if the READ operation fails, the migration will retry the operation a few times before failing the migration process. After further testing, we've found that this mechanism significantly improves the overall reliability of the migration process, removing the possibility of the migration failing due to the `InternalServerError` error.

Note that, previously, we were already doing this for WRITE operations, but now we've also added this mechanism for READ operations, making the migration process more robust.

<Alert type={"info"}>

  For more technical information on the improved `5.39.6-001` data migration, please refer to the original pull request ([#4154](https://github.com/webiny/webiny-js/pull/4154)).

</Alert>
