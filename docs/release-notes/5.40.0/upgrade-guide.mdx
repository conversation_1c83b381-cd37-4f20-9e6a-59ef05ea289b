---
id: bad5c115
title: Upgrade from 5.39.x to 5.40.0
description: Learn how to upgrade Webiny from 5.39.x to 5.40.0.
---

import { Al<PERSON> } from "@/components/Alert";
import { AdditionalNotes } from "@/components/upgrade/AdditionalNotes";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.39.x to 5.40.x

</Alert>

<Alert type="info">

Make sure to check out the [5.40.0 changelog](./changelog) to get familiar with the changes introduced in this
release.

</Alert>

## Step-by-Step Guide

### 1. Upgrade Webiny Packages

Upgrade all Webiny NPM packages by running the following command:

```bash
yarn up "@webiny/*@5.40.7"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return **5.40.7**.

### 2. Run the Upgrade Command

The next step is to run the project upgrade:

```bash
yarn webiny upgrade
```

### 3. Breaking Changes❗

This release contains a couple of breaking changes that might affect your project, if you've extended your project via plugins. If you're using the default installation of Webiny without customizations, you can skip this section.

#### 3.1 Removed Deprecated Backend APIs

As mentioned in the [changelog](/docs/release-notes/5.40.0/changelog#removed-deprecated-ap-is-4051), with this release, we've removed a couple of backend JavaScript APIs that were marked as deprecated in previous releases.

Essentially, we've removed a couple of older methods used for subscribing to lifecycle events and also for performing Headless CMS-related security permissions checks.

Before deploying, please refer to the separate [Removed APIs](/docs/release-notes/5.40.0/removed-apis) article, and make sure to update your project code, if necessary.

#### 3.2 Removed/Replaced Frontend APIs

We've also upgraded some of the Admin app components and plugins, namely `Form`, `AppPbWebsiteSettings`, and `FileManagerFileTypePlugin`.

Before deploying, please refer to their respective sections in the [Removed APIs](/docs/release-notes/5.40.0/removed-apis) article, and make sure to update your project code, if necessary.

#### 3.3 Pulumi v6 Upgrade

With this release, we've upgraded the Pulumi version to v6. Make sure to check the dedicated [Pulumi v6](/docs/release-notes/5.40.0/pulumi-v6) article for more information on this one.

#### 3.4 Page Builder - `theme` Folder Updates

With this release, we've made a couple of changes within the `apps/theme` folder. For more information, we recommend you take a look at the [Theme Folder Updates](/docs/release-notes/5.40.0/theme-folder-updates) article.

### 4. Run Builds Locally

To verify that your project was correctly upgraded and is ready to be deployed, run the following commands:

```bash
yarn webiny build api --env=dev
yarn webiny build admin --env=dev
yarn webiny build website --env=dev
```

If these execute successfully, you're ready to move on to the next step, and deploy your project.

### 5. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

<AdditionalNotes />
