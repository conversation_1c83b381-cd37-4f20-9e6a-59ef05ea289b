---
id: c86d8000
title: Theme Folder Updates
description: Learn about the changes made to the `theme` folder in the Webiny v5.40.0 release.
---

import { <PERSON>ert } from "@/components/Alert";

## Overview

As mentioned in the [changelog](/docs/release-notes/5.40.0/changelog#theme-folder-changes-4135-4138), with this release, we've made a couple of changes to the `theme` folder, that was previously located in the `apps` folder.

In short, the main changes are:

1. the `theme` folder has been moved from the `apps` folder to the new `extensions` folder
2. the actual theme-related code is now located in the `theme/src` folder and not in the root of the `theme` folder (only `package.json` and `tsconfig.json` remain)
3. the [Reset CSS](https://meyerweb.com/eric/tools/css/reset/) styles that have been residing in the [`theme/global.scss`](https://github.com/webiny/webiny-js/blob/v5.39.6/apps/theme/global.scss) file have been removed ([reference](https://github.com/webiny/webiny-js/blob/v5.39.6/apps/theme/global.scss#L4-L53))

And although these changes will be automatically applied to your project upon performing the upgrade (upon running the [`webiny upgrade`](#) command), it's still useful to know the following.

### `global.scss` Backup and Replacement

During the upgrade process, your existing `global.scss` file will be automatically backed up (`global.backup.scss` will be created) and replaced with the new one.

This ensures that you don't lose any customizations you might have made to the `global.scss` file and that you can easily re-apply them to the new file.

### Removal of `global.scss` File Imports

Existing imports of the `global.scss` file in your `apps/admin` and `apps/website` folders (**Admin** and **Website** project applications) will be removed, as the `global.scss` file is now imported within the `theme` folder itself.
