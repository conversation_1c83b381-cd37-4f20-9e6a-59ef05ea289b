---
id: 02316cf3
title: Pulumi v6
description: Learn about the changes you can expect upon doing your first redeployment after the upgrade to Pulumi v6.
---

import { Alert } from "@/components/Alert";
import pulumiDownload from "./assets/pulumi-v6/pulumi-download.png";
import providerUpdates from "./assets/pulumi-v6/provider-updates.png";
import tagsRelatedUpdates from "./assets/pulumi-v6/tags-related-updates.png";
import eventRules from "./assets/pulumi-v6/event-rules.png";
import userPoolClientChanges from "./assets/pulumi-v6/user-pool-client-changes.png";
import cfStaging from "./assets/pulumi-v6/cf-staging.png";
import stateMachinePublish from "./assets/pulumi-v6/state-machine-publish.png";

## Overview

As mentioned in the [changelog](/docs/release-notes/5.40.0/changelog#introducing-pulumi-v6-4096), with this release, we've decided to update Pulumi to version 6.

And although this update should not cause any breaking changes in your existing infrastructure code, we've still compiled a list of all of the things you can expect upon doing your first redeployment after the upgrade.

Note that, on top of the changes listed in this article, you should pay extra attention to deployment logs in case you have custom cloud infrastructure (Pulumi) code in your project.

<Alert type="info">

To deploy necessary cloud infrastructure resources, by default, Webiny relies on Pulumi, a modern infrastructure as
code framework. Find out more in the following [IaC with Pulumi](/docs/infrastructure/pulumi-iac/iac-with-pulumi)
article.

</Alert>

<Alert type="warning">

With the 5.40.0 release, by default, the deployment (Pulumi) logs [are hidden](/docs/release-notes/5.40.0/changelog#pulumi-output-hidden-by-default) when deploying from local machines. To see
the logs, you can append the `--deployment-logs` flag upon running the [`webiny deploy`](/docs/core-development-concepts/basics/project-deployment) command.

</Alert>

## Notable Changes

### Pulumi Installation

Upon running the [`webiny deploy`](/docs/{version}/core-development-concepts/basics/project-deployment) command for the first time after the upgrade, you will see the new Pulumi version being downloaded and installed.

<Image src={pulumiDownload} alt="Pulumi v6 Download" />

### Provider Updates

Pulumi will report provider updates on resources, for example:

<Image src={providerUpdates} alt="Provider Updates" />

This is due to the fact that the underlying [`@pulumi/aws`](https://www.npmjs.com/package/@pulumi/aws) package has been updated.

<Alert type={"info"}>

For more information on Pulumi providers, please refer to the [Pulumi documentation](https://www.pulumi.com/docs/concepts/resources/providers/).

</Alert>

### `tagsAll` and `tags` Properties

Upon deploying your project, multiple tags-related updates will be reported, for example:

<Image src={tagsRelatedUpdates} alt="Tags Related Updates" />

As we can see, instead of just `tags`, Pulumi is now using the `tagsAll` and `tags` properties.

As explained in [Terraform documentation](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/guides/resource-tagging#propagating-tags-to-all-resources), essentially, the `tags` property contains only the tags that were set upon resource definition, while the `tagsAll` property contains all tags that are set on the resource, including those that were set on the provider level.

### Amazon Cloudwatch - Event Rules

Two changes can be seen when Amazon Cloudwatch event rules are deployed.

First, a change of the `forceDestroy` property, for example:

<Image src={eventRules} alt="Event Rules" />

Per [Pulumi documentation](https://www.pulumi.com/registry/packages/aws/api-docs/cloudwatch/eventrule/), the `forceDestroy` property is used to delete managed rules created by AWS. Since Webiny is not using this property, you can safely ignore this change.

Another change that can be seen is a change of the `isEnabled` property. This is due to the fact that the property has been deprecated in favor of the `state` property. This change is also safe to ignore, as it has been handled internally by Webiny.

### Amazon Cognito User Pool Client Changes

A couple of changes will be reported when Amazon Cognito user pool client is deployed:

<Image src={userPoolClientChanges} alt="Event Rules" />

As none of these properties are used by Webiny, you can safely ignore these changes.

<Alert type="info">

For more information on these properties, please refer to the [Pulumi documentation](https://www.pulumi.com/registry/packages/aws/api-docs/cognito/userpoolclient/).

</Alert>

### Amazon Cloudfront Distributions - `staging` Property

A change in the `staging` property of Amazon Cloudfront distributions will be reported:

<Image src={cfStaging} alt="Cloudfront Staging" />

Webiny is not using the `staging` property, so you can safely ignore this change.

<Alert type="info">

For more information on the `staging` property, please refer to the [AWS documentation](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/continuous-deployment.html).

</Alert>

### State Machine Publish

A change in the `publish` property of state machines will be reported:

<Image src={stateMachinePublish} alt="State Machine Publish" />

Webiny is not using the `publish` property, so you can safely ignore this change.

<Alert type="info">

For more information on the `publish` property, please refer to the [AWS documentation](https://docs.aws.amazon.com/step-functions/latest/dg/concepts-state-machine-version.html).

</Alert>
