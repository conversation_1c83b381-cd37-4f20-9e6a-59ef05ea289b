---
id: 882183e7
title: Webiny 5.41.1 Changelog
description: See what's new in Webiny version 5.41.1
---

<GithubRelease version={"5.41.1"} />

## Security

### External Identity Providers - Roles and Teams Assignments ([#4349](https://github.com/webiny/webiny-js/pull/4349)]

We've addressed a couple of issues related to assigning roles when using external identity providers, like [Auth0](/docs/enterprise/auth0-integration) or [Okta](/docs/enterprise/okta-integration). We've also added one new feature. More information can be found below.

#### 1. `groups` Property Now Works As Expected

Prior to this release, when using external identity providers, assigning roles via the newly introduced `groups` property would not work. For example, with Auth0, the following configuration would not work:

```diff-ts
    /**
     * Configure Auth0 authentication and authorization.
     */
    createAuth0({
        /**
         * `domain` is required for token verification.
         */
        domain: String(process.env.WEBINY_API_AUTH0_DOMAIN),
        /**
         * Construct the identity object and map token claims
         * to arbitrary identity properties.
         */
        getIdentity({ token }) {
            return {
                id: token["sub"],
                type: "admin",
                displayName: token["name"],
                
+               // This would work.
+               group: "full-access",

-               // This would not work.
-               groups: ["full-access"],
            };
        }
    }),
```

With this release, this has been addressed and both `group` and `groups` properties can now be used to assign roles when using external identity providers.

<Alert>

  As mentioned in the [5.41.0 release notes](/docs/release-notes/5.41.0/changelog), the `group` property has been deprecated. Users are encouraged to use the `groups` property instead.

</Alert>

#### 2. Programmatically Created Roles Now Work As Expected

When using external identity providers, trying to assign a role to a user that was programmatically created would not work. This has now been addressed.

<Alert>

  Programmatic creation of roles and teams has been introduced in the [5.41.0 release](/docs/release-notes/5.41.0/changelog#programmatic-creation-of-roles-and-teams-4303).

</Alert>

#### 3. Ability To Assign Teams ([#4349](https://github.com/webiny/webiny-js/pull/4349)]

Since the introduction of the Teams feature, users were not able to assign teams to users when using external identity providers. With this release, this gap has finally been closed.

<Alert type="info">

  Note that the Teams feature is part of Webiny's enterprise offering. To learn more, please check out the
  [Teams](/docs/enterprise/aacl/teams) article.

</Alert>

## Page Builder

[//]: # (TODO: We had a longer discussion on the APIs for registering new page elements, and for now, we'll pause any announcements.)
[//]: # (### New "Page Builder Element" Extension &#40;[#4342]&#40;https://github.com/webiny/webiny-js/pull/4342&#41;&#41;)

[//]: # ()
[//]: # (We've introduced a new type of an extension called Page Builder element.)

[//]: # ()
[//]: # (Once run, the extension will make sure a users ends up with all of the required files and configurations for a new Page Builder element plugin. This will make it easier for users to start developing new Page Builder elements.)

[//]: # ()
[//]: # (The extension can be accessed like any other extension, via the `yarn webiny scaffold` command, by then selecting **New Extension** from the list of all available scaffolds, and ultimately choosing **Page Builder Element**.)

### New "Website" Extension ([#4344](https://github.com/webiny/webiny-js/pull/4344))

With the new **Page Builder element** extension, we've also introduced the new **Website** extension, which makes it easier to start extending Webiny's **Website** application.

As already mentioned above, in the same fashion, the extension can be accessed via the `webiny scaffold`, or the new [`webiny extension`](/docs/release-notes/5.41.1/changelog#the-webiny-extension-command) command.

[//]: # (TODO: We had a longer discussion on the APIs for registering new page elements, and for now, we'll pause any announcements.
[//]: # (### New Page Element Plugins &#40;[#4340]&#40;https://github.com/webiny/webiny-js/pull/4340&#41;&#41;)

[//]: # ()
[//]: # (In order to be more in line with the new [Page Builder element extension]&#40;/docs/release-notes/5.41.1/changelog#new-page-builder-element-extension-4342&#41;, and also some of the best practices we've been pushing lately, we've updated the existing Page Builder element plugins. Essentially, instead of using plain JavaScript objects, users will now do the same via React components.)

[//]: # ()
[//]: # (Note that the previous way of creating Page Builder element plugins will still work, but moving on, we recommend using the new way.)

[//]: # (<Alert type={"info"}>)

[//]: # ()
[//]: # (Our existing [Create a Custom Page Element]&#40;/docs/page-builder/extending/create-a-page-element&#41; article has been)

[//]: # (updated to reflect these changes.)

[//]: # ()
[//]: # (</Alert>)

<Alert>

  At the moment, there aren't any code examples that include the new **Website** extension. We'll be adding them in the upcoming releases.

</Alert>

## Development

### Improved Error Messages ([#4341](https://github.com/webiny/webiny-js/pull/4341))

Prior to this release, in case a user immediately tried to [watch](/docs/core-development-concepts/basics/watch-command) the **Admin** or **Website** frontend applications, but the **Core** and **API** applications weren't deployed, they would receive an error message that wasn't very helpful. We've improved this error message to provide more context and help users understand what's going on.

### The `webiny extension` Command

Up until now, in order to scaffold a new extension, users had to run the `yarn webiny scaffold` command, and then select **New Extension** from the list of all available scaffolds. And although this isn't a big deal, still, since extensions are becoming more and more important in the last couple of releases, we've decided to introduce a new command called [`webiny extension`](/docs/core-development-concepts/basics/webiny-cli#yarn-webiny-extension).

Essentially, everything stays the same, but now, instead of running `yarn webiny scaffold`, users can run `yarn webiny extension` to scaffold a new extension.

Note that the `webiny scaffold` command will still work, but moving forward, we'll be emphasizing the use of the new `webiny extension` command.
