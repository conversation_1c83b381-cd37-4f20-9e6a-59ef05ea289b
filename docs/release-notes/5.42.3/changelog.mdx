---
id: d9e4a63f
title: Webiny 5.42.3 Changelog
description: See what's new in Webiny version 5.42.3
---

import acoFolderFields from "./assets/aco-folder-fields.png";

<GithubRelease version={"5.42.3"} />

## Advanced Content Organization (ACO)

### Custom Fields for ACO Folders ([#4561](https://github.com/webiny/webiny-js/pull/4561))

With this update, developers can now extend the ACO folder model by adding custom fields. These fields are seamlessly integrated into the GraphQL schema and the Admin app UI, allowing for greater flexibility when organizing content.

Since ACO Folders are powered by Webiny's Headless CMS, adding a new field is as simple as defining it on the Folder model. Once added, the field will automatically appear under the extensions field in the GraphQL schema and will be available in the Admin app when creating or editing folders.

#### Plugin-Based Customization
Webiny provides specialized plugin factories to modify folder models based on their use case:

- **Headless CMS Folders**: use `createCmsFolderModelModifier` to extend folders for Headless CMS content models.
- **Page Builder Folders**: use `createPbPageFolderModelModifier` to extend folders for Page Builder pages.
- **File Manager Folders**: use `createFmFileFolderModelModifier` to extend folders for File Manager.

These plugins allow fine-grained control, such as targeting specific models.

For more details, check out the related documentation:

- [Adding custom fields to CMS folders](/docs/headless-cms/extending/customize-folder-fields)
- [Adding custom fields to Page Builder folders](/docs/page-builder/extending/customize-folder-fields)
- [Adding custom fields to File Manager folders](/docs/file-manager/extending/customize-folder-fields)

<Image src={acoFolderFields} title={"Custom Fields for ACO Folders"} />

### Fixed `listFoldersCompressed` GraphQL Query ([#4599](https://github.com/webiny/webiny-js/pull/4599))

With the [Enhanced Folder Listing and Performance](/docs/release-notes/5.42.2/changelog#enhanced-folder-listing-and-performance-4539) feature we did with the previous release, we've enhanced the performance of folder listing operations.

However, due to a bug within the new `listFoldersCompressed` GraphQL query, the permissions for folders were not being accurately reflected in the UI, leading to potential confusion for users (on the backend, the permissions were still correct). 

This bug has been fixed, and the `listFoldersCompressed` query now accurately reflects the permissions for folders in the UI.

## Page Builder

### Button Page Element - Fixed "Scroll To Element" Action ([#4606](https://github.com/webiny/webiny-js/pull/4606))

A regression was in one of the previous releases that caused the "Scroll To Element" button action to not work properly. This has been fixed, and the action now functions as expected.

### Introduced `PbEditorPageElementGroupPlugin` Plugin ([#4598](https://github.com/webiny/webiny-js/pull/4598))

The `PbEditorPageElementGroupPlugin` plugin has been introduced to allow developers to create new groups in the Page Builder editor. This plugin is used to group elements in the editor, making it easier to manage and organize elements on the page.
