---
id: 3d8d6ac8
title: Webiny 5.40.2 Changelog
description: See what's new in Webiny version 5.40.2.
---

<GithubRelease version={"5.40.2"} />

## Deployment

## Increased Memory Size For AWS Lambda Functions to 1024MB ([#4197](https://github.com/webiny/webiny-js/pull/4197))

With this release, we've increased the memory size for all AWS Lambda functions to 1024MB. This change will help improve the performance of your Webiny applications.

In terms of cost, note that this change should not have a significant impact on your AWS bill. The cost increase should be minimal or even non-existent, depending on your usage. This is because AWS Lambda pricing is based on the number of invocations, the duration of each invocation, and the amount of memory allocated to the function. By increasing the memory size, the duration of each invocation will be shorter, which can result even in lower costs.

<Alert type="info">

Check out [this AWS Knowledge Center article](https://repost.aws/knowledge-center/lambda-memory-compute-cost) for more information on how memory and computing power affect AWS Lambda costs.

</Alert>

## Headless CMS

### Fix Content Entry Traverser and Make it Async ([#4196](https://github.com/webiny/webiny-js/pull/4196))

Content Entry Traverser is now async, and supports async visitors. We've also fixed a bug with the value path generation for single value Object and Dynamic Zone fields, and ensured that the visitor function is executed for all falsy values (`null`, `false`, `[]`, `""`).

### Fix Default Value Handling for Predefined Values ([#4201](https://github.com/webiny/webiny-js/pull/4201))

When using predefined values in your content model fields, there's an ability to mark one or more value as "selected", making it a default value for the active renderer. With this release, we've fixed a few bugs in the relevant renderers (radio buttons, checkboxes, and select menu), made sure the correct values are selected in the corresponding UI elements, and also ensured that the default values are correctly set in the form itself.

## Development

### Fixed an Issue with Webiny Packages Not Building Correctly on Windows 11 ([#4182](https://github.com/webiny/webiny-js/pull/4182))

We've received a report of Webiny packages not building correctly on Windows 11. Thanks to [`will-fagerstrom`](https://github.com/will-fagerstrom), this issue has been fixed, and you can now build Webiny applications on Windows 11 without any problems.
