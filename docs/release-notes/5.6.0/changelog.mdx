---
id: aafea9e9
title: Webiny 5.6.0 Changelog
description: See what's new in Webiny version 5.6.0.
---

import { <PERSON>ert } from "@/components/Alert";

# Changes

This document highlights the most important fixes, improvements, and features, that were introduced in Webiny `5.6.0`.

<Alert type="info" title="How To Upgrade?">

Please check the [Webiny 5.6.0 migration guide](/docs/release-notes/5.6.0/upgrade-guide) for the upgrade steps.

</Alert>

## Headless CMS

### The Reference Field Can Now Reference Different Content Models ([#1572](https://github.com/webiny/webiny-js/pull/1572))

A feature that has been frequently requested is finally here!

Within a single reference field, you can now reference content entries that are created of multiple different content models.

For example, here we are adding the **Published Work** reference field to the existing **Author** content model. Notice how we've picked not one, but three different content models in the **Content Models** field: **Book**, **Video**, and **Music**.

![Reference Field - Adding Multiple Different Content Models](./cms-ref-multiple-models.png)

This means that, while managing authors, users will be able to select multiple book, video, and music entries, all referenced via the single **Published Work** reference field.

### Added Ability To Change Content Model Group ([#1591](https://github.com/webiny/webiny-js/pull/1591))

Upon creating a content model, users are required to select a content model group, into which their new content model will be put. But, if later down the road, users decided to change the group, they wouldn't be able to do it via user interface.

To address this issue, in the Headless CMS content model editor, a new form field was added to the settings section, allowing us to change the group if needed.

![Changing Content Model Groups](./cms-content-model-groups-changing.gif)

## Page Builder

### Added Visibility Element Setting in the Editor([#1579](https://github.com/webiny/webiny-js/pull/1579))

Now, you can set the visibility of an element using the _Visibility setting_ in the page editor sidebar.

![Webiny Page Builder visibility element setting](./pb-navigator-visibility.gif)

### Added Navigator in the Editor([#1579](https://github.com/webiny/webiny-js/pull/1579))

Quickly see the hierarchy structure of the page in a tree view.
You can also:

- reorder block by drag and drop
- select and unhide elements that are set hidden previously
- quickly jump between elements

![Webiny Page Builder navigator demo](./pb-navigator-block-reorder.gif)

## Webiny CLI

### Improved Watch Command With Brand New `browser` Output ([#1573](https://github.com/webiny/webiny-js/pull/1573))

In our [previous release](/docs/release-notes/5.5.0/changelog), we released the brand new [`webiny watch`](/docs/release-notes/5.5.0/changelog#brand-new-webiny-watch-command-1540) command. But, we didn't stop there, so today, we're happy to introduce its first improvement.

Upon running the `webiny watch` command, by simply appending the `--browser output` argument, you can now have all of the build, deploy, and logs output displayed in your browser.

The following video shows the new improvement in action:

![Webiny Watch Command - Browser Output](./cli-watch-browser-demo.gif)

<Alert type="info">

The full command we run here is `yarn webiny watch api/code/graphql --env dev --logs --output browser`.

</Alert>

Comparing this to the original terminal output, we can immediately see that here we have a lot more options when it comes to building the actual user interface. Features like scrolling, resizing of panes, and others, are simply not possible to implement in your regular terminal.

This also gives us ability to maybe add more features in the future, if our community will show interest.

<Alert type="success">

Have an awesome idea? [Let us know](https://www.webiny.com/slack), we'd be glad to hear about it!

</Alert>

## File Manager

### Fixed Uploading Files With Less Standard Extensions([#1577](https://github.com/webiny/webiny-js/pull/1577))

When in File Manager, uploading files with a less standard file extension would fail. This is no longer the case.

## Other

### Added Tags For Remaining Cloud Infrastructure Resources ([#1585](https://github.com/webiny/webiny-js/pull/1585))

In one of our previous releases, we added tagging of cloud infrastructure resources. In other words, all resources that are deployed to your AWS account are tagged with `WbyProjectName` and `WbyEnvironment` tags, which makes it easier to later find everything that's deployed by Webiny, in your [AWS Management Console](https://aws.amazon.com/console/).

But, this was done for the **API** project application's cloud infrastructure resources only. The remaining **Admin Area** and **Website** applications were still deploying resources without tagging anything.

So, from now on, you can expect all of the project applications (meaning all cloud infrastructure resources) to apply tags, so you get a complete picture.

<Alert type="success">

As you may have already known, every Webiny project, by default, comes with three project application: **API** (`api`), **Admin Area** (`apps/admin`), and **Website** (`apps/website`). These consist of application code, and, more importantly, of necessary cloud infrastructure that needs to be deployed in order for them to work as intended.

Read more about project organization in the [Project Organization](/docs/core-development-concepts/project-organization/project-applications-and-packages) key topics section.

</Alert>

### Migrating Config Files to TypeScript ([#1584](https://github.com/webiny/webiny-js/pull/1584))

For a very long time, our wish was to have Webiny projects be 100% TypeScript. And while that was to a large degree true, there were still a couple of pieces missing.

So, with this release, we migrated all of the different JavaScript configuration files to TypeScript. These include:

- `webiny.project.ts` - located in your project root
- `webiny.application.ts` - located within project application folders (e.g. [`api/webiny.application.ts`](https://github.com/webiny/webiny-js/blob/next/packages/cwp-template-aws/template/api/webiny.application.ts))
- `webiny.config.ts` - located within all packages in your Webiny project

Note that the existing JavaScript configuration files will still work. You're not forced to migrate if you don't want to or it's not that important.
