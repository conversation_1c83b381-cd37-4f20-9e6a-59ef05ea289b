---
id: changelog
title: Webiny 5.37.2 Changelog
description: See what's new in Webiny version 5.37.2.
---

import pbInputsNotUpdating from "./assets/pb-inputs-not-updating.png";
import pbScrollToElement from "./assets/pb-scroll-to-element.png";
import pbNewTab from "./assets/pb-new-tab.png";
import pbScrollToElementLink from "./assets/lexical-scroll-to-element-link-x.png";
import cmsNewEntryDialog from "./assets/cms-new-entry-dialog.png";

<GithubRelease version={"5.37.2"} />

## Security

### Upgrade `jsonwebtoken` to v9.0.1 ([#3483](https://github.com/webiny/webiny-js/pull/3483))

We've upgraded the [`jsonwebtoken`](https://www.npmjs.com/package/jsonwebtoken) dependency to v9, since there were some security vulnerabilities that were fixed with that release. You can learn more about it in the [official changelog](https://github.com/auth0/node-jsonwebtoken/blob/master/CHANGELOG.md#security-fixes).

### HTML Sanitization In `@webiny/react-rich-text-renderer` Package ([#3477](https://github.com/webiny/webiny-js/pull/3477))

In order to further minimize the possibility of [cross-site scripting (XSS) attacks](https://en.wikipedia.org/wiki/Cross-site_scripting), the HTML that's rendered by the [legacy rich text editor](/docs/{exact:5.37.x}/headless-cms/integrations/legacy-render-rich-text-content) will now be sanitized, which is done with the help of the [`sanitize-html`](https://www.npmjs.com/package/sanitize-html) library.

Sanitization rules can be completely customized, using global configuration, or it can be configured on
individual component instances. Read more about the sanitization configuration on [NPM](https://www.npmjs.com/package/@webiny/react-rich-text-renderer).

## Page Builder

### Form Field Values Not Updating When Switching Between Page Elements ([#3478](https://github.com/webiny/webiny-js/pull/3478))

With the [5.37.0 release](/docs/release-notes/5.37.0/changelog), a regression was introduced that affected the page editor.

When switching between page elements in the page editor, in the right sidebar, the form field values would not update to reflect the values of the newly selected element.

<Image src={pbInputsNotUpdating} alt={"Right Sidebar - Form Field Values Not Correct"} />

This issue has now been taken care of and the form field values will update as expected.

### Button Page Element Fixes ([#3479](https://github.com/webiny/webiny-js/pull/3479))

Two issues have been addressed with the Button page element.

First, in case the button was first used as a regular link to another page, and then the user decided to use the **Scroll to element** functionality instead, the button would not update to reflect the new functionality. Basically, the button would still behave as a regular link to another page.

<Image src={pbScrollToElement} alt={"Button Page Element - Scroll to Element"} />

Second, a similar issue was present when a user enabled **New tab** option upon setting up their button as a regular link to another page. Again, in case the user decided to use the **Scroll to element** functionality instead, the button would still open the link in a new tab, making the **Scroll to element** functionality unusable.

<Image src={pbNewTab} alt={"Button Page Element - New Tab Option"} />

Both issues have been addressed and the button will now behave as expected.

### Bringing the Scroll to Element Feature Into the Lexical Editor ([#3474](https://github.com/webiny/webiny-js/pull/3474))

Previously, the **Scroll to element** feature was only available with the Button page element.

With this release, the feature has been also brought into the Lexical editor.

<Image src={pbScrollToElementLink} alt={"Scroll To Element Link"} />

## Headless CMS

### Improve the New Entry Modal Dialog ([#3485](https://github.com/webiny/webiny-js/pull/3485))

With the introduction of the advanced content organization in our previous releases, all content is now organized in folders. With this release, we've also added the folder selector to the modal dialog that lets you create new content entries inline, when using the Reference field. Now you will clearly see where the new content entry is located in the associated content model folder hierarchy.

<Image src={cmsNewEntryDialog} alt={"Folder Selector in the New Entry Modal Dialog"} />

## Deployments

### `elasticSearch` Parameter Fixes ([#3488](https://github.com/webiny/webiny-js/pull/3488)) ([#3481](https://github.com/webiny/webiny-js/pull/3481))

With the past [5.36.0 release](/docs/release-notes/5.36.0/changelog#configure-a-shared-elastic-search-domain-via-webiny-application-ts-3308), we've improved the `elasticSearch` parameter that can be specified via the `webiny.application.ts` file. In short, the parameter also allows developers to specify a shared Amazon ElasticSearch cluster, so that it's used with multiple Webiny project deployments, instead of having to create a new cluster for each deployment. This can be useful for development and staging environments, where the data is not critical.

However, a few issues were present with the parameter, and they have now been addressed.

<Alert type="info">

To learn more about this feature, please refer to the [Using a Shared Amazon Elasticsearch (OpenSearch) Domain](/docs/{exact:5.37.x}/infrastructure/basics/modify-cloud-infrastructure#using-a-shared-amazon-elasticsearch-open-search-domain) article.

</Alert>
