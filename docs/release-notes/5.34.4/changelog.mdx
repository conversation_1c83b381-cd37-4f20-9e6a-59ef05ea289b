---
id: changelog
title: Webiny 5.34.4 Changelog
description: See what's new in Webiny version 5.34.4.
---

## Page Builder

### Image Page Element - Further Optimizations Of the `srcset` HTML Attribute ([#3026](https://github.com/webiny/webiny-js/pull/3026))

With this release, we've further optimized the `srcset` HTML attribute that gets assigned to images, rendered via the Image page element.

From now on, if an image has a custom width set by the user via page editor (via element settings), the `srcset` attribute will contain only the optimal widths, as opposed to having all possible widths listed unnecessarily.

For example, if the user provided `155px` as the width of the image, the `srcset` attribute will contain only the following:

```html
srcset="xyz.png?width=100 100w, xyz.png?width=300 300w
```

Before, the list would contain images up to 2500 pixels in width. This is unnecessary because the image itself will never be displayed in those sizes.

### Fixed Data Fetching For Pages List and Form Elements ([#3020](https://github.com/webiny/webiny-js/pull/3020), [#3027](https://github.com/webiny/webiny-js/pull/3027))

With this release, we've implemented a couple of fixes for the existing Pages List and Form page elements, all related to data fetching in multi-tenant and multi-locale environments.

## Development

### Pulumi CLI - Using ARM64 Version When Possible ([#3028](https://github.com/webiny/webiny-js/pull/3028))

Prior to this release, upon running the Webiny CLI for the first time and downloading the required Pulumi CLI, users using macOS would always end up with the X64 version of the binary on their machine.

With this release, we've ensured that correct ARM64 version of the binary is downloaded for users with newer Apple M1 and M2 processors.
