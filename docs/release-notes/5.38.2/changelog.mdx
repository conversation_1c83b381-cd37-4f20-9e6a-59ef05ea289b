---
id: ec1cd8a9
title: Webiny 5.38.2 Changelog
description: See what's new in Webiny version 5.38.2.
---

import { Gallery } from "@/components/Image";
import hcmsFm1 from "./assets/hcms-fm-step-1.png";
import hcmsFm2 from "./assets/hcms-fm-step-2.png";
import fmFileDetails from "./assets/fm-file-details.png";

<GithubRelease version={"5.38.2"} />

## Headless CMS

### File Manager Overlay When Opened from the Create Reference Dialog ([#3758](https://github.com/webiny/webiny-js/pull/3758))

Before this release, users were experiencing unusual behaviour when interacting with the file manager that opened from the new reference entry dialog. The following steps could reproduce the issue:

1. Open an entry with a reference field, ensuring the target model has a file field.
2. Click on the "Create a new record" button.
3. The dialog for creating a new record should open.
4. Click on the file field.
5. Notice that the file manager opened in the background, preventing the user from interacting with it.

With this release, the identified issue has been addressed. Users can now seamlessly interact with the file manager opened from the new reference entry dialog without encountering the previously observed odd behavior.

<Gallery>
  <Image src={hcmsFm1} alt={"Reproduce bug: Step 1"} />
  <Image src={hcmsFm2} alt={"Reproduce bug: Step 2"} />
</Gallery>

A shoutout to Ben Lopez for spotting and reporting this issue! 🙏

### Fix Removal of Last Item in a Multiple Reference Field ([#3765](https://github.com/webiny/webiny-js/pull/3765))

In 5.38.0 a bug was introduced that prevented users from removing the last item in a multiple reference field. The item was removed from the UI, but it was not sent to the API, and it was not updated.

A shoutout to Nathanael McMillan for reporting this bug! ❤️

### Multi-Model Multi-Value Reference Field Output Order on Read API ([#3761](https://github.com/webiny/webiny-js/pull/3761))

When having multiple reference field, with multiple models, the order of the items was not correctly returned from the Read API.

## File Manager

### Ensure extension exists on edited images ([#3760](https://github.com/webiny/webiny-js/pull/3760))

We fixed a bug where an image, edited via the built-in editing dialog in the File Manager, would lose its extension, if the original image name didn't contain an extension. We're now sending the original file key to the API when requesting a presigned payload. The original file key is used to generate a new file key. File name is kept the same as the original, and it can be with or without an extension.

A shoutout to Ben Lopez for spotting this issue! 🙏

### File Details Drawer Overlay ([#3759](https://github.com/webiny/webiny-js/pull/3759))

With this release, we have fixed a bug that caused some elements from the underlying list to remain highlighted when users opened the file details drawer.

<Image src={fmFileDetails} title={"File Manager: File Details"} />

### Add New Config Capabilities to the File Details Drawer

This is a little improvement which allows you to control the grouping of file fields, the width of the overall drawer, and also the proportions of the left and right panels within the drawer.

Have a look at the docs article describing [how to configure the file details drawer](https://www.webiny.com/docs/file-manager/extending/customize-file-details-drawer).

## Page Builder

### Fix GraphQL Selection in the `deleteElement` Mutation ([#3762](https://github.com/webiny/webiny-js/pull/3762))

In 5.38.0, we introduced a regression where the mutation sent from the client side contained a selection of fields on a boolean response. This is now fixed, and you can continue using the delete button on saved elements in the elements toolbar of the page editor.  

A shoutout to Antonio Bianchetti for reporting the bug! 🍻
