---
id: aafea9c4
title: Upgrade from 5.22.0 to 5.22.1
description: Learn how to upgrade Webiny from 5.22.0 to 5.22.1.
---

import { Al<PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.21.0 to 5.22.1

</Alert>

<Alert type="info">

This Webiny upgrade only requires an update of `npm` packages.
Make sure to check out the [5.22.1 changelog](/docs/release-notes/5.22.1/changelog) to get familiar with the changes introduced in this release.

</Alert>

## 1. Upgrade Webiny Packages

Upgrade all `@webiny/*` packages by running the following command:

```bash
yarn up "@webiny/*@5.22.1"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return `5.22.1`.

## 2. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

<Alert type="warning">

As stated in the [Upgrade Webiny](/docs/release-notes/upgrade-webiny#pre-production-environments-first) section, we recommend that you first deploy your changes into one of your pre-production environments, like `dev` or `staging`.

</Alert>

<Alert type="success">

Learn more about different deployment environments in the [CI/CD / Environments](/docs/core-development-concepts/ci-cd/environments) key topic.

</Alert>
