---
id: aafea9c1
title: Webiny 5.22.1 Changelog
description: See what's new in Webiny version 5.22.1.
---

# Changes

In the 5.22.0 release, we migrated the system to Webpack 5. The missing piece of the puzzle was a polyfill for the `<PERSON>uffer` module in the browser, since Webpack 5 no longer polyfills anything. This, unfortunately, caused issues with our telemetry module. Our CI was also unable to catch this issue, because the telemetry is disabled in our CI environment.

## Project Utils

For webpack builds, we added a polyfill for [buffer](https://www.npmjs.com/package/buffer), when `<PERSON>uffer` is used in the browser.

## Telemetry

To be able to catch this kind of issues in our CI, we've restructured the code of the telemetry module in such a way that the majority of logic will get executed even when telemetry is disabled, but no actual telemetry will be sent. This will ensure that similar issues are caught in our CI pipeline in the future.
