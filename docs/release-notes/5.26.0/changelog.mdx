---
id: changelog
title: Webiny 5.26.0 Changelog
description: See what's new in Webiny version 5.26.0.
---

# Changes

## Page Builder

### New IFrame Page Element ([#2319](https://github.com/webiny/webiny-js/pull/2319))

A new IFrame ([inline frame](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/iframe)) page element is available under the "Media" section of the Page Builder elements. Choose an IFrame source (which must be a valid URL) to render an iFrame. You can also set different properties at different breakpoints as you can in other elements.

![Choose an IFrame source - a valid URL - to render the iFrame](https://user-images.githubusercontent.com/2216344/162738130-ab69873c-cc7c-4d25-af12-f0667ccd0ff0.png)

## File Manager

### Fixed Tagging of Files ([#2381](https://github.com/webiny/webiny-js/pull/2381))

In some cases, trying to tag an uploaded file in the File Manager would cause the app to break. This has now been addressed.

![Tagging Uploaded File](https://user-images.githubusercontent.com/5121148/168072290-acaad166-197f-400e-b024-0df0d8b8a7ca.png)

### Changing the File Upload Limits Is Now Propagated Immediately ([#2385](https://github.com/webiny/webiny-js/pull/2385))

Prior to this release, changes to File Manager settings would not take immediate effect.

More specifically, if a user tried to upload a file and received the "Max file exceeded." error message, even after incrementing the maximum file upload size limit, and then trying to re-upload the file would not work. Only after a complete page reload the user would be able to continue with the upload.

![File Manager Settings](https://user-images.githubusercontent.com/5121148/167284117-309007cf-989f-4ca4-9395-c144f2aacc0f.png)

This has now been addressed. Changes to File Manager settings will take immediate effect and doing a page reload is no longer needed.

## Headless CMS

### Validate Model Name Against The Existing Models ([#2324](https://github.com/webiny/webiny-js/pull/2324))

We added a check in the UI when creating a new model to verify that the model with same name does not exist in the system already.
The check is still being run on the API side, but this way we do not need to call the API to check for the existing model.

![Model Name Error](./assets/article-name-error.png)

## Development

### Customize The Elasticsearch Index Settings And Mappings For Your Deployment ([#2316](https://github.com/webiny/webiny-js/pull/2316))

We added plugins to customize your Elasticsearch Index creation for any of our applications.
To find out more, read [How To Customize Elasticsearch Index](/docs/core-development-concepts/extending-and-customizing/how-to-customize-elasticsearch-index).

### Split The Elasticsearch Index For Each Of The Locales ([#2323](https://github.com/webiny/webiny-js/pull/2323))

We added an option to split each Elasticsearch Index to per-locale one.
In all new Webiny deployments where the Elasticsearch is used, it is the default behavior.

On all old systems, if you want to change how the indexes are created, and change the existing ones, please read [How To Change Elasticsearch Index Name To Contain Locale](/docs/core-development-concepts/extending-and-customizing/how-to-change-elasticsearch-index-name-to-contain-locale).

### Add Japanese Elasticsearch Index Configuration ([#2326](https://github.com/webiny/webiny-js/pull/2326))

We added the Japanese Elasticsearch Index configuration for all of our applications.

### Replace `@typescript/lib-dom` with `@types/web`([a6488b](https://github.com/webiny/webiny-js/commit/a6cf5c720a7b440a9c7bc262d4e8b5d771a6488b))

[@types/web](https://www.npmjs.com/package/@types/web) is a package that contains DOM types for the majority of the web APIs used in a web browser. In your projects, it replaces `@typescript/lib-dom`, which you are more familiar with in form of `lib: ["dom"]` in your `tsconfig.json` files. When using `@types/web`, you no longer need to specify `lib: ["dom"]`.

The main reason for this change is that more and more packages out there are using this new utility package for their types, and if it ends up in your `node_moduiles`, all of a sudden, all your TS builds will start failing because the two types packages are clashing. To avoid these nightmare scenarios (and we spent one whole day figuring this out), we are officially switching to `@types/web` by adding it as a dependency to our `@webiny/app` package. The upgrade to Webiny 5.26.0 will take care of removing the `lib: ["dom"]` for you.
