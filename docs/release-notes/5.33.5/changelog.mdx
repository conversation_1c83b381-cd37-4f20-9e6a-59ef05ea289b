---
id: changelog
title: Webiny 5.33.5 Changelog
description: See what's new in Webiny version 5.33.5.
---

## Core

### DynamoDB To Elasticsearch Handler Payload Limit ([#2885](https://github.com/webiny/webiny-js/pull/2885))

We have increased the DynamoDB to Elasticsearch handler payload limit to 1GB.

The default was 1MB, which was not enough to transfer a lot of large entries from the DynamoDB to the Elasticsearch.

## Headless CMS

### Fix GraphQL Union Type Generation For the `ref` Fields ([#2887](https://github.com/webiny/webiny-js/pull/2887))

This PR fixes an issue with GraphQL union type generation for ref fields, configured to accept multiple models, within a nested object field.

### Initialize Content Model Data Parameter ([#2888](https://github.com/webiny/webiny-js/pull/2888))

We added the `data` parameter to the `initializeModel` GraphQL mutation, so users can send any kind of data in it and use it in their plugins.

### Compress Latest Entry Data ([#2889](https://github.com/webiny/webiny-js/pull/2889))

We fixed the bug where latest entry data was not compressed when it was sent to be stored into the Elasticsearch. This possibly caused problems with extremely large entries (400+ kb).
