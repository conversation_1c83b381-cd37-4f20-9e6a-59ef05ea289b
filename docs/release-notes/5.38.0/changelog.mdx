---
id: changelog
title: Webiny 5.38.0 Changelog
description: See what's new in Webiny version 5.38.0.
---

import { Gallery } from "@/components/Image";
import flp from "./assets/flp.png";
import watchCmd from "./assets/watch-cmd.png";
import blockPreview from "./assets/blocks-preview.png";
import multiStepForms from "./assets/multi-step-forms.png";
import nestedFields from "./assets/nested-fields.png";
import lexicalFlat from "./assets/lexical-flat.png";
import lexicalCustomTransform from "./assets/lexical-custom-transform.png";
import lexicalToHtml from "./assets/lexical-to-html.png";
import advancedSearchFilters from "./assets/advanced-search-filters.png";
import bulkActionsHcms from "./assets/bulk-actions-hcms.png";
import bulkActionsPb from "./assets/bulk-actions-pb.png";
import bulkActionsFm from "./assets/bulk-actions-fm.png";
import preDefinedBlocks from "./assets/pre-defined-blocks.png";
import auditLogs from "./assets/audit-logs.png";
import fnShiftSelect from "./assets/fm-shift-select.mp4";

<GithubRelease version={"5.38.0"} />

## Breaking Changes

### Removal of `WEBINY_ELASTICSEARCH_INDEX_LOCALE` Environment Variable ([#3672](https://github.com/webiny/webiny-js/pull/3672))

Used only with [Amazon DynamoDB + Amazon ElasticSearch](/docs/architecture/introduction#different-database-setups) database setup, the `WEBINY_ELASTICSEARCH_INDEX_LOCALE` environment variable would allow users to choose whether they want their ElasticSearch indexes to include a locale code in their names.

Today, there is really no reason to not set this to `true`, so we've decided to hide this environment variable altogether, and always include the locale code in the index name.

We're aware that this might cause issues for existing projects, so we've added [a couple of notes](/docs/release-notes/5.38.0/upgrade-guide#4-webiny-elasticsearch-index-locale-environment-variable) in the migration guide to help you with the migration process.

## Security

### Advanced Access Control Layer (AACL) - Folder Level Permissions ([#3610](https://github.com/webiny/webiny-js/pull/3610))

With this release, we're expanding Webiny's [Advanced Access Control Layer (AACL)](/docs/release-notes/5.37.0/changelog#introducing-advanced-access-control-layer-aacl-2576), by introducing a brand-new feature called **Folder Level Permissions**.

<Image src={flp} alt={"Folder Level Permissions"} shadow={false} />

With this feature, users are now able to define access permissions on a folder level, across the three main Webiny applications: **Headless CMS**, **File Manager**, and **Page Builder**.

More specifically, users that are designated as **folder owners** can now define which users can access which folders and, subsequently, the content within those folders.

Upon doing that, the folder owner also defines the level of access that the user will receive:

- **Viewer** - users can view content, but not modify it
- **Editor** - users can view and modify content
- **Owner** - users can edit and manage content permissions

Once defined, besides the selected folder, the access permissions are also inherited by all subfolders and the content within. In other words, if a user has access to a folder, they will also have access to all subfolders and the content within.

We believe that this feature will be especially useful for teams that are working on large projects, where the content is organized into multiple folders, and where the content is managed by multiple users.

To learn more, please refer to the newly added [Folder Level Permissions](/docs/enterprise/aacl/folder-level-permissions) article.

### Audit Logs ([#3454](https://github.com/webiny/webiny-js/pull/3454) [#3580](https://github.com/webiny/webiny-js/pull/3580) [#3644](https://github.com/webiny/webiny-js/pull/3644) [#3645](https://github.com/webiny/webiny-js/pull/3645))

With this release, for our enterprise users, we are introducing Audit Logs.

In the Audit Logs our enterprise users can track what exactly was done, who did it and when.
Audit Logs contain the changed record values so that users can easily identify which parts of the record have been changed.

<Image src={auditLogs} alt={"Audit Logs"} />

## Headless CMS

### Support for Nested Dynamic Zones, Objects, and Ref Fields ([#3471](https://github.com/webiny/webiny-js/pull/3471))

Up until now, the Dynamic Zone field had a few very important limitations: it could only be used as a top-level field, it did not support nesting, and it did not support `ref` fields. With this release, things change! 😎

We now support nesting of any field, at any depth, without any **structural** limitations. Dynamic zones can also be nested within each other, meaning your dynamic templates are now as powerful as they get.

<Image src={nestedFields} alt={"Nesting of Dynamic Zones, Objects and Ref Fields"} />

However, there are still limitations on filtering: _you cannot filter content entries based on the fields within the dynamic zone templates_.

Still, this unlocks near limitless data modeling, and can be an indispensable tool when combined with a site generator like Gatsby or Next.js, where your entire dynamic zone templates can be mapped to React components designed to render your content.

### Introducing Advanced Search Filter Builder ([#3604](https://github.com/webiny/webiny-js/pull/3604))

With this release, we are introducing our Advanced Search Filter Builder, specifically designed to improve the search functionality of our Headless CMS application.

With this new feature, users can now easily create complex search queries without requiring the assistance of a developer. The Advanced Search Filter Builder supports grouping conditions and defining the relationship between individual conditions or groups, making it a powerful tool for improving search accuracy and efficiency.

Users can create custom search queries based on model-defined fields that are one of the following types:

- `boolean`
- `datetime`
- `number`
- `text`

While this release covers specific field types, we are committed to ongoing development and will expand this functionality to cover more field types, making the search experience even better.

Additionally, the filter includes default fields such as `status`, `createdOn`, and `savedOn`, besides the model-defined ones.

Moreover, users can save and manage their search filters, making it easier to access and reuse them in the future.

This feature promises to simplify user interaction with entries, streamline workflows, and optimise content management within the application.

<Image src={advancedSearchFilters} alt={"Headless CMS Advanced Search Filter Builder"} />

### Partial Saving of the CMS Entry ([#3579](https://github.com/webiny/webiny-js/pull/3579))

With this release, we are introducing partial saving of the CMS entry. This means that users can save drafts that are not fully populated.

For example, if a user has a required field, they can save the entry without filling in the required field. This is particularly useful for large entries, where the user may want to save the entry several times during the writing process, without having to fill in all the required fields.
This is done by skipping the required validator if the field is not populated. As soon as field is populated, all other validators will be executed on the field.

The required validator is enforced when the user attempts to publish the entry, so there is no way to publish an entry without filling in all the required fields.

## Page Builder

### Over 100+ pre-built visual blocks for Webiny Page Builder

We are exicited to launch a free library of over 100+ professionally designed, fully responsive page builder blocks that you can use in your Webiny Page Builder. These new pre-built visual blocks will empower you to effortlessly create stunning pages in no time. To get started with these new blocks, please visit [blocks.webiny.com](https://blocks.webiny.com). For more details, you can refer to the full announcement [here](https://www.webiny.com/blog/pre-built-visual-blocks-for-webiny-page-builder).

<ExternalLink href={`https://blocks.webiny.com/`} icon={false}>
  <Image src={preDefinedBlocks} alt={"Pre-Built Visual Blocks for Webiny Page Builder"} />
</ExternalLink>

### Improved Previewing of Blocks ([#3329](https://github.com/webiny/webiny-js/pull/3329))

We've improved the way blocks are previewed in the Page Builder app. Previously, whenever a block was saved, a preview image was generated and stored in the database. This image was then used to display the block preview in the Page Builder app.

However, this approach had a few drawbacks, the most important one being that the preview image was not always up-to-date with the actual block content.

So, with this release, we've changed the way blocks are previewed. Now, instead of generating and storing a preview image, we're using the actual block content to display the preview. This way, the preview is always up-to-date with the actual block content.

<Image src={blockPreview} alt={"Improved Previewing of Blocks"} />

## Form Builder

### Multi-Step Forms

With this release, we're introducing support for multi-step forms in the Form Builder app.

<Image src={multiStepForms} alt={"Multi-step Forms"} />

As the name suggests, multi-step forms allow users to break down long forms into multiple steps. This way, users can create more complex forms, while still keeping them simple and easy to fill out.

In terms of backward compatibility, note that, upon redeploying your Webiny project, a data migration will be triggered, which will automatically convert all existing forms into multi-step forms. In essence, in this process, all form fields from existing forms will be moved into the first step of the form.

In terms of manual steps, note that you will still need to update a couple of form layout React components, which will partially be handled by the project upgrade script that's included in this release. For more information, please refer to the [Upgrade Guide](/docs/release-notes/5.38.0/upgrade-guide#3-form-builder-multi-step-forms).

[//]: # "## Deployments"
[//]: #
[//]: # "### Introducing Support for Amazon OpenSearch ([#3612](https://github.com/webiny/webiny-js/pull/3612))"
[//]: #
[//]: # "With this release, we're introducing support for [Amazon OpenSearch](https://aws.amazon.com/opensearch-service/), a managed service that replaces the popular [Amazon Elasticsearch Service](https://aws.amazon.com/elasticsearch-service/)."
[//]: #
[//]: # "Upon creating a new Webiny project, users will no longer be able to select the DynamoDB + Elasticsearch database setup. Instead, they will be able to select the DynamoDB + OpenSearch setup."
[//]: #
[//]: # "Note that all existing projects that are using the Amazon DynamoDB + Amazon Elasticsearch database setup will continue to work as before. Users' projects will still be able to use the Amazon Elasticsearch service, and they will not be forced to migrate to Amazon OpenSearch."
[//]: #
[//]: # "Note that, at the moment, we do not provide a migration path from Amazon Elasticsearch to Amazon OpenSearch for existing projects. Drop us a message in our [Community Slack](https://www.webiny.com/slack) if this is something you'd like to see in the future."

## File Manager

### Multiple Files Selection Using the Shift Key ([#3675](https://github.com/webiny/webiny-js/pull/3657))

Using the Shift key, users can now select multiple files while browsing their files.

<Video src={fnShiftSelect} controls={false} />

The above video demonstrates the feature when using the grid layout, but note that the same behaviour is also available when using the list layout.

## Bulk actions

### Introducing Bulk Actions for Headless CMS, Page Builder and File Manager ([#3501](https://github.com/webiny/webiny-js/pull/3501) [#3530](https://github.com/webiny/webiny-js/pull/3530) [#3608](https://github.com/webiny/webiny-js/pull/3608))

In this release, we are introducing Bulk Actions to our main applications. With this feature, users can now select multiple entries at once and perform various actions on the entire selection.

Customisation is another integral part of this feature. Developers can tailor the default actions to meet specific needs, and a comprehensive guide is available to help them refine the functionalities according to user feedback and demands.

This enhancement promises to redefine user interaction with entries, making routine operations easier and providing greater control to content editors.

<Gallery>
  <Image src={bulkActionsHcms} alt={`Bulk Actions for Headless CMS`} />
  <Image src={bulkActionsPb} alt={`Bulk Actions for Page Builder`} />
  <Image src={bulkActionsFm} alt={`Bulk Actions for File Manager`} />
</Gallery>

## Lexical Tooling

### Lexical Parser and Converter ([#3626](https://github.com/webiny/webiny-js/pull/3626))

We've developed some useful tools to help you work with the Lexical editor, which we use in the Page Builder and Headless CMS.

Often, you need to import or export data to and from Webiny, but you only have, or need, HTML at your disposal. To help you convert HTML to Lexical, or Lexical to HTML, we've introduced a package `@webiny/lexical-converter`, to facilitate the process. It contains utilities to help you with bidirectional conversion of Lexical data.

<Gallery>
  <Image
    src={lexicalFlat}
    alt={`The "flatten" utility produces an array of objects containing the Lexical node and its HTML representation.`}
  />
  <Image
    src={lexicalCustomTransform}
    alt={"Example of processing the flattened output to further shape your data."}
  />
  <Image
    src={lexicalToHtml}
    alt={`The "toHtml" utility converts Lexical data into a single HTML string.`}
  />
</Gallery>

## Development

### Revamped Watch Command Output

We've revamped the output for the [`webiny watch`](/docs/core-development-concepts/basics/watch-command) command.

<Image src={watchCmd} alt={"Revamped Watch Command Output"} />

When compared to the previous, the new output is much more compact and easier to read. Instead of using three panes to display the build, deploy, and application runtime logs, the command is now printing all logs directly into the terminal.

Furthermore, the deployment logs have been simplified, only showing basic information about the deployment process. Only in case of an error, the full deployment logs will be printed to the user.

### Node.js v16 ([#3504](https://github.com/webiny/webiny-js/pull/3504))

Because of the end of life for the Node.js v14, we have upgraded version of the Node.js for the system to the v16.
AWS Lambdas will get deployed, with the usage of the new version, on your first deploy after the system upgrade.

Note that, if you have used [puppeteer-core](https://www.npmjs.com/package/puppeteer-core) in your project, the version of it has also changed, so update the package and update your code accordingly.

This is a step towards the Node.js v18, which will probably go out with the next version of Webiny.
