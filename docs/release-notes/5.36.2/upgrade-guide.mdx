---
id: aafea9a0
title: Upgrade from 5.36.x to 5.36.2
description: Learn how to upgrade Webiny from 5.36.x to 5.36.2.
---

import { Alert } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.36.x to 5.36.2

</Alert>

<Alert type="info">

Make sure to check out the [5.36.2 changelog](/docs/release-notes/5.36.2/changelog) to get familiar with the changes introduced in this release.

</Alert>

## 1. Upgrade Webiny Packages

Upgrade all `@webiny/*` packages by running the following command:

```bash
yarn up "@webiny/*@5.36.2"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return `5.36.2`.

## 2. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

<Alert type="warning">

Make sure you deploy the entire system using the command shown above! You have to deploy all apps before using the system. Partial deploys may cause the upgrade to be applied incorrectly.

</Alert>

<Alert type="warning">

As stated in the [Upgrade Webiny](/docs/release-notes/upgrade-webiny#pre-production-environments-first) section, we recommend that you first deploy your changes into one of your pre-production environments, like `dev` or `staging`.

</Alert>

<Alert type="info">

Learn more about different deployment environments in the [CI/CD / Environments](/docs/core-development-concepts/ci-cd/environments) key topic.

</Alert>
