---
id: changelog
title: Webiny 5.36.2 Changelog
description: See what's new in Webiny version 5.36.2.
---

import aboutCommand from "./assets/aboutCommand.png";
import clickToAdd from "./assets/clickToAdd.png";

<GithubRelease version={"5.36.2"} />

## Page Builder

### Multiple Fixes ([#3349](https://github.com/webiny/webiny-js/pull/3349))

#### Enabled `target` Property On the `PbEditorPageElementPlugin` Plugin

The [`target`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L507) property on the [`PbEditorPageElementPlugin`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L500-L540) plugin enables users to define into which existing elements the element that's being dragged can be dropped. Prior to this release, this didn't work, because the property essentially wasn't taken into consideration in the internal code.

From now on, users can now use the [`target`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L507) property on the [`PbEditorPageElementPlugin`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L500-L540) plugin to explicitly list elements into which the dragged element can be dropped.

#### Introduced the `canReceiveChildren` Property On the `PbEditorPageElementPlugin` Plugin

Prior to this release, if a user wanted to mark a page element as an element that can receive additional children elements (for example via drag and drop), they would need to define the [`onReceived`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L519) callback via the [`PbEditorPageElementPlugin`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L500-L540) plugin.

Since this showed to be a common use-case, we decided to introduce a new property called [`canReceiveChildren`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L517) on the [`PbEditorPageElementPlugin`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L500-L540) plugin, which enables users to mark an element as an element that can receive additional children elements, without the need to define the [`onReceived`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L519) callback.

#### `target` Is Now Also Considered When Adding Elements By Clicking In the Left Sidebar

Prior to this release, when adding an element onto a page via the **Click to Add** feature (vs. just dragging and dropping), the [`target`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L507) property defined on the [`PbEditorPageElementPlugin`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L500-L540) plugin would not be taken into consideration.

<Image src={clickToAdd} alt={"The Click to Add button in the left sidebar."} />

This has now been taken addressed. No matter if the user drags and drops the page element onto an existing element, or adds it via the mentioned **Click to Add** feature, the [`target`](https://github.com/webiny/webiny-js/blob/dev/packages/app-page-builder/src/types.ts#L507) property will be considered. In other words, the page element won't get added into the existing element, no matter which approach was used.

#### Paragraph and Heading Page Elements - New Text Align Options ([#3304](https://github.com/webiny/webiny-js/pull/3304))

In the page editor, users are now able to perform the following text alignment-related actions when using the Paragraph and Heading page elements:

- alignment actions: align left, align right, align center, and justify center
- indent actions: indent and outdent text

Note that, previously, these actions were performed via the right side menu, which has now been removed.

## File Manager

### Migration Failure in Locale Without File Entries (DynamoDB + Elasticsearch Setup) ([#3337](https://github.com/webiny/webiny-js/pull/3337))

With 5.36.0 version we introduced the [Advanced Content Organisation for File Manager](https://www.webiny.com/docs/release-notes/5.36.0/changelog#introducing-advanced-content-organization-for-file-manager-3312): in order to work a data migration was needed and executed via the [Data Migration Lambda Function](https://www.webiny.com/docs/release-notes/5.35.0/changelog#data-migration-lambda-function-3079-3150). When dealing with a locale that lacked file entries, the migration process would fail.

We have resolved this migration bug. The fix ensures that in cases where a locale does not have any file entries (and the consequent empty Elasticsearch index), the migration process no longer encounters errors and proceeds smoothly.

<Alert type="warning">

The fix specifically targets instances where the migration failed due to this particular scenario. In case you experienced a migration failure, this has been marked as failed and will be re-run during Webiny project deploy.

</Alert>

## Documentation

### New "Page Element With Child Elements" Example

On the back of the improvements listed in the [Page Builder](http://localhost:8000/docs/release-notes/5.36.2/changelog#page-builder) section above, we've created the [Page Element With Child Elements](https://github.com/webiny/webiny-examples/tree/master/page-builder/element-with-child-elements) example, which demonstrates how to create a page element that can receive additional child elements. Be sure to check out the example, as it also contains a couple of bonuses.

## Development

### Fully Destroy Your Project With the Improved `webiny destroy` Command ([#3353](https://github.com/webiny/webiny-js/pull/3353))

Prior to this release, in order to fully destroy a deployed Webiny project, users would need to destroy all four project applications (**Website**, **Admin**, **API**, and **Core**), by sequentially running four separate [`webiny destroy`](/docs/infrastructure/basics/destroy-cloud-infrastructure#the-destroy-command) commands.

<br />
<br />

And although this was made a bit easier with the ability to [specify multiple applications](/docs/release-notes/5.34.1/changelog#webiny-cli-run-commands-against-multiple-apps-2974) in a single command execution, it was still not ideal because users would need to remember to destroy all four applications, in the correct order.

<br />
<br />

So, with this release, users can now run the [`webiny destroy`](/docs/infrastructure/basics/destroy-cloud-infrastructure) command without specifying the project application. This will destroy all four applications in the correct order, and in a single command execution. In other words, the command will fully destroy your Webiny project, deployed into the specified environment.

For example:

```bash
yarn webiny destroy --env dev --confirm-destroy-env dev
```

Note that, upon doing this, the command will ask for a confirmation of the environment you wish to destroy, via the `--confirm-destroy-env` flag.

### The `webiny about` Command ([#3351](https://github.com/webiny/webiny-js/pull/3351))

With this release, we're introducing the `webiny about` command, which displays a plethora of information about the Webiny project you're currently working on.

<Image src={aboutCommand} alt={"The output of the `webiny about` command."} />

In some cases, the output of this command might help us troubleshoot your issues faster, so please make sure to include the output of this command when you're reporting an issue.
