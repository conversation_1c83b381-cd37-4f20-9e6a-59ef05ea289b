---
id: aafea9c2
title: Upgrade from 5.23.1 to 5.24.0
description: Learn how to upgrade Webiny from 5.23.1 to 5.24.0.
---

import { Al<PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.23.1 to 5.24.0

</Alert>

<Alert type="info">

Make sure to check out the [5.24.0 changelog](/docs/release-notes/5.24.0/changelog) to get familiar with the changes introduced in this release.

</Alert>

## 1. Upgrade Webiny Packages

Upgrade all `@webiny/*` packages by running the following command:

```bash
yarn up "@webiny/*@5.24.0"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return `5.24.0`.

<Alert type="warning">

As we mentioned in the changelog, we're in the process of tightening the Typescript rules, making it stricter, and improving the code quality. Because of that, there is a possibility of some build issues in your existing projects, if you have a non-default setup or custom plugins.

To minimize the impact of TS strictness, we are _NOT CHANGING_ your existing `tsconfig.json` configuration. If you want to use stricter rules, you'll have to enable them yourself.

</Alert>

## 2. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

<Alert type="warning">

As stated in the [Upgrade Webiny](/docs/release-notes/upgrade-webiny#pre-production-environments-first) section, we recommend that you first deploy your changes into one of your pre-production environments, like `dev` or `staging`.

</Alert>

<Alert type="info">

Learn more about different deployment environments in the [CI/CD / Environments](/docs/core-development-concepts/ci-cd/environments) key topic.

</Alert>
