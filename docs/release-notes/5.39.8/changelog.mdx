---
id: ca039c85
title: Webiny 5.39.8 Changelog
description: See what's new in Webiny version 5.39.8.
---

import { GithubRelease } from "@/components/GithubRelease";

<GithubRelease version={"5.39.8"} />

## Page Builder

### Fix Page Import and Export on Non-Default Tenants and Locales ([#4170](https://github.com/webiny/webiny-js/pull/4170))

When exporting pages on non-default tenant and non-default locale, background tasks were being executed using the wrong tenant and locale context, resulting in various issues with data loading. This has now been addressed, and our background tasks are now correctly setting up the system context.

## Asset Delivery

### Set Locale Header Using the Correct Format ([#4170](https://github.com/webiny/webiny-js/pull/4170))

When an asset is requested from our Asset Delivery service, and the asset is successfully resolved, we bootstrap the system using the resolved asset's tenant and locale information. We've received reports that, in multi-locale systems, assets are not being served correctly when using assets from non-default locales.

This has now been fixed, and the resolved asset's locale is now passed to the system in the correct format, allowing the system to perform data queries for the correct locale.
