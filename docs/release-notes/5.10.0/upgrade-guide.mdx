---
id: aafea9e4
title: Upgrade from 5.9.0 to 5.10.0
description: Learn how to upgrade Webiny from 5.9.0 to 5.10.0.
---

import { <PERSON><PERSON> } from "@/components/Alert";

<Alert type="success" title="What you’ll learn">

- how to upgrade Webiny from 5.9.0 to 5.10.0

</Alert>

<Alert type="danger">

Before continuing, make sure to take the necessary precautions, listed in the [Overview](/docs/release-notes/upgrade-webiny#-precaution-measures) section.

</Alert>

<Alert type="info">

Make sure to check out the [5.10.0 changelog](/docs/release-notes/5.10.0/changelog) to get familiar with all the changes introduced in this release.

</Alert>

## 1. Project Preparation

First, we need to prepare your project for the upgrade process.
We renamed our shared Elasticsearch package from `api-plugin-elastic-search-client` to `api-elasticsearch`, and we need to update package.json files that are using it, before attempting to upgrade packages using `yarn`.

In your project root, run the following:

```bash
npx https://gist.github.com/brunozoric/96e66c47abc6769947034fe707138cda
```

## 2. Upgrade Webiny Packages

We're now ready to upgrade all `@webiny/*` packages! Run the following command:

```bash
yarn up "@webiny/*@5.10.0"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return `5.10.0`.

<Alert type="info">

Before moving on, make sure you commit all your changes.

</Alert>

## 3. Run the Upgrade Command

Now let's run the project upgrade:

```bash
yarn webiny upgrade 5.10.0
```

The upgrade script will make a couple of changes to your existing **API** project application's code (located within the `api` folder). Once the upgrade command has finished, you can run the [`git status`](https://git-scm.com/docs/git-status) command to see all changes that the command performed.

## 4. Deploy Your Project

Finally, proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

<Alert type="warning">

As stated in the [Upgrade Webiny](/docs/release-notes/upgrade-webiny#pre-production-environments-first) section, we recommend that you first deploy your changes into one of your pre-production environments, like `dev` or `staging`.

</Alert>

<Alert type="success">

Learn more about different deployment environments in the [CI/CD / Environments](/docs/core-development-concepts/ci-cd/environments) key topic.

</Alert>
