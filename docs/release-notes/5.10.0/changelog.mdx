---
id: aafea9e1
title: Webiny 5.10.0 Changelog
description: See what's new in Webiny version 5.10.0.
---

import { <PERSON><PERSON> } from "@/components/Alert";

# Changes

This document highlights the most important fixes, improvements, and features, that were introduced in Webiny `5.10.0`.

<Alert type="info" title="How To Upgrade?">

Please check the [Webiny 5.10.0 migration guide](/docs/release-notes/5.10.0/upgrade-guide) for the upgrade steps.

</Alert>

## Infrastructure

### Node 12 Runtime and a New Sharp Layer ([#1738](https://github.com/webiny/webiny-js/pull/1738))

With AWS deprecating Node 10 Lambda runtime, we've also upgraded all of our Lambda functions to use Node 12 (you may have noticed that File Manager Lambdas were using node 10). With that, we also released a new version of Sharp layer, which is twice smaller, and also supports Node 14, so we have the peace of mind for the near future.

The upgrade process will take care of the existing projects and will link your existing Lambdas with the new Sharp layer, and switch to Node 12.

## Page Builder

### Website Fixes ([#1746](https://github.com/webiny/webiny-js/pull/1746), [#1752](https://github.com/webiny/webiny-js/pull/1752), [#1755](https://github.com/webiny/webiny-js/pull/1755))

We discovered a couple of issues that would sometimes occur while the user is navigating through the public website.

First of all, a really easy-to-miss Apollo cache-related issue would sometimes cause the pages to stop being shown correctly, forcing the user to refresh the page. We believe the changes we made in [#1746](https://github.com/webiny/webiny-js/pull/1746/files) will fully address this.

We should also mention that we've managed to revisit the existing Pages List page element and that all of the community-reported issues were resolved. For more information, please check linked pull requests.

### Preloading and Caching Improvements ([#1753](https://github.com/webiny/webiny-js/pull/1753))

The following improvements should improve the page load times for pages created with [Webiny Page Builder](https://www.webiny.com/serverless-app/page-builder).

#### Asset Preloading

Once a page has been published, in the static page generation process that follows, all links to JS and CSS files will now utilize the [`rel="preload"`](https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types/preload) attribute :

> The preload value of the `<link>` element's rel attribute lets you declare fetch requests in the HTML's `<head>`, specifying resources that your page will need very soon, which you want to start loading early in the page lifecycle, before browsers' main rendering machinery kicks in. This ensures they are available earlier and are less likely to block the page's render, improving performance.

Except redeploying your project, no special steps are needed in order to receive this improvement.

To double-check whether this new improvement is working as expected, open the source code of any published page in your browser. In the `<head>` section, you should be able to see the new `rel="preload"` attributes, for example:

```diff-js
<!DOCTYPE html>
<html lang="en">
  <head>
-    <link as="style" rel="preload" href="/static/css/2.1edd21d9.chunk.css" />
-    <link as="style" rel="preload" href="/static/css/main.30bba61e.chunk.css" />
-    <link as="script" rel="preload" href="/static/js/2.229acfe0.chunk.js" />
-    <link as="script" rel="preload" href="/static/js/main.680e9d72.chunk.js" />
    <meta charset="utf-8" />
    (...)
```

#### Added Missing `Cache-Control` Response Headers

Upon visiting any published page, all of the static JS, CSS, and images that are part of the Website React application's production build, will now be served with proper [`Cache-Control`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cache-Control) headers. Ultimately, once downloaded, these files will now be kept in browser's cache, making subsequent website renders noticeably faster.

## Development

### Admin Area React Application - Reduced Deployment Time ([#1740](https://github.com/webiny/webiny-js/pull/1740))

Prior to this release, every time we'd deploy the **Admin Area** project application ([`apps/admin`](https://github.com/webiny/webiny-js/tree/v5.10.0-beta.0/packages/cwp-template-aws/template/apps/admin/)), all of the React application's production build files (JS, CSS, images) would be re-uploaded from scratch, even if we didn't perform any changes in our application code.

From now on, files whose content didn't change simply won't be uploaded. This will significantly speed up the deployment of the **Admin Area** project application.

<Alert type="info" title="Project Applications">

Learn more about project applications and project organization in general, in the [Project Applications and Packages](/docs/core-development-concepts/project-organization/project-applications-and-packages) key topic.

</Alert>

### Improved Existing Scaffolds ([#1739](https://github.com/webiny/webiny-js/pull/1739))

A couple of minor fixes and improvements were made to the [scaffolding utilities](/docs/core-development-concepts/scaffolding/introduction), introduced in the previous [Webiny 5.9.0](/docs/release-notes/5.9.0/changelog) release.

For example, if you already had the chance to use the [Extend GraphQL API](/docs/core-development-concepts/scaffolding/extend-graphql-api) scaffold, you might notice that the default location in which the application code is generated has been changed:

```bash
# Previously, all of the application code would be generated within the following path:
api/code/graphql/src/plugins/scaffolds/graphql/carManufacturers

# This is the new path, without the "graphql" folder:
api/code/graphql/src/plugins/scaffolds/carManufacturers
```

The same change was applied to the [Extend Admin Area](/docs/admin-area/new-app-tutorial/scaffolding) scaffold, where the redundant `admin` folder was removed:

```bash
# Previously, all of the application code would be generated within the following path:
apps/admin/code/src/plugins/scaffolds/admin/carManufacturers

# This is the new path, without the "graphql" folder:
apps/admin/code/src/plugins/scaffolds/carManufacturers
```

For other related changes, please see the linked pull request.
