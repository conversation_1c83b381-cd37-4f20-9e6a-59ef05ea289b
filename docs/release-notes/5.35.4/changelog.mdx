---
id: changelog
title: Webiny 5.35.4 Changelog
description: See what's new in Webiny version 5.35.4.
---

## Migration

### Failure in Case of Locale without File or Page Entries (DynamoDB + Elasticsearch setup) ([849c103](https://github.com/webiny/webiny-js/commit/849c103858fa6eb5a5c9b17630d6f7c94b006515))

With [5.35.0 version](https://www.webiny.com/docs/release-notes/5.35.0/changelog) we introduced several features that needed data migration: this is executed via the [Data Migration Lambda Function](https://www.webiny.com/docs/release-notes/5.35.0/changelog#data-migration-lambda-function-3079-3150). When dealing with a locale that lacked file or page entries, the migration process would fail.

We have resolved this migration bug. The fix ensures that in cases where a locale does not have any page or file entries (and the consequent empty Elasticsearch indexes), the migration process no longer encounters errors and proceeds smoothly.

<Alert type="warning">

The fix specifically targets instances where the migration failed due to this particular scenario. In case you experienced a migration failure, this has been marked as failed and will be re-run during Webiny project deploy.

</Alert>
