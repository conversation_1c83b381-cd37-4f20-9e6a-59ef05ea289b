---
id: e9269552
title: Webiny 5.41.4 Changelog
description: See what's new in Webiny version 5.41.4
---

<GithubRelease version={"5.41.4"} />

## Security

### Cognito Federation: Fixed Handling of Identity's Groups and Teams Properties ([#4418](https://github.com/webiny/webiny-js/pull/4418))

When using [Cognito Federation](https://www.webiny.com/docs/enterprise/cognito-federation), via the `getIdentity` callback, developers have the ability to customize the identity object.

This is useful when you need to add additional properties to the identity object, such as groups or teams, which can then be used for authorization purposes.

But, there was an issue with how the identity's groups and teams-related properties were handled. Previously, only the `group` property was handled. Now, all of the following properties are handled:

- `group`
- `groups`
- `team`
- `teams`

This change ensures that the identity object is correctly processed, and that the groups and teams properties are correctly loaded and used for authorization purposes.

### Fixed Loading of Teams When Using 3rd Party IdPs ([#4419](https://github.com/webiny/webiny-js/pull/4419))([#4420](https://github.com/webiny/webiny-js/pull/4420))

When using 3rd party IdPs, such as [Okta](/docs/enterprise/okta-integration) or [Cognito Federation](https://www.webiny.com/docs/enterprise/cognito-federation), and using the `getIdentity` callback to customize the identity object, assigning teams to the identity object was not working correctly.

More specifically, resolving security roles from teams that were created via the Admin app was not working as expected. On the other hand, teams that were define programmatically were working as expected.

This issue has been fixed, and now, when using the `getIdentity` callback, teams are correctly loaded and assigned to the identity object.

## Page Builder

### New "Page Builder Element" Extension ([#4342](https://github.com/webiny/webiny-js/pull/4342))

We've introduced a new type of an extension called **Page Builder element** extension.

Once run, the extension will make sure a users ends up with all of the required files and configurations for a new Page Builder element plugin. This will make it easier for users to start developing new Page Builder elements.

The extension can be accessed like any other extension, via the [`webiny extension`](/docs/core-development-concepts/basics/webiny-cli#yarn-webiny-extension) command, by then selecting **Page Builder Element** from the list of all available extensions.

### New Page Element Plugins ([#4340](https://github.com/webiny/webiny-js/pull/4340))

In order to be more in line with the new [Page Builder element extension](/docs/release-notes/5.41.4/changelog#new-page-builder-element-extension-4342), and also some of the best practices we've been pushing lately, we've updated the existing Page Builder element plugins. Essentially, instead of using plain JavaScript objects, users will now do the same via React components.

Note that the previous way of creating Page Builder element plugins will still work, but moving on, we recommend using the new way.

<Alert type={"info"}>

Our existing [Create a Custom Page Element](/docs/page-builder/extending/create-a-page-element) article has been
updated to reflect these changes.

</Alert>

### Introducing the `useLoader` React Hook ([#4424](https://github.com/webiny/webiny-js/pull/4424)) ([#4435](https://github.com/webiny/webiny-js/pull/4435)) ([#4447](https://github.com/webiny/webiny-js/pull/4447))

We've introduced a brand new `useLoader` React hook, which is a essentially utility a hook that helps you load data in custom page elements.

The main purpose of this hook is to ensure that, once the data is loaded in the page prerendering process, it is captured and properly cached. This way, the loaded data is immediately available when the published page is delivered to the end user, which improves page performance and user experience.

For more information, please refer to the [Loading Data in Page Elements](/docs/page-builder/extending/loading-data-in-page-elements) guide.

### Page Prerendering - Preloading of Fonts ([#4450](https://github.com/webiny/webiny-js/pull/4450))

We've improved the page prerendering process to enhance how fonts are loaded.

During prerendering, we now capture all font requests and automatically generate corresponding `<link>` HTML tags in the document's `<head>` to preload the fonts. For example:

```html
<link 
  rel="preload" 
  href="https://fonts.gstatic..." 
  as="font" 
  type="font/woff2"
  crossorigin="anonymous">
```

Ultimately, this ensures that fonts are loaded before the page is displayed, preventing the dreaded "flash of unstyled text" (FOUT) and improving the overall user experience.

<Alert title={"In Case You Missed It"}>

  Pages created with the Webiny' Page Builder are prerendered when published. This means that the page is generated as a static HTML file and served to the end user. This is done to ensure the best possible performance and SEO.

</Alert>

## Form Builder

### Form Builder - Create Settings Entry When New Locale Is Created ([#4446](https://github.com/webiny/webiny-js/pull/4446))

Prior to this release, when a new locale was created, Form Builder application's settings entry was not created for the new locale. This resulted in users not being able to change the form's settings for the new locale.

This issue has been fixed, and now, when a new locale is created, the Form Builder application's settings entry is also created for the new locale.

## Development

### Webiny CLI - Improved Developer Experience ([#4455](https://github.com/webiny/webiny-js/pull/4455))

We've made a couple improvements to the Webiny CLI to enhance the developer experience.

For more information, please refer to the linked pull request.
