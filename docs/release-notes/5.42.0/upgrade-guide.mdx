---
id: bad5c115
title: Upgrade from 5.41.x to 5.42.0
description: Learn how to upgrade Webiny from 5.41.x to 5.42.0.
---



import {<PERSON><PERSON>} from "@/components/Alert";
import {AdditionalNotes} from "@/components/upgrade/AdditionalNotes";

<Alert type="success" title="What you’ll learn">

  - how to upgrade Webiny from 5.41.x to 5.42.x

</Alert>

<Alert type="info">

  Make sure to check out the [5.42.0 changelog](./changelog) to get familiar with the changes introduced in this
  release.

</Alert>

## Step-by-Step Guide

### 1. Upgrade Webiny Packages

Upgrade all Webiny NPM packages by running the following command:

```bash
yarn up "@webiny/*@5.42.2"
```

Once the upgrade has finished, running the `yarn webiny --version` command in your terminal should return **5.42.2**.

### 2. Upgrade Yarn to 4.6.0

<Alert type="warning">
  
  This is really important step, as the project upgrade will not work without the correct Yarn version.
  
</Alert>

We have updated the `Yarn` version to `4.6.0` and we cannot update it automatically in the project, so you need to do it manually.

To upgrade Yarn to version `4.6.0`, run the following command:

```bash
yarn set version 4.6.0 && yarn install
````

This will effectively set your project Yarn to v4.6.0 and install all the packages with that Yarn version.

### 3. Run the Upgrade Command

The next step is to run the project upgrade:

```bash
yarn webiny upgrade
```

### 4. Breaking Changes❗
This release contains a couple of breaking changes that might affect your project.

We have updated a lot of dependencies, and we are syncing Webiny dependency versions with your project, so they might cause some issues.

#### 4.1. If You Have `@editorjs/*` Packages In Your Project

If there are packages starting with `@editorjs/*` in your project, they will probably produce an error during Typescript compile time.

We have added a code mode in our update script, but if there are any leftovers, please sort them out before deploying.
If there are any issues, it is due to the `// ts-expect-error` comment in the code.

### Build Your Project

After the upgrade, build your **API**, **Admin**, and **Website** project applications:

```bash
yarn webiny build api,admin,website --env YOUR_ENVIRONMENT
```

<Alert type="info">

  If you have issues with building the project, something related to `@babel/*` packages, please delete your `node_modules` directory, run `yarn` and try building the project again.

</Alert>

### 6. Deploy Your Project

Proceed by redeploying your Webiny project:

```bash
# Execute in your project root.
yarn webiny deploy --env {environment}
```

<AdditionalNotes />
