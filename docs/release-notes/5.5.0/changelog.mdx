---
id: aafea9ec
title: Webiny 5.5.0 Changelog
description: See what's new in Webiny version 5.5.0.
---

import { <PERSON>ert } from "@/components/Alert";

# Changes

This document highlights the most important fixes, improvements, and features, that were introduced in Webiny `5.5.0`.

<Alert type="info" title="How To Upgrade?">

Please check the [Webiny 5.5.0 migration guide](/docs/release-notes/5.5.0/upgrade-guide) for the upgrade steps.

</Alert>

## Webiny CLI

### Brand New `webiny watch` Command ([#1540](https://github.com/webiny/webiny-js/pull/1540))

<Alert type="success">

We also wrote about the new `webiny watch` command on our [blog](https://www.webiny.com/blog/webiny-v5.5.0-watch)! It also contains a live demo, so we encourage you to check it out.

</Alert>

The brand new `webiny watch` command brings local development experience to a whole new level!

#### How We Currently Do It

Let’s say you wanted to expand the default GraphQL API, that’s deployed as part of the [API](/docs/architecture/api/overview) project application. At the moment, in order to do that, you’d have to do the following:

- start a watch session on relevant packages using the `webiny workspaces run` command
- make your code changes
- deploy them using the [`yarn webiny deploy api --env {your-environment}`](#) command
- repeat

This is not ideal for a couple of reasons:

- you're required to manually execute the `webiny deploy` command all the time
- the `webiny deploy` command always builds _all of the project application's packages_, which can be really slow
- apart from running the `webiny deploy`, you also need to separately run the `webiny workspaces run` command we mentioned before, in order to establish watch session on packages you're working on

#### The New Way

From now on, you'll be able to just run the `yarn webiny watch api --env {your-environment}` command and start coding! The command will automatically start watching all of the relevant packages (no matter where they’re located in your Webiny project) and also re-deploy only relevant cloud infrastructure resources (e.g. a single Lambda function), as needed.

![Webiny CLI - The New watch Command](./watch-command-webiny.gif)

#### Logs

And there's more! By simply appending the `--logs` argument to the `webiny watch` command, you also gain the ability to stream all logs from your application code (Lambda functions), directly to your terminal:

![Webiny CLI - Streaming Logs To Your Terminal](./watch-command-webiny-logs.png)

This basically means **no more digging** through [Amazon CloudWatch](https://aws.amazon.com/cloudwatch/) logs while developing or debugging your backend application code.

<Alert type="info" title="Learn More">

To learn more about the new `webiny watch` command, check out the newly added [Use the Watch Command](/docs/core-development-concepts/basics/watch-command) guide.

</Alert>

<Alert type="warning" title="Using Windows and Git Bash?">

If on Windows and using [Git Bash](https://www.atlassian.com/git/tutorials/git-bash), please note that you'll have to update it to at least to version <code>2.27.0</code>, as this version introduces the support for pseudo consoles (you will be asked whether you want this enabled in the installation process). Otherwise, upon starting the <code>webiny watch</code> command, you might be receiving broken output in your terminal.

</Alert>

### Introduced New `webiny workspaces tree` Command ([#1540](https://github.com/webiny/webiny-js/pull/1540))

The new `webiny workspaces tree` command gives you an overview of all packages that exist within your Webiny project and how they depend on each other.

![Webiny CLI - The New webiny workspaces tree Command](./tree-command.png)

For additional information and usage examples, simply type the following command in your terminal of choice:

```bash
yarn webiny ws tree --help
```

## Headless CMS

### CMS Permissions ([#1562](https://github.com/webiny/webiny-js/pull/1562))

This release introduces an upgrade to CMS permissions. It includes changes in permission editor UI and permission data structure. The goal is to improve the process of defining fine-grained permissions and make them easier to grasp.

Now you have to define access to specific content model groups first, and based on that, the list of available content models gets updated. The navigation in the Admin Area is also improved by introducing this configuration flow.

![Headless CMS - permission selector UI](./headless-cms-permission-ui-1.png)

![Headless CMS - permission selector UI](./headless-cms-permission-ui-2.png)

### Rich Text Content Renderer ([#1566](https://github.com/webiny/webiny-js/pull/1566))

Up until now, you had to manually render rich text content that comes from the Headless CMS. To make it a lot easier for you, we've finally introduced a dedicated component to do the heavy lifting. For details, see the [how-to guide on rendering the rich text content](/docs/headless-cms/extending/render-rich-text-content).

### Filtering by Reference Field Id ([#1567](https://github.com/webiny/webiny-js/pull/1567))

We added a filtering option, into our generated GraphQL Schema, to filter CMS entries by a reference field id.

For example, you can find all the articles that are connected to an author, or a list of authors:

```graphql
{
  listArticles(
    where: {
      author: "author-id"
      # OR for multiple authors
      author_in: ["author-id", "another-author-id"]
    }
  ) {
    data {
      id
    }
  }
}
```

You can also filter out all the articles that contain an author, or a list of authors:

```graphql
{
  listArticles(
    where: {
      author_not: "author-id"
      # OR for multiple authors
      author_not_in: ["author-id", "another-author-id"]
    }
  ) {
    data {
      id
    }
  }
}
```

## Page Builder

### New Pre-rendering Related Fixes ([#1569](https://github.com/webiny/webiny-js/pull/1569))

Two pre-rendering related issues were fixed in this release:

1. previewing and pre-rendering pages on non-default locales and tenants now works as expected
2. rendering not-found page also now works as expected

Note that, if upgrading to version `5.5.0`, there are a couple of [manual steps](/docs/release-notes/5.5.0/upgrade-guide#additional-manual-upgrade-steps) that need to be done in order for these fixes to be fully applied.

## Other

### Project Organization Changes ([#1540](https://github.com/webiny/webiny-js/pull/1540))

This release introduces a couple of changes on a project level. Most of these are required by the newly introduced `watch` command.

<Alert type="info" >

All the changes can be automatically applied in your existing Webiny project via the new `webiny upgrade` command. Please take a look at the [upgrade guide](/docs/release-notes/5.5.0/upgrade-guide) for more information.

</Alert>

#### 1. `.pulumi` Folders Moved To Project Root

By default, the Pulumi cloud infrastructure [state files](/docs/infrastructure/pulumi-iac/iac-with-pulumi#state-files) are stored locally - in `.pulumi` folders located in your project applications folders:

```bash
api/.pulumi
apps/admin/.pulumi
apps/website/.pulumi
```

Because these files change on every deploy, and that interferes with the new `webiny watch` command, we've been forced to move these folders outside of project application folders. So, all of your locally stored cloud infrastructure state files are now located in your project root, inside the new `.pulumi` folder:

```bash
.pulumi/api/.pulumi
.pulumi/apps/admin/.pulumi
.pulumi/apps/website/.pulumi
```

<Alert type="danger" title="CI/CD workflows">

We've seen some users storing the `.pulumi` folders in an internal Amazon S3 bucket in their CI/CD workflows. So, if you're using this approach, make sure to take the new location of the `.pulumi` folders into consideration and adjust your CI/CD logic accordingly.

</Alert>

<Alert type="warning">

As noted in the [Overview](/docs/release-notes/upgrade-webiny#-precaution-measures) section, please test your changes in pre-production environments first.

</Alert>

<Alert type="info">

If you need additional help or information about this, feel free to contact us over [Slack](https://www.webiny.com/slack).

</Alert>

#### 2. `webiny.root.js` Renamed To `webiny.project.js`

The `webiny.root.js` file, located in your project root, has been renamed to `webiny.project.js`.

#### 3. `webiny.application.js` Files

Every project application now has its own `webiny.application.js` file. Think of it as a simple manifest file, which contains project application's ID, name, and description. Optionally, it can also include additional arbitrary properties, if need be.

<Alert type="info">

Learn more about project applications in the [Project Applications](/docs/core-development-concepts/project-organization/project-applications) guide.

</Alert>

#### 4. API packages - Added `watch` Scripts and Commands

Every package located within the `api` project application folder received a `watch` script in its `package.json` file and a `watch` command in its `webiny.config.ts` file.

#### 5. Admin Area / Website - Added `watch` Scripts

The **Admin Area** and **Website** React applications also received a `watch` script in their respective `package.json` files. It is a copy of the existing `start` script.

#### 6. Custom Packages - New `build` and `watch` Scripts

Every custom package in your project now uses the new `build` and `watch` scripts, located in its `package.json` file.

<Alert type="info">

Custom packages are, for example, packages created with the `webiny scaffold` command.

</Alert>

The new `scripts` section should look like the following:

```json package.json
(...)
  "scripts": {
    "build": "yarn webiny run build",
    "watch": "yarn webiny run watch"
  }
(...)
```

In order for these to work, the `@webiny/cli` and `@webiny/project-utils` were added as development dependencies within the `devDependencies` property. Finally, the `webiny.config.ts` file was created, which defines the actual `build` and `watch` commands.

<Alert type="info">

Check this [example package](https://github.com/webiny/webiny-js/blob/watch/packages/cli-plugin-scaffold-graphql-service/template) to see updated [`package.json`](https://github.com/webiny/webiny-js/blob/watch/packages/cli-plugin-scaffold-graphql-service/template/package.json) and [`webiny.config.ts`](https://github.com/webiny/webiny-js/blob/watch/packages/cli-plugin-scaffold-graphql-service/template/webiny.config.ts) files.

</Alert>
