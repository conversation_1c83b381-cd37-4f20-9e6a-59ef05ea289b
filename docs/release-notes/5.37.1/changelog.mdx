---
id: changelog
title: Webiny 5.37.1 Changelog
description: See what's new in Webiny version 5.37.1.
---

import pbDeletingPageRevision from "./assets/pb-deleting-page-revision.png";

<GithubRelease version={"5.37.1"} />

## Page Builder

### Fixed Deletion and Unpublishing of Pages ([#3438](https://github.com/webiny/webiny-js/pull/3438))

Prior to this release, when deleting a page, it would not be fully deleted. More specifically, despite the fact that the page would not be visible in the Admin app, it would still be visible on the public website.

This has now been addressed. When deleting a page, it will be fully deleted, meaning the page will no longer be visible on the public website.

### Fixed Listing of Pages in multi-tenant Webiny projects ([#3467](https://github.com/webiny/webiny-js/pull/3467))

In a multi-tenant Webiny project, when switching between tenants in the Admin app, every now and then users might be able to see pages from other tenants. Essentially, this was happening because of a backend GraphQL schema caching-related issue.

The issue has been resolved. The pages will now be properly filtered based on the current tenant.

### Fixed Deletion of Page Revisions ([#3468](https://github.com/webiny/webiny-js/pull/3468))

When deleting a page revision other than the first one, the page would completely disappear from the list of pages in the Admin app.

<Image src={pbDeletingPageRevision} alt={"Deleting a Non-First Page Revision"} />

With this release, no matter which page revision you delete, the page will remain visible in the list of pages in the Admin app. Only once the page is completely deleted, with all of its revisions, will it disappear from the list of pages.

## Admin

### Optimized Rendering of Large Data Lists ([#3439](https://github.com/webiny/webiny-js/pull/3439))

We've received a couple of reports of the Admin app becoming slow or even unresponsive when rendering large data sets, for example lists of Page Builder pages or list of Headless CMS content entries.

With this release, we've optimized the rendering of large data sets, which should now be more performant and should not cause the Admin app to become unresponsive.

## Deployments

### Fixed Deployment of the Website App With an Existing VPC ([#3456](https://github.com/webiny/webiny-js/pull/3456))

When deploying a Webiny project into [an existing VPC](/docs/enterprise/use-existing-amazon-vpc) and with [multi-tenancy](/docs/enterprise/multi-tenancy) enabled, the deployment of the Website app would fail. More specifically, the deployment of the `website-router-origin-request` Lambda@Edge function would fail, because its IAM role was missing the base [`AWSLambdaVPCAccessExecutionRole`](https://docs.aws.amazon.com/aws-managed-policy/latest/reference/AWSLambdaVPCAccessExecutionRole.html) AWS managed policy attached to it.

This has now been addressed.

### 5.37.0 Migration Improvement ([#3469](https://github.com/webiny/webiny-js/pull/3469))

We've received a couple of reports of 5.37.0 migration sometimes failing for users using the [Amazon DynamoDB + Amazon ElasticSearch database setup](/docs/architecture/introduction#different-database-setups). Essentially, upon ElasticSearch index creation, the migration would fail with the `resource_already_exists_exception` error being thrown, and this was happening despite the fact that the existence of the index was properly checked prior to its creation.

In order to try to resolve this issue, we've added more robust checks for the existence of the index.

<Alert type="info">

ElasticSearch throws the `resource_already_exists_exception` exception when trying to create an index that already exists.

</Alert>
