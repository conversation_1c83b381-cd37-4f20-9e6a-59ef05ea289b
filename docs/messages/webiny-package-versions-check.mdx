---
id: 1ec7505b
title: Webiny Package Versions Check
---

import packageVersionsCheck from "./assets/webiny-package-versions-check.png";

Each time you run the Webiny CLI, it verifies that all `@webiny/*` NPM packages in your project are using the same version. This consistency is crucial because Webiny packages are tightly integrated, and version mismatches can cause unexpected issues.

In case you've encountered a version mismatch, the CLI will display a warning message similar to the one below:

<Image src={packageVersionsCheck}/>

To fix this, in your project, simply find all `package.json` files that include `@webiny/*` packages and ensure they all have the same version. 

Another option is to run the [`yarn why`](https://yarnpkg.com/cli/why) command to check where the different package versions are coming from. For example:

```bash
yarh why @webiny/utils
```

The benefit of using this command is that it will show you where the package is being used in your project. This way, you immediately know where to look for the version mismatch.
