---
id: 47345083
title: Could not assign required environment variables. API project application's stack output could not be retrieved.
---

import stackOutputSuccess from "./assets/stack-output-success.png";

Looks like you wanted to start the **Admin** or **Website** app locally via the [`webiny watch`](/docs/core-development-concepts/basics/watch-command) command. But, upon doing that, the required environment variables that define values like the GraphQL API URL, or Amazon Cognito User Pool Client ID, could not be assigned.

Because of this, your locally running app will not be able to communicate with Webiny's backend services like its GraphQL API, and you will see errors in the console.

Usually, this happens because the stack output from the **API** project application deployment could not be retrieved. This can happen if the **API** project application was not deployed, or if the deployment failed.

To resolve this, make sure the **API** project application is deployed successfully and that its stack output can be retrieved. To quickly test this, you can run the following command:

```bash
yarn webiny output api --env dev
```

If the command is successful, you should see the stack output printed to the console, which will look similar to the following:

<Image src={stackOutputSuccess} alt="Successfully retrieved stack output." />

If the command fails, try redeploying the **API** project application:

```bash
yarn webiny deploy api --env dev --deployment-logs
```

<Alert>

  The `--deployment-logs` flag will output the deployment logs to the console, which can help you identify the issue if the deployment fails.

</Alert>

Redeploying the **API** project application should resolve the issue, and you should be able to start the **Admin** or **Website** app locally without any problems. Still, if you encounter any issues, feel free to give us a shout in our [community Slack](https://www.webiny.com/slack) channel, and we'll be happy to help you out.
