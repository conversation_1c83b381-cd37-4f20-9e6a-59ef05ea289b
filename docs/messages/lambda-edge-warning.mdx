---
id: aafea9f7
title: Lambda@Edge function was not automatically deleted. Make sure to delete it manually.
description: Learn about the nuances of Lambda@Edge function deletion.
---

When Webiny's [multi-tenancy](/docs/enterprise/multi-tenancy) feature is enabled, as part of the **Website** project application, Webiny deploys an additional [Lambda@Edge](https://aws.amazon.com/lambda/edge/) function, which is used for additional multi-tenancy-related routing purposes.

But note that this function is not automatically destroyed when destroying the project application. You must destroy it manually, either via the [AWS Console](https://aws.amazon.com/console/), or via the [AWS CLI](https://aws.amazon.com/cli/).

This is because of an AWS limitation, which does not allow the immediate deletion of Lambda@Edge functions. Trying to immediately delete the function will result in an error, which is why Webiny does not attempt to delete it automatically.

<Alert type={"info"}>

Note that Lambda@Edge functions are always deployed into `us-east-1` region. Make sure it is selected in the AWS Console, or via the AWS CLI, before proceeding with the deletion.

</Alert>

<Alert type={"info"}>

Check the [Deleting Lambda@Edge functions and replicas](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/lambda-edge-delete-replicas.html) article for more information on how to delete Lambda@Edge functions.

</Alert>
