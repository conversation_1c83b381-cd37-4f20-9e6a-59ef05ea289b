{"private": true, "version": "1.0.0", "workspaces": ["./generator"], "scripts": {"dev": "./scripts/dev.sh", "generate": "node generateDocs.js", "build": "yarn generate && next build", "build:next": "next build", "dev:next": "next dev", "start": "next start", "export": "yarn build && next export", "format": "prettier '{src,docs,scripts}/**/*.{css,js,mdx}' --write", "eslint": "eslint \"**/*.{js,jsx,ts,tsx}\" --max-warnings=0 --fix", "prepare-release-notes": "node scripts/prepareReleaseNotes.js", "lint:fix": "yarn format && yarn eslint", "get-id": "node -e 'console.log(require(\"@webiny/utils\").mdbid().slice(-8))'"}, "browserslist": {"development": ["last 2 chrome versions", "last 2 firefox versions", "last 2 edge versions"], "production": [">1%", "not op_mini all", "ie 11"]}, "dependencies": {"@babel/preset-env": "^7.18.6", "@babel/preset-react": "^7.18.6", "@babel/register": "^7.18.6", "@badrap/bar-of-progress": "^0.1.1", "@docsearch/react": "^3.1.1", "@headlessui/react": "^1.6.5", "@heroicons/react": "^1.0.6", "@juggle/resize-observer": "^3.2.0", "@mdx-js/mdx": "^1.6.22", "@mdx-js/react": "^1.6.22", "@next/bundle-analyzer": "^9.4.4", "@reach/alert": "^0.16.0", "@reach/rect": "^0.10.5", "@silvenon/remark-smartypants": "^1.0.0", "@sindresorhus/slugify": "^1.1.0", "@svgr/webpack": "^5.5.0", "@tailwindcss/typography": "0.5.2", "@webiny/react-composition": "^5.39.6", "@webiny/react-properties": "^5.39.6", "@webiny/utils": "latest", "autoprefixer": "^10.4.0", "babel-plugin-module-resolver": "^4.1.0", "brotli-size": "^4.0.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "classnames": "^2.3.1", "clean-css": "^4.2.3", "clsx": "^1.1.1", "deasync": "^0.1.20", "debounce": "^1.2.0", "dlv": "^1.1.3", "file-loader": "^6.0.0", "focus-visible": "^5.1.0", "front-matter": "^4.0.2", "fs-extra": "^9.1.0", "globby": "^11.1.0", "jsdom": "^20.0.0", "load-json-file": "^6.2.0", "lodash.memoize": "^4.1.2", "minimatch": "^3.0.4", "next": "12.3.4", "node-gyp": "^9.0.0", "p-map": "4.0.0", "p-retry": "^4.6.2", "postcss": "^8.4.5", "postcss-focus-visible": "^5.0.0", "postcss-import": "^14.0.1", "prettier": "^2.5.0", "prismjs": "^1.25.0", "prompt-sync": "^4.2.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-image-lightbox": "^5.1.4", "react-tabs": "^3.2.3", "redent": "^3.0.0", "replace-in-path": "^1.1.0", "rimraf": "^3.0.2", "semver": "^7.3.7", "simple-functional-loader": "^1.2.1", "tailwindcss": "^3.4.17", "tinytime": "^0.2.6", "titlecase": "^1.1.3", "walk": "^2.3.15", "write-json-file": "^4.3.0", "wts": "https://github.com/webiny/wts#ae82e59470d79dc448a74239bf9d2328f4b93499"}, "devDependencies": {"@types/jest": "^29.5.4", "@types/react": "^17.0.83", "@types/react-dom": "^17.0.26", "@types/semver": "^7.5.5", "@typescript-eslint/eslint-plugin": "2.x", "@typescript-eslint/parser": "2.x", "babel-eslint": "10.x", "concurrently": "^7.2.2", "eslint": "7.x", "eslint-config-react-app": "^5.2.1", "eslint-plugin-flowtype": "4.x", "eslint-plugin-import": "2.x", "eslint-plugin-jsx-a11y": "6.x", "eslint-plugin-react": "7.x", "eslint-plugin-react-hooks": "2.x", "execa": "^5.0.0", "glyphhanger": "^4.0.1", "inquirer": "^8.2.4", "jest": "^29.6.4", "ncp": "^2.0.0", "ts-jest": "^29.1.1"}, "packageManager": "yarn@3.2.0", "resolutions": {"react": "^17.0.2", "react-dom": "^17.0.2", "@types/react": "^17.0.83", "@types/react-dom": "^17.0.26"}}